<?php

namespace App\Http\Controllers\Client;

use App\Models\Post;
use App\Http\Controllers\Controller;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    public function index()
    {
        \Assets::addScriptsDirectly(['assets/js/common.min.js','assets/js/clients/datatable-report.min.js']);
        return view('clients.reports.index');
    }

    public function report()
    {
        $order = Post::query()->select('id', 'title')->get();

        return DataTables::of($order)->toJson();
    }
}
