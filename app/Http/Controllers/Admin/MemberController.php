<?php

namespace App\Http\Controllers\Admin;

use App\Enums\RoleEnum;
use App\Enums\UserLevelEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class MemberController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $tenantID = session('current_tenant_id');
            $data = User::query()
                ->whereHas('tenants', function ($query) use ($tenantID) {
                    $query->where('tenant_id', $tenantID);
                })
                ->select('id', 'username', 'phone', 'status', 'email', 'level', 'created_at', 'balance');

            return DataTables::of($data)
                ->addColumn('action', function ($row) {
                    $editUrl = route('admin.members.edit', $row->id);
                    $deleteUrl = route('admin.members.destroy', $row->id);

                    return '
                        <div class="dropdown">
                            <a href="#" role="button" id="datatable-actions' . $row->id . '" data-bs-toggle="dropdown" aria-expanded="false" class="btn-action text-primary">
                                <i class="ri-more-2-fill"></i>
                            </a>

                            <ul class="dropdown-menu" aria-labelledby="datatable-actions' . $row->id . '">
                                <li>
                                    <a class="dropdown-item view-detail" href="#" data-id="' . $row->id . '" title="Xem chi tiết">
                                        Xem chi tiết
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="' . $editUrl . '" title="Sửa">
                                        Chỉnh sửa
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item btn-delete" href="#" data-id="' . $row->id . '" data-url="' . $deleteUrl . '" title="Xóa">
                                        Xóa bỏ
                                    </a>
                                </li>
                            </ul>
                        </div>
                    ';
                })

                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('balance', function ($row) {
                    return '<span class="badge bg-pink">' . number_format($row->balance) . '</span>';
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->editColumn('level', function ($row) {
                    return $row->level->toHtml();
                })
                ->rawColumns(['action', 'status', 'level', 'balance'])
                ->make(true);
        }
        return view('admin.members.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.members.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $rq = $request->validate([
            'full_name' => 'nullable|string|max:255',
            'username' => 'required|string|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'phone' => 'nullable|string',
            'dob' => 'nullable|date',
            'avatar' => 'nullable',
            'password' => 'required|string|min:8|confirmed',
            'status' => 'required',
            'level' => 'required'
        ]);

        DB::beginTransaction();

        try {
            $user = User::create([
                'full_name' => $rq['full_name'],
                'username' => $rq['username'],
                'email' => $rq['email'],
                'password' => Hash::make($rq['password']),
                'phone' => $rq['phone'],
                'dob' => $rq['dob'],
                'avatar' => $rq['avatar'],
                'balance' => $rq['balance'] ?? 0,
                'level' => $rq['level'] ?? UserLevelEnum::MEMBER->value,
                'status' => $rq['status'] ?? UserStatusEnum::ACTIVE->value,
                'timezone' => $rq['timezone'] ?? 'UTC',
                'locale' => $rq['locale'] ?? 'vi',
            ]);

            $tenant = $request->getHost();
            $currentTenant = Tenant::query()
                ->where('domain', $tenant)
                ->first();
            $user->tenants()->attach($currentTenant->id, [
                'role' => $request->role ?? RoleEnum::USER->value,
            ]);

            DB::commit();

            return redirect()->route('admin.members.index')
                ->with('success_msg', 'Tạo mới thành viên thành công!');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error_msg', 'Lỗi khi tạo thành viên: ' . $e->getMessage());
        }

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $query = User::query()->findOrFail($id);

        return view('admin.members.edit', [
            'dataEdit' => $query,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string|int $id)
    {
        $user = User::query()->findOrFail($id);

        $rq = $request->validate([
            'full_name' => 'nullable|string|max:255',
            'username' => 'required|string|unique:users,username,' . $user->id,
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string',
            'dob' => 'nullable|date',
            'avatar' => 'nullable',
            'password' => 'nullable|string|min:8|confirmed',
            'status' => 'required',
            'level' => 'required'
        ]);

        DB::beginTransaction();

        try {
            $updateData = [
                'full_name' => $rq['full_name'],
                'username' => $rq['username'],
                'email' => $rq['email'],
                'phone' => $rq['phone'],
                'dob' => $rq['dob'],
                'status' => $rq['status'],
                'level' => $rq['level'],
                'avatar' => $rq['avatar'],
            ];

            if (!empty($rq['password'])) {
                $updateData['password'] = Hash::make($rq['password']);
            }


            $user->update($updateData);

            DB::commit();

            return redirect()->route('admin.members.index')
                ->with('success_msg', 'Cập nhật thành viên thành công!');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withInput()
                ->with('error_msg', 'Lỗi khi cập nhật thành viên: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string|int $id)
    {
        $query = User::query()->findOrFail($id);
        $query->delete();

        return redirect()->route('admin.members.index')->with('success_msg', 'Xóa thành viên thành công!');
    }


    public function showDetail(User $user)
    {
        $tenantID = session('current_tenant_id');

        if (!$user->tenants()->where('tenant_id', $tenantID)->exists()) {
            abort(403, 'Unauthorized action.');
        }

        return view('admin.members.partials.detail', [
            'user' => $user->load('tenants')
        ]);
    }
}
