<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Enums\BaseStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PackageRequest;
use App\Models\Category;
use App\Models\Package;
use App\Models\PackageList;
use App\Models\Provider;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Package::query()
                ->with(['category:id,name'])
                ->select('id', 'name', 'category_id', 'member_price','collaborator_price','agency_price', 'distributor_price','status', 'visibility', 'created_at');

            return DataTables::of($data)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.packages.edit', $row->id);
                    $deleteUrl = route('supper-admin.packages.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('name', function ($row) {
                    return '<span class="text-plain">' . $row->name . '</span>';
                })
                ->editColumn('category', function ($row) {
                    return '<span class="badge bg-primary">'.$row->category->name.'</span>' ?? 'N/A';
                })
                ->editColumn('member_price', function ($row) {
                    return '<span class="badge bg-success">'.$row->member_price.'</span>' ?? 'N/A';
                })
                ->editColumn('collaborator_price', function ($row) {
                    return '<span class="badge bg-purple">'.$row->collaborator_price.'</span>' ?? 'N/A';
                })
                ->editColumn('agency_price', function ($row) {
                    return '<span class="badge bg-primary">'.$row->agency_price.'</span>' ?? 'N/A';
                })
                ->editColumn('distributor_price', function ($row) {
                    return '<span class="badge bg-pink">'.$row->distributor_price.'</span>' ?? 'N/A';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->rawColumns(['action', 'status', 'category','name', 'member_price', 'collaborator_price', 'agency_price','distributor_price'])
                ->make(true);
        }
        return view('supper-admin.packages.index');
    }

    public function create()
    {
        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();

        $packageLists = PackageList::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        return view('supper-admin.packages.create', [
            'categories' => $categories,
            'providers' => $providers,
            'packageLists' => $packageLists
        ]);
    }

    public function store(PackageRequest $request)
    {
        $package = new Package();

        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withCreatedSuccessMessage();
    }

    public function edit(Package $package)
    {

        \Assets::addScriptsDirectly(['assets/js/packages.min.js', 'assets/libs/select2/js/select2.min.js']);
        $categories = Category::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $providers = Provider::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        $packageLists = PackageList::query()
            ->select('id', 'name', 'status')
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->get();
        return view('supper-admin.packages.edit', [
            'categories' => $categories,
            'providers' => $providers,
            'dataEdit' =>  $package,
            'packageLists' => $packageLists
        ]);
    }

    public function update(PackageRequest $request, Package $package)
    {
        $package->fill($request->validated());
        $package->refill_type = 'auto';
        $package->mode = $request->input('mode');
        $package->save();

        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withUpdatedSuccessMessage();
    }

    public function destroy(Package $package)
    {
        $package->delete();
        return $this->httpResponse()->setNextUrl(route('supper-admin.packages.index'))->withDeletedSuccessMessage();
    }
}
