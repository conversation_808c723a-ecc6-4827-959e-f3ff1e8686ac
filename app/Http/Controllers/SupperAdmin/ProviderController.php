<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Provider;
use App\Supports\ConnectApiSMM;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Yajra\DataTables\Facades\DataTables;

class ProviderController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Provider::query()
                ->select('id', 'name', 'status','balance', 'created_at');

            return DataTables::of($data)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.providers.edit', $row->id);
                    $deleteUrl = route('supper-admin.providers.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }
        return view('supper-admin.providers.index');
    }

    public function create()
    {
        return view('supper-admin.providers.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'url' => ['required', 'string', 'max:255'],
            'key' => ['required', 'string', 'max:255'],
            'exchange_rate' => ['nullable', 'numeric'],
            'status' => ['required']
        ]);

        $result = Http::asForm()->post($validated['url'], [
            'key' => $validated['key'],
            'action' => 'balance'
        ])->json();

        if (isset($result['error'])) {
            return redirect()->route('supper-admin.providers.create')
                ->with('error_msg', $result['error']);
        }

        if (!isset($result['balance']) || !isset($result['currency'])) {
            return redirect()->route('supper-admin.providers.create')
                ->with('error_msg', 'Dữ liệu trả về không hợp lệ từ API.');
        }

        $provider = new Provider();
        $provider->fill($validated);
        $provider->balance = $result['balance'];
        $provider->currency = $result['currency'];
        $provider->save();

        return redirect()->route('supper-admin.providers.index')
            ->with('success_msg', 'Kết nối và lưu thành công.');
    }

    public function edit(Provider $provider)
    {
        return view('supper-admin.providers.edit', [
            'dataEdit' =>  $provider
        ]);
    }

    public function update(Request $request, Provider $provider)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'url' => ['required', 'string', 'max:255'],
            'key' => ['required', 'string', 'max:255'],
            'exchange_rate' => ['nullable', 'numeric'],
            'status' => ['required']
        ]);

        $result = Http::asForm()->post($validated['url'], [
            'key' => $validated['key'],
            'action' => 'balance'
        ])->json();

        if (isset($result['error'])) {
            return redirect()->route('supper-admin.providers.edit', $provider->id)
                ->with('error_msg', $result['error']);
        }

        if (!isset($result['balance']) || !isset($result['currency'])) {
            return redirect()->route('supper-admin.providers.edit', $provider->id)
                ->with('error_msg', 'Dữ liệu trả về không hợp lệ từ API.');
        }

        $provider->fill($validated);
        $provider->balance = $result['balance'];
        $provider->currency = $result['currency'];
        $provider->save();

        return redirect()->route('supper-admin.providers.index')
            ->with('success_msg', 'Cập nhật thành công.');
    }

    public function destroy(Provider $provider)
    {
        $provider->delete();
        return redirect()->route('supper-admin.providers.index')
            ->with('success_msg', 'Xóa nhà cung cấp thành công.');
    }

    public function getAllServices(Request $request){
        $payload = $request->validate([
            'provider_id' => 'required|integer',
        ]);
        $app = new ConnectApiSMM();
        $provider = Provider::query()->where('id', $payload['provider_id'])->firstOrFail();
        $service = $app->services($provider->toArray());

        $currentPage = Paginator::resolveCurrentPage();
        $perPage = $request->integer('per_page', 100);
        $collection = collect($service);

        $data = $collection
            ->when($request->query('q'), function (Collection $collection, $search) {
                return $collection->filter(function($item) use ($search) {
                    $searchLower = strtolower($search);
                    return str_contains(strtolower($item['name']), $searchLower) ||
                        str_contains(strtolower($item['service'] ?? ''), $searchLower);
                });
            })
            ->slice(($currentPage - 1) * $perPage, $perPage)
            ->all();

        return $this
            ->httpResponse()
            ->setData((new LengthAwarePaginator($data, count($collection), $perPage))
                ->setPath(route('supper-admin.providers.get-services'))
                ->appends($request->except('page')));
    }
}

