<?php

namespace App\Http\Controllers\SupperAdmin;

use App\Http\Controllers\Controller;
use App\Models\PackageList;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class PackageListController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = PackageList::query()
                ->select('id', 'name','key', 'status', 'created_at')->orderBy('created_at', 'desc');

            return DataTables::of($data)
                ->addColumn('action', function ($row) {
                    $editUrl = route('supper-admin.package-lists.edit', $row->id);
                    $deleteUrl = route('supper-admin.package-lists.destroy', $row->id);

                    return '
                        <a href="' . $editUrl . '" class="btn-action text-primary me-1" title="Sửa">
                            <i class="ri-edit-2-line"></i>
                        </a>
                        <button type="button" class="btn-action text-danger btn-delete"
                            data-id="' . $row->id . '"
                            data-url="' . $deleteUrl . '"
                            title="Xóa">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                    ';
                })
                ->editColumn('key', function ($row) {
                    return '<span class="badge bg-purple">' . $row->key . '</span>';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at->format('d/m/Y');
                })
                ->editColumn('status', function ($row) {
                    return $row->status->toHtml();
                })
                ->rawColumns(['action', 'status','key'])
                ->make(true);
        }
        return view('supper-admin.package-lists.index');
    }

    public function create()
    {
        return view('supper-admin.package-lists.create');
    }

    public function store(Request $request)
    {
        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:package_lists,key',
            'status' => 'required',
        ]);

        PackageList::query()->create($payload);

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withCreatedSuccessMessage();
    }

    public function edit(string|int $id)
    {
        $packageList = PackageList::query()->findOrFail($id);
        return view('supper-admin.package-lists.edit', [
            'dataEdit' => $packageList,
        ]);
    }

    public function update(string|int $id, Request $request)
    {
        $packageList = PackageList::query()->findOrFail($id);

        $payload = $request->validate([
            'name' => 'required|string|max:255',
            'key' => 'required|string|max:255|unique:package_lists,key,' . $id,
            'status' => 'required',
        ]);

        $packageList->update($payload);

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withUpdatedSuccessMessage();
    }
    public function destroy(string|int $id)
    {
        $packageList = PackageList::query()->findOrFail($id);
        $packageList->delete();

        return $this->httpResponse()->setNextUrl(route('supper-admin.package-lists.index'))->withDeletedSuccessMessage();
    }
}
