<?php

namespace App\Http\Controllers;

use App\Enums\BaseStatusEnum;
use App\Models\Faq;
use App\Models\Notification;
use App\Models\Post;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $tenantId = session('current_tenant_id');
        $faqs = Faq::query()
            ->where('tenant_id', $tenantId)
            ->get();

        $notifications = Notification::query()->where('tenant_id', $tenantId)->get();
        $posts = Post::query()
            ->where('tenant_id', $tenantId)
            ->where('status', BaseStatusEnum::PUBLISHED->value)
            ->orderByDesc('pin')
            ->orderByDesc('created_at')
            ->get();
        return view('clients.dashboard.index', [
            'faqs' => $faqs,
            'notifications' => $notifications,
            'posts' => $posts,
        ]);
    }
}
