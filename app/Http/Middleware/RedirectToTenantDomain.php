<?php

namespace App\Http\Middleware;

use App\Models\Tenant;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectToTenantDomain
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $domain = $request->getHost();
        $tenant = Tenant::query()->where('domain', $domain)->first();

        if (!$tenant) {
            abort(404, 'Tenant không tồn tại');
        }

        if (Auth::check() && !Auth::user()->tenants()->where('tenant_id', $tenant->id)->exists()) {
            Auth::logout();
            return redirect()->route('login')->with('error', 'Bạn không có quyền truy cập tenant này!');
        }

        app()->instance('currentTenant', $tenant);
        return $next($request);

    }
}
