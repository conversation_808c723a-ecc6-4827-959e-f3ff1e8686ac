@charset "UTF-8";
/* vietnamese */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjfqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjvqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hk1qnyudypw.woff2") format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjfqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjvqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hk1qnyudypw.woff2") format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjfqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjvqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hk1qnyudypw.woff2") format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjfqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjvqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hk1qnyudypw.woff2") format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* vietnamese */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjfqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hjvqnyudyp7bh.woff2") format("woff2");
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("/fonts/6f16cdd6fd/squicksandv366xktdszam9ie8kbpra-hk1qnyudypw.woff2") format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/*********************variables-custom dark Mode***************************/
[data-theme=default] {
  --vz-font-family-primary: "Quicksand", sans-serif;
  --vz-font-family-secondary: "Quicksand", sans-serif;
  --vz-grid-gutter-width: 1.5rem;
  --vz-headings-font-weight: 500;
  --vz-element-shadow: none;
  --vz-card-border-width-custom: 0;
  --vz-card-header-border-width: 1px;
  --vz-card-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  --vz-headings-font-family: var(--vz-font-family-secondary);
  --vz-vertical-menu-item-font-size: 0.875rem;
  --vz-vertical-menu-sub-item-font-size: 0.813rem;
  --vz-vertical-menu-bg: #fff;
  --vz-vertical-menu-border: #fff;
  --vz-vertical-menu-item-color: rgb(63.6925, 69.8, 75.9075);
  --vz-vertical-menu-item-bg: rgba(var(--vz-primary-rgb), 0.15);
  --vz-vertical-menu-item-hover-color: var(--vz-primary);
  --vz-vertical-menu-item-active-color: var(--vz-primary);
  --vz-vertical-menu-item-active-bg: rgba(var(--vz-primary-rgb), 0.15);
  --vz-vertical-menu-sub-item-color: rgb(63.6925, 69.8, 75.9075);
  --vz-vertical-menu-sub-item-hover-color: var(--vz-primary);
  --vz-vertical-menu-sub-item-active-color: var(--vz-primary);
  --vz-vertical-menu-title-color: #919da9;
  --vz-vertical-menu-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-vertical-menu-dropdown-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-sidebar-user-bg: #f3f6f9;
  --vz-sidebar-user-name-text: rgb(49.73125, 54.5, 59.26875);
  --vz-sidebar-user-name-sub-text: rgb(123.972972973, 127.2486486486, 143.627027027);
  --vz-header-bg: #fff;
  --vz-header-border: #e9ebec;
  --vz-header-item-color: #495057;
  --vz-header-item-bg: rgba(173, 181, 189, 0.12);
  --vz-header-item-sub-color: #878a99;
  --vz-topbar-search-bg: #f3f3f9;
  --vz-topbar-user-bg: #f3f3f9;
  --vz-topbar-search-color: #495057;
  --vz-page-title-box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  --vz-page-title-border: none;
  --vz-footer-bg: #fff;
  --vz-footer-color: #98a6ad;
  --vz-boxed-body-bg: #e5e5f2;
  --vz-timeline-color: var(--vz-secondary-bg);
  --vz-chat-primary-bg: var(--vz-light);
  --vz-chat-secondary-bg: rgba(var(--vz-success-rgb), 0.15);
  --vz-chat-secondary-color: var(--vz-success);
  --vz-font-6: 0.375rem;
  --vz-font-7: 0.4375rem;
  --vz-font-8: 0.5rem;
  --vz-font-9: 0.5625rem;
  --vz-font-10: 0.625rem;
  --vz-font-11: 0.6875rem;
  --vz-font-12: 0.75rem;
  --vz-font-13: 0.8125rem;
  --vz-font-14: 0.875rem;
  --vz-font-base: 0.875rem;
  --vz-font-15: 0.9375rem;
  --vz-font-16: 1rem;
  --vz-font-17: 1.0625rem;
  --vz-font-18: 1.125rem;
  --vz-font-19: 1.1875rem;
  --vz-font-20: 1.25rem;
  --vz-font-21: 1.3125rem;
  --vz-font-22: 1.375rem;
  --vz-font-23: 0.8125rem;
  --vz-font-24: 1.5rem;
  --vz-font-36: 2.25rem;
  --vz-font-48: 3rem;
  --vz-font-weight-light: 300;
  --vz-font-weight-normal: 400;
  --vz-font-weight-medium: 500;
  --vz-font-weight-semibold: 600;
  --vz-font-weight-bold: 700;
}
[data-theme=default]:root {
  --vz-vertical-menu-item-font-family: var(--vz-font-family-primary);
  --vz-vertical-menu-sub-item-font-family: var(--vz-font-family-secondary);
}
[data-theme=default][data-sidebar=dark] {
  --vz-vertical-menu-bg: var(--vz-primary);
  --vz-vertical-menu-border: var(--vz-primary);
  --vz-vertical-menu-item-color: #abb9e8;
  --vz-vertical-menu-item-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-item-hover-color: #fff;
  --vz-vertical-menu-item-active-color: #fff;
  --vz-vertical-menu-item-active-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-sub-item-color: #abb9e8;
  --vz-vertical-menu-sub-item-hover-color: #fff;
  --vz-vertical-menu-sub-item-active-color: #fff;
  --vz-vertical-menu-title-color: #838fb9;
  --vz-twocolumn-menu-iconview-bg: rgb(43.4611111111, 112.5777777778, 240.3388888889);
  --vz-vertical-menu-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-vertical-menu-dropdown-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-sidebar-user-bg: rgba(255, 255, 255, 0.08);
  --vz-sidebar-user-name-text: #fff;
  --vz-sidebar-user-name-sub-text: rgb(184.3, 207.4, 250.1);
}
[data-theme=default][data-topbar=dark] {
  --vz-header-bg: var(--vz-primary);
  --vz-header-border: var(--vz-primary);
  --vz-header-item-color: rgba(255, 255, 255, 0.85);
  --vz-header-item-bg: #495c99;
  --vz-header-item-sub-color: #b0c4d9;
  --vz-topbar-user-bg: #52639c;
  --vz-topbar-search-bg: rgba(255, 255, 255, 0.05);
  --vz-topbar-search-color: #fff;
}
[data-theme=default][data-sidebar=gradient] {
  --vz-vertical-menu-bg: linear-gradient(to right, var(--vz-primary), var(--vz-success));
  --vz-vertical-menu-border: var(--vz-success);
  --vz-twocolumn-menu-iconview-bg: var(--vz-primary);
}
[data-theme=default][data-sidebar=gradient-2] {
  --vz-vertical-menu-bg: linear-gradient(to right, var(--vz-info), var(--vz-secondary));
  --vz-vertical-menu-border: var(--vz-secondary);
  --vz-twocolumn-menu-iconview-bg: var(--vz-info);
}
[data-theme=default][data-sidebar=gradient-3] {
  --vz-vertical-menu-bg: linear-gradient(to right, var(--vz-info), var(--vz-success));
  --vz-vertical-menu-border: var(--vz-success);
  --vz-twocolumn-menu-iconview-bg: var(--vz-info);
}
[data-theme=default][data-sidebar=gradient-4] {
  --vz-vertical-menu-bg: linear-gradient(to right, #1a1d21, var(--vz-primary));
  --vz-vertical-menu-border: var(--vz-primary);
  --vz-twocolumn-menu-iconview-bg: #1a1d21;
}
[data-theme=default] .card-radio .form-check-label[for=themeColor-01] {
  background-color: #3577f1;
}
[data-theme=default] .card-radio .form-check-label[for=themeColor-02] {
  background-color: rgb(6, 112.2, 81);
}
[data-theme=default] .card-radio .form-check-label[for=themeColor-03] {
  background-color: rgb(80.8, 71.2, 163.2);
}
[data-theme=default] .card-radio .form-check-label[for=themeColor-04] {
  background-color: rgb(42.4, 95.2, 192.8);
}

#page-topbar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1002;
  background-color: var(--vz-header-bg);
  transition: all 0.1s ease-out;
  border-bottom: 1px solid var(--vz-header-border);
}
#page-topbar.topbar-shadow {
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
}
@media (min-width: 768px) {
  #page-topbar {
    left: var(--vz-vertical-menu-width);
  }
}

.navbar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  height: 70px;
  padding: 0 var(--vz-grid-gutter-width) 0 calc(var(--vz-grid-gutter-width) * 0.5);
}
@media (max-width: 767.98px) {
  .navbar-header {
    padding: 0 calc(var(--vz-grid-gutter-width) * 0.5) 0 calc(var(--vz-grid-gutter-width) * 0.5);
  }
}
.navbar-header .topbar-head-dropdown .dropdown-menu.show {
  top: 13px !important;
}
.navbar-header .topbar-head-dropdown .notification-actions {
  display: none;
  position: absolute;
  background-color: var(--vz-secondary-bg);
  left: 0;
  right: 0;
  bottom: 0;
  padding: 7px;
  border-top: 1px solid var(--vz-border-color);
}
.navbar-header .btn-topbar {
  height: 42px;
  width: 42px;
  color: var(--vz-header-item-sub-color);
  transition: all 0.5s ease;
}
.navbar-header .btn-topbar:hover, .navbar-header .btn-topbar:focus, .navbar-header .btn-topbar:active, .navbar-header .btn-topbar.active {
  background-color: var(--vz-header-item-bg);
}
@media (max-width: 360px) {
  .navbar-header .btn-topbar {
    height: 36px;
    width: 36px;
  }
}
.navbar-header .user-name-sub-text {
  color: var(--vz-header-item-sub-color);
}
.navbar-header .user-name-text {
  color: var(--vz-header-item-color);
}

/* Search */
.app-search {
  padding: calc(32px * 0.5) 0;
}
.app-search .form-control {
  border: none;
  height: 38px;
  padding-left: 40px;
  padding-right: 30px;
  background-color: var(--vz-topbar-search-bg);
  color: var(--vz-topbar-search-color);
  box-shadow: none;
}
.app-search span.search-widget-icon {
  position: absolute;
  z-index: 10;
  font-size: 18px;
  line-height: 38px;
  left: 13px;
  top: 0;
  color: var(--vz-header-item-sub-color);
}
.app-search .search-widget-icon-close {
  right: 7px;
  left: auto !important;
}
@media (max-width: 1023.99px) {
  .app-search {
    padding-left: calc(var(--vz-grid-gutter-width) * 0.5);
  }
}
.app-search ::-moz-placeholder {
  color: var(--vz-header-item-sub-color);
}
.app-search ::placeholder {
  color: var(--vz-header-item-sub-color);
}

.megamenu-list li {
  position: relative;
  padding: 5px 0px;
}
.megamenu-list li a {
  color: var(--vz-body-color);
}

@media (max-width: 767.98px) {
  .logo span.logo-lg {
    display: none;
  }
  .logo span.logo-sm {
    display: inline-block;
  }
}
.header-item {
  height: 70px;
  display: flex;
  align-items: center;
}

.header-profile-user {
  height: 32px;
  width: 32px;
}

.topbar-badge-sm {
  right: 0;
  top: 7px !important;
}

.topbar-badge {
  right: -9px;
  top: 4px !important;
}

@media (min-width: 768px) {
  .topbar-user {
    background-color: var(--vz-topbar-user-bg);
  }
}
.topbar-user .dropdown-menu {
  top: 6px !important;
}

.notification-item {
  padding: 0.75rem 1rem;
  white-space: inherit;
  position: relative;
}
.notification-item .form-check-input {
  position: relative;
  z-index: 2;
}

.dropdown-icon-item {
  display: block;
  border-radius: 3px;
  line-height: 34px;
  text-align: center;
  padding: 15px 0 9px;
  border: 1px solid transparent;
  color: var(--vz-dropdown-link-color);
}
.dropdown-icon-item img {
  height: 24px;
}
.dropdown-icon-item span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dropdown-icon-item:hover {
  background-color: var(--vz-dropdown-link-hover-bg);
}

.fullscreen-enable [data-toggle=fullscreen] .bx-fullscreen::before {
  content: "\eacb";
}

[data-bs-theme=dark] .light-dark-mode .bx-moon::before {
  content: "\ec34";
}

[data-topbar=dark] .logo-dark {
  display: none;
}
[data-topbar=dark] .logo-light {
  display: inline-block;
}

[data-bs-theme=dark][data-topbar=light] .logo-dark {
  display: none;
}
[data-bs-theme=dark][data-topbar=light] .logo-light {
  display: inline-block;
}

@media (max-width: 600px) {
  .navbar-header .dropdown {
    position: static;
  }
  .navbar-header .dropdown .dropdown-menu {
    width: 100%;
  }
}
@media (max-width: 767.98px) {
  #search-dropdown-reponsive {
    top: 54px !important;
  }
}
@media (min-width: 1024.1px) {
  [data-layout=vertical][data-layout-style=detached] #page-topbar {
    left: 0 !important;
    box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  }
  [data-layout=vertical][data-layout-style=detached] .horizontal-logo {
    display: inline-block;
    padding-left: 0;
  }
  [data-layout=vertical][data-layout-style=detached] .topnav-hamburger {
    visibility: hidden;
  }
  [data-layout=vertical][data-layout-style=detached] .layout-width {
    max-width: 95%;
    margin: 0 auto;
  }
  [data-layout=vertical][data-layout-style=detached]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .navbar-brand-box {
    background-color: transparent !important;
    position: relative;
    width: auto;
    text-align: left;
  }
  [data-layout=vertical][data-layout-style=detached]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .navbar-brand-box .logo-sm {
    display: none;
  }
  [data-layout=vertical][data-layout-style=detached]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .navbar-brand-box .logo-lg {
    display: block;
  }
}
[data-layout=vertical][data-layout-style=detached][data-topbar=dark] .horizontal-logo .logo-dark {
  display: none;
}
[data-layout=vertical][data-layout-style=detached][data-topbar=dark] .horizontal-logo .logo-light {
  display: block;
}

[data-layout=horizontal] #page-topbar {
  left: 0;
  border-bottom: 1px solid var(--vz-header-border);
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal] #page-topbar.topbar-shadow {
    box-shadow: none;
  }
}
[data-layout=horizontal] .page-content {
  padding: calc(45px + var(--vz-grid-gutter-width)) calc(var(--vz-grid-gutter-width) * 0.5) 60px calc(var(--vz-grid-gutter-width) * 0.5);
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal] .page-content {
    margin-top: 70px;
  }
}
@media (max-width: 1024.1px) {
  [data-layout=horizontal] .page-content {
    padding: calc(45px + var(--vz-grid-gutter-width) * 1.8) calc(var(--vz-grid-gutter-width) * 0.5) 60px calc(var(--vz-grid-gutter-width) * 0.5);
  }
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal][data-layout-width=boxed] .page-content {
    min-height: calc(100vh - 130px);
  }
}

@media (min-width: 768px) {
  [data-layout=vertical]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) #page-topbar {
    left: var(--vz-vertical-menu-width-sm);
  }
}
@media (min-width: 768px) {
  [data-layout=vertical][data-sidebar-size=md] #page-topbar {
    left: var(--vz-vertical-menu-width-md);
  }
}

@media (min-width: 768px) {
  [data-layout=twocolumn] #page-topbar {
    left: calc(70px + 220px);
  }
}
[data-layout=twocolumn] .horizontal-logo {
  display: none;
}

.page-title-box {
  padding: 10px var(--vz-grid-gutter-width);
  background-color: var(--vz-secondary-bg);
  box-shadow: var(--vz-page-title-box-shadow);
  border-bottom: 1px solid var(--vz-page-title-border);
  margin: -23px calc(var(--vz-grid-gutter-width) * -1) var(--vz-grid-gutter-width) calc(var(--vz-grid-gutter-width) * -1);
}
.page-title-box .breadcrumb {
  background-color: transparent;
  padding: 0;
}
.page-title-box h4 {
  font-weight: 700;
  font-size: 15px !important;
  text-transform: uppercase;
}

[data-layout=horizontal] .page-title-box {
  padding: 1.2rem 0;
  background-color: transparent !important;
  border-bottom: none;
  border-top: none;
  box-shadow: none;
  margin: 0;
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal] .page-title-box {
    margin: -19px 0 0 0;
  }
}

[data-layout=vertical][data-layout-style=detached] .page-title-box {
  padding: 1.2rem 0;
  background-color: transparent !important;
  border-bottom: none;
  border-top: none;
  box-shadow: none;
  margin: 0;
}
@media (min-width: 1024.1px) {
  [data-layout=vertical][data-layout-style=detached] .page-title-box {
    margin: -19px 0 0 0;
  }
}

.footer {
  bottom: 0;
  padding: 20px calc(var(--vz-grid-gutter-width) * 0.5);
  position: absolute;
  right: 0;
  color: var(--vz-footer-color);
  left: var(--vz-vertical-menu-width);
  height: 60px;
  background-color: var(--vz-footer-bg);
}
@media (max-width: 991.98px) {
  .footer {
    left: 0;
  }
}

[data-layout=vertical]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .footer {
  left: var(--vz-vertical-menu-width-sm);
}
@media (max-width: 767.98px) {
  [data-layout=vertical]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .footer {
    left: 0;
  }
}
[data-layout=vertical][data-sidebar-size=md] .footer {
  left: var(--vz-vertical-menu-width-md);
}
@media (max-width: 991.98px) {
  [data-layout=vertical][data-sidebar-size=md] .footer {
    left: 0;
  }
}

[data-layout=horizontal] .footer {
  left: 0 !important;
}

@media (min-width: 1024.1px) {
  [data-layout=vertical][data-layout-style=detached] .footer {
    left: 0 !important;
    background-color: transparent;
  }
}

@media (min-width: 768.1px) {
  [data-layout=twocolumn] .footer {
    left: calc(70px + 220px);
  }
}

.app-content {
  margin-left: var(--vz-vertical-menu-width);
  overflow: hidden;
}
.app-content .content {
  padding: 0 15px 10px 15px;
  margin-top: 70px;
}

.main-content {
  transition: all 0.1s ease-out;
}
@media (min-width: 768px) {
  .main-content {
    margin-left: var(--vz-vertical-menu-width);
  }
}

.page-content {
  padding: calc(70px + var(--vz-grid-gutter-width)) calc(var(--vz-grid-gutter-width) * 0.5) 60px calc(var(--vz-grid-gutter-width) * 0.5);
}

.navbar-menu {
  width: var(--vz-vertical-menu-width);
  z-index: 1002;
  background: var(--vz-vertical-menu-bg);
  border-right: 1px solid var(--vz-vertical-menu-border);
  bottom: 0;
  margin-top: 0;
  position: fixed;
  top: 0;
  box-shadow: var(--vz-vertical-menu-box-shadow);
  padding: 0 0 20px 0;
  transition: all 0.1s ease-out;
}
.navbar-menu .navbar-nav .nav-link {
  display: flex;
  align-items: center;
  padding: 0.625rem 1rem;
  color: var(--vz-vertical-menu-item-color);
  font-size: var(--vz-vertical-menu-item-font-size);
  font-family: var(--vz-vertical-menu-item-font-family);
  text-transform: capitalize;
  margin: 0.15rem 0;
}
.navbar-menu .navbar-nav .nav-link span {
  font-weight: 500;
}
.navbar-menu .navbar-nav .nav-link.active {
  color: var(--vz-vertical-menu-item-active-color);
  background-color: var(--vz-vertical-menu-item-active-bg);
}
.navbar-menu .navbar-nav .nav-link:hover {
  color: var(--vz-vertical-menu-item-hover-color);
}
.navbar-menu .navbar-nav .nav-link i {
  display: inline-block;
  min-width: 2rem;
  font-size: 18px;
  line-height: inherit;
}
.navbar-menu .navbar-nav .nav-link svg {
  margin-right: 0.665rem;
  color: var(--vz-vertical-menu-item-color);
}
.navbar-menu .navbar-nav .nav-link .badge {
  margin-left: auto;
  margin-right: -2px;
  z-index: 1;
}
.navbar-menu .navbar-nav .nav-link:hover {
  color: var(--vz-vertical-menu-item-hover-color);
}
.navbar-menu .navbar-nav .nav-link:hover .icon-dual {
  color: var(--vz-vertical-menu-item-hover-color);
  fill: rgba(53, 119, 241, 0.16);
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
  display: block;
  content: "\ea6e";
  font-family: "remixicon";
  margin-left: auto;
  transition: transform 0.2s;
  font-size: 1.05rem;
  position: absolute;
  right: 18px;
  color: inherit;
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
  color: var(--vz-vertical-menu-item-active-color);
  background-color: var(--vz-vertical-menu-item-active-bg);
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] i {
  color: var(--vz-vertical-menu-item-active-color) !important;
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
  color: var(--vz-vertical-menu-item-hover-color);
  fill: rgba(53, 119, 241, 0.16);
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
  transform: rotate(90deg);
  color: var(--vz-vertical-menu-item-active-color);
}
.navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:before {
  opacity: 1;
  background-color: var(--vz-vertical-menu-item-active-color);
}
.navbar-menu .navbar-nav .nav-link.collapsed.active::after {
  transform: rotate(90deg);
}
.navbar-menu .navbar-nav .nav-sm .nav-link {
  color: var(--vz-vertical-menu-sub-item-color);
  white-space: none;
  position: relative;
  font-size: var(--vz-vertical-menu-item-font-size);
  font-family: var(--vz-vertical-menu-sub-item-font-family);
  font-weight: 500;
  padding: 0.75rem 0 0.75rem 3.5rem;
  margin: 0.15rem 0;
}
.navbar-menu .navbar-nav .nav-sm .nav-link:hover {
  color: var(--vz-vertical-menu-sub-item-hover-color);
}
.navbar-menu .navbar-nav .nav-sm .nav-link.active {
  color: var(--vz-vertical-menu-item-active-color);
}
.navbar-menu .navbar-nav .nav-sm .nav-link.active:before {
  background-color: var(--vz-vertical-menu-item-active-color);
}
.navbar-menu .navbar-nav .nav-sm .nav-sm .nav-link {
  padding-left: 5.5rem;
  margin: 0.15rem 0;
}
.navbar-menu .navbar-nav .nav-sm .nav-sm .nav-link:before {
  height: 5px;
  width: 5px;
  left: 5px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid;
  top: 16px;
}
.navbar-menu .btn-vertical-sm-hover {
  color: var(--vz-secondary-color);
  display: none;
}

.navbar-brand-box {
  padding: 0 1.3rem;
  text-align: center;
  transition: all 0.1s ease-out;
}
@media (max-width: 767.98px) {
  .navbar-brand-box {
    display: none;
  }
}

.hamburger-icon {
  width: 20px;
  height: 14px;
  position: relative;
  cursor: pointer;
  display: inline-block;
}
.hamburger-icon span {
  background-color: var(--vz-header-item-sub-color);
  position: absolute;
  border-radius: 2px;
  transition: 0.3s cubic-bezier(0.8, 0.5, 0.2, 1.4);
  width: 100%;
  height: 2px;
  display: block;
  left: 0px;
}
.hamburger-icon span:nth-child(1) {
  top: 0;
  width: 80%;
}
.hamburger-icon span:nth-child(2) {
  top: 6px;
}
.hamburger-icon span:nth-child(3) {
  bottom: 0;
  width: 60%;
}
.vertical-menu-btn:hover .hamburger-icon:not(.open) span:nth-child(1) {
  top: -1px;
}
.vertical-menu-btn:hover .hamburger-icon:not(.open) span:nth-child(3) {
  bottom: -1px;
}
.hamburger-icon.open {
  transform: rotate(-90deg);
}
.hamburger-icon.open span:nth-child(1) {
  left: 1px;
  top: 5px;
  width: 20px;
  transform: rotate(90deg);
  transition-delay: 150ms;
}
.hamburger-icon.open span:nth-child(2) {
  left: 3px;
  top: 13px;
  width: 10px;
  transform: rotate(45deg);
  transition-delay: 50ms;
}
.hamburger-icon.open span:nth-child(3) {
  left: 9px;
  top: 13px;
  width: 10px;
  transform: rotate(-45deg);
  transition-delay: 100ms;
}

.logo {
  line-height: 70px;
}
.logo .logo-sm {
  display: none;
}

.logo-light {
  display: none;
}

.sidebar-user {
  display: none;
}

:is([data-layout=vertical], [data-layout=semibox]) .app-menu .row {
  margin: 0;
}
:is([data-layout=vertical], [data-layout=semibox]) .app-menu .row > * {
  width: 100%;
  padding: 0;
}
@media (max-width: 767.98px) {
  :is([data-layout=vertical], [data-layout=semibox]) .app-menu {
    margin-left: -100%;
    padding: 10px 0 20px 0;
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show] .app-menu .sidebar-user {
  display: block;
  background-color: var(--vz-sidebar-user-bg);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show] .app-menu .sidebar-user .sidebar-user-name-text {
  color: var(--vz-sidebar-user-name-text);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show] .app-menu .sidebar-user .sidebar-user-name-sub-text {
  color: var(--vz-sidebar-user-name-sub-text);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show] .app-menu .sidebar-user .header-profile-user {
  height: 40px;
  width: 40px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show] #page-topbar .topbar-user {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .app-menu .header-profile-user {
  height: 40px;
  width: 40px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .app-menu .sidebar-user .btn {
  padding: 9px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-user-show]:is([data-sidebar-size=sm], [data-sidebar-size=sm-hover]) .app-menu .sidebar-user .text-start {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox]) .navbar-menu .container-fluid {
  padding: 0;
}
@media (max-width: 767.98px) {
  :is([data-layout=vertical], [data-layout=semibox]) .navbar-brand-box {
    display: none;
  }
}
:is([data-layout=vertical], [data-layout=semibox]) .horizontal-logo {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .main-content {
  margin-left: var(--vz-vertical-menu-width-sm);
}
@media (max-width: 767.98px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .main-content {
    margin-left: 0;
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .logo span.logo-lg {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .logo span.logo-sm {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .btn-vertical-sm-hover {
  display: inline-block;
}
@media (min-width: 768px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .topnav-hamburger {
    display: none;
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu {
  width: var(--vz-vertical-menu-width-sm);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .btn-vertical-sm-hover {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .btn-vertical-sm-hover i.ri-record-circle-line:before {
  content: "\eb7d";
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .badge {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title {
  text-align: center;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title span {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-title i {
  color: var(--vz-vertical-menu-item-color);
  display: block;
  line-height: 36px;
  font-size: 1rem;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link span {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i {
  font-size: 22px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.las, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.lar, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link i.lab {
  font-size: 24px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link svg {
  margin-right: 0px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link:after, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .nav-link:before {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu .navbar-nav .menu-dropdown {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover {
  width: var(--vz-vertical-menu-width) !important;
}
@media (min-width: 1024.99px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .btn-vertical-sm-hover {
    display: inline-block;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .navbar-brand-box {
    text-align: left;
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .navbar-nav .menu-dropdown.show {
  display: block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i {
  font-size: 18px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.las, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.lar, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link i.lab {
  font-size: 20px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link svg {
  margin-right: 0.665rem;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link span, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link:after, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .nav-link:before {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .logo span.logo-lg {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .logo span.logo-sm {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title {
  text-align: left;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title span {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover] .navbar-menu:hover .menu-title i {
  display: none;
}
@media (min-width: 1025px) {
  :is([data-layout=vertical], [data-layout=semibox]):is([data-sidebar-size=sm-hover], [data-sidebar-size=sm-hover-active]) .navbar-header {
    padding-left: var(--vz-grid-gutter-width);
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover-active] .navbar-brand-box {
  text-align: left;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover-active] .topnav-hamburger {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm-hover-active] .btn-vertical-sm-hover {
  display: inline-block;
}
@media (min-width: 768px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] {
    min-height: 1400px;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .main-content {
    margin-left: var(--vz-vertical-menu-width-sm);
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] #page-topbar {
  z-index: calc(1002 + 1);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-brand-box {
  position: fixed;
  padding: 0;
  width: var(--vz-vertical-menu-width-sm);
  z-index: 1;
  top: 0;
  background-color: var(--vz-vertical-menu-bg);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .logo span.logo-lg {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .logo span.logo-sm {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu {
  position: absolute;
  width: var(--vz-vertical-menu-width-sm) !important;
  padding-top: 70px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .simplebar-mask,
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .simplebar-content-wrapper {
  overflow: visible !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .simplebar-scrollbar,
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .vertical-menu-btn {
  display: none !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .simplebar-offset {
  bottom: 0 !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .badge {
  display: none !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title {
  text-align: center;
  font-size: 1rem;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title span {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-title i {
  display: block;
  line-height: 36px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link span {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link i {
  font-size: 22px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link svg {
  margin-right: 0px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link:after, :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-link:before {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .menu-dropdown {
  display: none;
  height: auto !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item {
  position: relative;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover > a.menu-link {
  position: relative;
  width: calc(200px + var(--vz-vertical-menu-width-sm));
  transition: none;
  background: var(--vz-vertical-menu-bg);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover > a.menu-link .icon-dual {
  fill: rgba(255, 255, 255, 0.16);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover > a.menu-link span {
  display: inline-block;
  padding-left: 25px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover > a.menu-link:after {
  display: block;
  transform: rotate(90deg);
  color: #fff;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover .nav-link span {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-item:hover > .menu-dropdown {
  display: block;
  left: var(--vz-vertical-menu-width-sm);
  position: absolute;
  width: 200px;
  background: var(--vz-vertical-menu-bg);
  height: auto !important;
  padding: 0.5rem 0;
  border-radius: 0 0 3px 3px;
  box-shadow: var(--vz-vertical-menu-dropdown-box-shadow);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm {
  padding: 0;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover > .nav-link {
  color: var(--vz-vertical-menu-item-hover-color);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-item:hover > .nav-link:after {
  color: inherit;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .nav-link:after {
  display: block !important;
  transform: rotate(0deg) !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=sm] .navbar-menu .navbar-nav .nav-sm .menu-dropdown {
  left: 100% !important;
  top: 0;
  border-radius: 3px !important;
}
@media (min-width: 768px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .main-content {
    margin-left: var(--vz-vertical-menu-width-md);
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-brand-box {
  width: var(--vz-vertical-menu-width-md);
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu {
  width: var(--vz-vertical-menu-width-md) !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link {
  display: block;
  text-align: center;
  padding: 0.55rem 0.525rem;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link i {
  display: block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link svg {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link:before {
  display: none !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
  position: relative;
  display: inline-block;
  right: 0;
  top: 3px;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-link.menu-link[data-bs-toggle=collapse]:after {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .badge {
  display: none !important;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .navbar-nav .nav-sm {
  padding-left: 0;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .menu-title {
  text-align: center;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar-size=md] .navbar-menu .menu-title span {
  text-decoration: underline;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar=dark] .logo-dark {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar=dark] .logo-light {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar=light] .logo-dark {
  display: inline-block;
}
:is([data-layout=vertical], [data-layout=semibox])[data-sidebar=light] .logo-light {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] #layout-wrapper,
:is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .main-content {
  min-height: 100vh;
}
@media (min-width: 1024.1px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .main-content {
    position: relative;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] #layout-wrapper {
    max-width: 95%;
    margin: 0 auto;
    padding-left: var(--vz-grid-gutter-width);
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .navbar-header {
    padding-left: var(--vz-grid-gutter-width);
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .navbar-menu {
    top: calc(70px + var(--vz-grid-gutter-width));
    bottom: var(--vz-grid-gutter-width);
    padding: 0;
    border-right: var(--vz-vertical-menu-bg);
    border-radius: 5px;
    padding: 10px 0;
    z-index: 1;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .navbar-menu .navbar-brand-box {
    display: none;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-dark {
    display: inline-block;
  }
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-light {
    display: none;
  }
}
:is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .footer {
  border-top: 1px dashed var(--vz-border-color);
}
:is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached] .auth-page-wrapper .footer {
  border-top: none;
}
@media (min-width: 768px) {
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached][data-sidebar-size=sm] #layout-wrapper,
  :is([data-layout=vertical], [data-layout=semibox])[data-layout-style=detached][data-sidebar-size=sm] .main-content {
    min-height: 1400px;
  }
}
:is([data-layout=vertical], [data-layout=semibox]):is([data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-dark {
  display: none;
}
:is([data-layout=vertical], [data-layout=semibox]):is([data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-light {
  display: inline-block;
}

@media (min-width: 1024.1px) {
  [data-layout=vertical][data-bs-theme=dark][data-layout-style=detached]:is([data-sidebar=light], [data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-dark {
    display: none;
  }
  [data-layout=vertical][data-bs-theme=dark][data-layout-style=detached]:is([data-sidebar=light], [data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-light {
    display: inline-block;
  }
}

.menu-title {
  letter-spacing: 0.05em;
  cursor: default;
  font-size: 0.875rem;
  text-transform: uppercase;
  color: var(--vz-vertical-menu-title-color);
  font-weight: var(--vz-font-weight-semibold);
}
.menu-title span {
  padding: 12px 16px;
  display: inline-block;
}
.menu-title i {
  display: none;
}

.vertical-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(33, 37, 41, 0.35);
  z-index: 1003;
  display: none;
}

.vertical-sidebar-enable .vertical-overlay {
  display: block;
}
.vertical-sidebar-enable .app-menu {
  margin-left: 0 !important;
  z-index: 1004;
}

[dir=rtl] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse]:after {
  transform: rotate(180deg);
}
[dir=rtl] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
  transform: rotate(270deg);
}

.bg-vertical-gradient {
  background: linear-gradient(to right, var(--vz-primary), var(--vz-success));
}

.bg-vertical-gradient-2 {
  background: linear-gradient(to right, var(--vz-info), var(--vz-secondary));
}

.bg-vertical-gradient-3 {
  background: linear-gradient(to right, var(--vz-info), var(--vz-success));
}

.bg-vertical-gradient-4 {
  background: linear-gradient(to right, #1a1d21, var(--vz-primary));
}

.sidebar-background {
  position: absolute;
  z-index: -1;
  height: 100%;
  width: 100%;
  display: block;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: 50%;
  opacity: 0.07;
}

[data-sidebar-image=img-1] .sidebar-background {
  background-image: url("../images/sidebar/img-1.jpg");
}

[data-sidebar-image=img-2] .sidebar-background {
  background-image: url("../images/sidebar/img-2.jpg");
}

[data-sidebar-image=img-3] .sidebar-background {
  background-image: url("../images/sidebar/img-3.jpg");
}

[data-sidebar-image=img-4] .sidebar-background {
  background-image: url("../images/sidebar/img-4.jpg");
}

[data-layout=semibox] .page-title-box {
  padding: 10px 0;
  background-color: transparent;
  box-shadow: none;
  margin: -15px 0 1.5rem 0;
}
@media (min-width: 768px) {
  [data-layout=semibox][data-sidebar-size=sm] .main-content, [data-layout=semibox][data-sidebar-size=sm-hover] .main-content {
    margin-left: var(--vz-vertical-menu-width-sm);
  }
  [data-layout=semibox][data-sidebar-size=sm] #page-topbar, [data-layout=semibox][data-sidebar-size=sm-hover] #page-topbar {
    left: var(--vz-vertical-menu-width-sm);
  }
  [data-layout=semibox][data-sidebar-size=sm] .footer, [data-layout=semibox][data-sidebar-size=sm-hover] .footer {
    left: var(--vz-vertical-menu-width-sm);
  }
  [data-layout=semibox][data-sidebar-size=md] #page-topbar {
    left: var(--vz-vertical-menu-width-md);
  }
  [data-layout=semibox][data-sidebar-size=md] .footer {
    left: var(--vz-vertical-menu-width-md);
  }
}
@media (min-width: 1440px) {
  [data-layout=semibox] .page-content {
    padding: calc(70px + var(--vz-grid-gutter-width) * 2) calc(var(--vz-grid-gutter-width) * 0.5) 60px calc(var(--vz-grid-gutter-width) * 0.5);
  }
  [data-layout=semibox] .navbar-menu {
    margin: 25px;
    border-radius: var(--vz-border-radius);
  }
  [data-layout=semibox] .main-content {
    margin-left: calc(var(--vz-vertical-menu-width) + 25px);
    padding: 0 var(--vz-semibox-width);
  }
  [data-layout=semibox] .footer {
    left: calc(var(--vz-vertical-menu-width) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
    right: calc(var(--vz-semibox-width) + var(--vz-grid-gutter-width));
  }
  [data-layout=semibox] #page-topbar {
    left: calc(var(--vz-vertical-menu-width) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
    right: calc(var(--vz-semibox-width) + var(--vz-grid-gutter-width));
    top: 25px;
    border-radius: 0.25rem;
    transition: all 0.5s ease;
    border: 1px solid var(--vz-header-border);
  }
  [data-layout=semibox] #page-topbar.topbar-shadow {
    top: 0px;
  }
  [data-layout=semibox][data-sidebar-size=md] .main-content {
    margin-left: calc(var(--vz-vertical-menu-width-md) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=md] #page-topbar {
    left: calc(var(--vz-vertical-menu-width-md) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=md] .footer {
    left: calc(var(--vz-vertical-menu-width-md) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm] .main-content {
    margin-left: calc(var(--vz-vertical-menu-width-sm) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm] .navbar-brand-box {
    top: 25px;
  }
  [data-layout=semibox][data-sidebar-size=sm] #page-topbar {
    left: calc(var(--vz-vertical-menu-width-sm) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm] .footer {
    left: calc(var(--vz-vertical-menu-width-sm) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm-hover] .main-content {
    margin-left: calc(var(--vz-vertical-menu-width-sm) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm-hover] #page-topbar {
    left: calc(var(--vz-vertical-menu-width-sm) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
  [data-layout=semibox][data-sidebar-size=sm-hover] .footer {
    left: calc(var(--vz-vertical-menu-width-sm) + var(--vz-semibox-width) + var(--vz-grid-gutter-width) + 25px);
  }
}
[data-layout=semibox] .mx-n4 {
  margin-left: 0px !important;
  margin-right: 0px !important;
}
[data-layout=semibox] .mx-n4.p-1 {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

@media (min-width: 1440px) {
  [data-theme=vintage]:is([data-layout=vertical], [data-layout=twocolumn], [data-layout=horizontal]) .page-content {
    padding: calc(70px + var(--vz-grid-gutter-width)) calc(var(--vz-grid-gutter-width) * 5) 60px calc(var(--vz-grid-gutter-width) * 5);
  }
}
[data-layout=horizontal] .main-content {
  margin-left: 0;
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal] .layout-width,
  [data-layout=horizontal] .container-fluid {
    max-width: 90%;
    margin: 0 auto;
  }
  [data-layout=horizontal] .topnav-hamburger {
    visibility: hidden;
  }
}
[data-layout=horizontal] .horizontal-logo {
  padding-left: calc(var(--vz-grid-gutter-width) * 0.5);
}
@media (max-width: 1024.98px) {
  [data-layout=horizontal] .horizontal-logo {
    padding-left: var(--vz-grid-gutter-width);
  }
}
[data-layout=horizontal] .navbar-menu {
  background: var(--vz-topnav-bg);
  border-right-color: transparent;
  padding: 0 calc(var(--vz-grid-gutter-width) * 0.5);
  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  margin-top: 70px;
  position: fixed;
  left: 0;
  right: 0;
  z-index: 100;
  width: 100%;
  bottom: auto;
}
@media (max-width: 575.98px) {
  [data-layout=horizontal] .navbar-menu .container-fluid {
    padding: 0;
  }
}
[data-layout=horizontal] .navbar-menu .navbar-nav {
  flex-direction: row;
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm {
  padding-left: 0;
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link:before {
  opacity: 0 !important;
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link:hover, [data-layout=horizontal] .navbar-menu .navbar-nav .nav-sm .nav-link.active {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link {
  color: var(--vz-topnav-item-color);
  padding: 0.75rem 1.5rem;
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link i {
  line-height: 1;
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active:after {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link.active .icon-dual {
  color: var(--vz-topnav-item-active-color);
  fill: rgba(var(--vz-primary-rgb), 0.1);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true] .icon-dual {
  color: var(--vz-topnav-item-active-color);
  fill: rgba(var(--vz-primary-rgb), 0.1);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link[data-bs-toggle=collapse][aria-expanded=true]:after {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-menu .navbar-nav .nav-link:hover .icon-dual {
  color: var(--vz-topnav-item-active-color);
  fill: rgba(var(--vz-primary-rgb), 0.1);
}
[data-layout=horizontal] .navbar-menu .navbar-nav > .nav-item > .nav-link[data-bs-toggle=collapse]:after {
  right: 0px;
  transform: rotate(90deg) !important;
}
[data-layout=horizontal] .navbar-menu .navbar-nav > li:nth-of-type(2) > .nav-link.menu-link {
  padding-left: 0;
}
[data-layout=horizontal] .navbar-menu .navbar-brand-box {
  display: none;
}
[data-layout=horizontal] .navbar-nav .nav-item {
  position: relative;
}
[data-layout=horizontal] .navbar-nav .nav-item .nav-link[data-bs-toggle=collapse]:after {
  right: 10px;
  transform: rotate(0deg) !important;
}
[data-layout=horizontal] .navbar-nav .nav-item > .nav-link > .badge {
  display: none;
}
[data-layout=horizontal] .navbar-nav .nav-item:hover > .nav-link {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .navbar-nav .nav-item:hover > .nav-link .icon-dual {
  color: var(--vz-topnav-item-active-color);
  fill: rgba(var(--vz-primary-rgb), 0.1);
}
[data-layout=horizontal] .navbar-nav .nav-item:hover > .nav-link:after {
  color: var(--vz-topnav-item-active-color);
}
@media (min-width: 1024.1px) {
  [data-layout=horizontal] .navbar-nav .nav-item:hover > .menu-dropdown {
    display: block;
    height: auto !important;
  }
}
[data-layout=horizontal] .navbar-nav .nav-item.active {
  color: var(--vz-topnav-item-active-color);
}
[data-layout=horizontal] .menu-dropdown {
  position: absolute;
  min-width: 12rem;
  padding: 0.5rem 0;
  box-shadow: 0 0px 5px rgba(15, 34, 58, 0.15);
  animation-name: DropDownSlide;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  margin: 0;
  z-index: 1000;
  background-color: var(--vz-topnav-bg);
  background-clip: padding-box;
  border: 0 solid var(--vz-border-color);
  border-radius: var(--vz-border-radius-lg);
  display: none;
}
[data-layout=horizontal] .menu-dropdown .menu-dropdown {
  top: 0;
  left: 100%;
}
[data-layout=horizontal] .mega-dropdown-menu {
  width: 40rem;
}
[data-layout=horizontal] .menu-title {
  display: none;
}
[data-layout=horizontal] .dropdown-custom-right {
  left: -100% !important;
  right: 100%;
}
@media (max-width: 1024px) {
  [data-layout=horizontal] .navbar-menu {
    display: none;
  }
}

@media (max-width: 1024px) {
  [data-layout=horizontal] .menu .navbar-menu {
    display: block;
    max-height: 360px;
    overflow-y: auto;
    padding-left: 0;
  }
  [data-layout=horizontal] .menu .navbar-menu .navbar-nav {
    flex-direction: column;
  }
  [data-layout=horizontal] .menu .navbar-menu .navbar-nav > li:nth-of-type(2) > .nav-link.menu-link {
    padding-left: 1rem;
  }
  [data-layout=horizontal] .menu .navbar-menu .navbar-nav .nav-sm .nav-link:before {
    opacity: 1 !important;
  }
  [data-layout=horizontal] .menu .menu-dropdown {
    position: relative;
    min-width: 100%;
    box-shadow: none;
    padding-left: 28px;
    left: 0;
    animation: none;
    padding-top: 0;
  }
  [data-layout=horizontal] .menu .menu-dropdown.show {
    display: block;
  }
  [data-layout=horizontal] .menu .dropdown-custom-right {
    left: 0 !important;
  }
  [data-layout=horizontal] .menu .nav-item .nav-link[data-bs-toggle=collapse]:after {
    right: 0px;
  }
  [data-layout=horizontal] .menu .mega-dropdown-menu {
    width: 100%;
  }
}

[dir=rtl][data-layout=horizontal] .navbar-menu .navbar-nav > .nav-item > .nav-link[data-bs-toggle=collapse]:after {
  transform: rotate(-90deg) !important;
}
[dir=rtl][data-layout=horizontal] .navbar-nav .nav-item .nav-link[data-bs-toggle=collapse]:after {
  transform: rotate(-180deg) !important;
}

[data-layout=twocolumn] .app-menu {
  padding-bottom: 0;
  width: 220px;
  left: 70px;
}
[data-layout=twocolumn] .app-menu .menu-link {
  letter-spacing: 0.05em;
  cursor: default;
  font-size: 11px;
  text-transform: uppercase;
  font-weight: var(--vz-font-weight-semibold);
}
[data-layout=twocolumn] .app-menu .menu-link:after {
  display: none !important;
}
[data-layout=twocolumn] .app-menu .menu-link i {
  display: none;
}
[data-layout=twocolumn] .app-menu .navbar-nav {
  height: calc(100vh - 70px);
}
@media (max-width: 767.98px) {
  [data-layout=twocolumn] .app-menu .navbar-nav {
    padding-top: 16px;
  }
}
[data-layout=twocolumn] .app-menu .navbar-nav > li:not(.twocolumn-item-show) {
  display: none;
}
[data-layout=twocolumn] .app-menu .navbar-nav .twocolumn-item-show > div {
  display: block !important;
  height: auto !important;
}
[data-layout=twocolumn] .app-menu .navbar-nav > .nav-item > .menu-dropdown {
  display: block !important;
  height: auto !important;
}
[data-layout=twocolumn] .app-menu .navbar-nav .nav-item .menu-dropdown .row {
  margin: 0;
}
[data-layout=twocolumn] .app-menu .navbar-nav .nav-item .menu-dropdown .row .col-lg-4 {
  width: 100%;
  padding: 0;
}
[data-layout=twocolumn] .app-menu .container-fluid {
  padding: 0;
}
[data-layout=twocolumn] .main-content {
  margin-left: calc(220px + 70px);
}
@media (max-width: 767.98px) {
  [data-layout=twocolumn] .main-content {
    margin-left: 70px;
  }
}
[data-layout=twocolumn]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .app-menu .navbar-brand-box .logo-light {
  display: block;
}
[data-layout=twocolumn]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .app-menu .navbar-brand-box .logo-dark {
  display: none;
}
[data-layout=twocolumn] .twocolumn-iconview {
  width: 70px;
  background-color: var(--vz-twocolumn-menu-iconview-bg);
  height: 100%;
  left: -70px;
  box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  top: 0;
  position: absolute;
  padding: 0;
  text-align: center;
}
[data-layout=twocolumn] .twocolumn-iconview li {
  position: relative;
}
[data-layout=twocolumn] .twocolumn-iconview li .nav-icon::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
[data-layout=twocolumn] .twocolumn-iconview .nav-icon {
  width: 42px;
  height: 42px;
  color: var(--vz-vertical-menu-item-color);
  line-height: 42px;
  z-index: 1;
  font-size: 22px;
  text-align: center;
  border-radius: 3px;
  margin: 5px 0;
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
[data-layout=twocolumn] .twocolumn-iconview .nav-icon .icon-dual {
  width: 18px;
  color: var(--vz-vertical-menu-item-color);
  fill: var(--vz-vertical-menu-item-bg);
}
[data-layout=twocolumn] .twocolumn-iconview .nav-icon.active {
  background-color: var(--vz-vertical-menu-item-active-bg);
  color: var(--vz-vertical-menu-item-active-color);
}
[data-layout=twocolumn] .twocolumn-iconview .nav-icon.active .icon-dual {
  color: var(--vz-vertical-menu-item-active-color);
  fill: var(--vz-vertical-menu-item-active-bg);
}
[data-layout=twocolumn] .menu-title {
  display: none;
}
@media (max-width: 991.98px) {
  [data-layout=twocolumn] .logo span.logo-lg {
    display: block;
  }
  [data-layout=twocolumn] .logo span.logo-sm {
    display: none;
  }
}
[data-layout=twocolumn][data-sidebar=light] .app-menu .navbar-brand-box .logo-light {
  display: none;
}
[data-layout=twocolumn][data-sidebar=light] .app-menu .navbar-brand-box .logo-dark {
  display: block;
}
[data-layout=twocolumn][data-sidebar=dark] .app-menu .navbar-brand-box .logo-light {
  display: block;
}
[data-layout=twocolumn][data-sidebar=dark] .app-menu .navbar-brand-box .logo-dark {
  display: none;
}
[data-layout=twocolumn] .twocolumn-panel .app-menu {
  width: 0;
  border-right: 0;
}
@media (max-width: 575.98px) {
  [data-layout=twocolumn] .twocolumn-panel .app-menu {
    display: none;
  }
}
[data-layout=twocolumn] .twocolumn-panel .navbar-brand-box,
[data-layout=twocolumn] .twocolumn-panel .navbar-nav {
  display: none;
}
[data-layout=twocolumn] .twocolumn-panel .main-content {
  margin-left: 70px;
}
[data-layout=twocolumn] .twocolumn-panel #page-topbar,
[data-layout=twocolumn] .twocolumn-panel .footer {
  left: 70px;
}
@media (max-width: 575.98px) {
  [data-layout=twocolumn] .twocolumn-panel .main-content {
    margin-left: 0;
  }
  [data-layout=twocolumn] .twocolumn-panel #page-topbar,
  [data-layout=twocolumn] .twocolumn-panel .footer {
    left: 0;
  }
}
@media (max-width: 767.98px) {
  [data-layout=twocolumn] #page-topbar,
  [data-layout=twocolumn] .footer {
    left: 70px;
  }
}
@media (max-width: 575.98px) {
  [data-layout=twocolumn] .main-content {
    margin-left: 0;
  }
  [data-layout=twocolumn] #page-topbar,
  [data-layout=twocolumn] .footer {
    left: 0;
  }
}

@media (max-width: 767.98px) {
  [data-layout=twocolumn] .app-menu {
    left: 0;
    margin-left: 100%;
  }
  [data-layout=twocolumn] .main-content {
    margin-left: 0 !important;
  }
  [data-layout=twocolumn] #page-topbar,
  [data-layout=twocolumn] .footer {
    left: 0 !important;
  }
}

[data-layout-width=boxed] body {
  background-color: var(--vz-boxed-body-bg);
}
[data-layout-width=boxed] #layout-wrapper {
  max-width: var(--vz-boxed-layout-width);
  margin: 0 auto;
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  background-color: var(--vz-body-bg);
}
[data-layout-width=boxed][data-layout=vertical] #layout-wrapper {
  min-height: 100vh;
}
[data-layout-width=boxed] #page-topbar,
[data-layout-width=boxed] .footer {
  max-width: var(--vz-boxed-layout-width);
  margin: 0 auto;
  left: 0 !important;
}
@media (min-width: 768px) {
  [data-layout-width=boxed][data-sidebar-size=sm-hover][data-layout=vertical] #layout-wrapper, [data-layout-width=boxed][data-sidebar-size=sm][data-layout=vertical] #layout-wrapper {
    min-height: 1400px;
  }
}
@media (max-width: 767.98px) {
  [data-layout-width=boxed][data-sidebar-size=sm-hover][data-layout=vertical] .main-content, [data-layout-width=boxed][data-sidebar-size=sm][data-layout=vertical] .main-content {
    margin-left: 0;
  }
}
[data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm-hover] .footer, [data-layout-width=boxed][data-sidebar-size=sm] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm] .footer {
  left: 0 !important;
  max-width: calc(var(--vz-boxed-layout-width) - var(--vz-vertical-menu-width-sm));
}
@media (min-width: 768px) {
  [data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
  [data-layout-width=boxed][data-sidebar-size=sm-hover] .footer, [data-layout-width=boxed][data-sidebar-size=sm] #page-topbar,
  [data-layout-width=boxed][data-sidebar-size=sm] .footer {
    left: var(--vz-vertical-menu-width-sm) !important;
  }
}
[data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer, [data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=lg] .footer {
  max-width: calc(var(--vz-boxed-layout-width) - var(--vz-vertical-menu-width));
}
@media (min-width: 768px) {
  [data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
  [data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer, [data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
  [data-layout-width=boxed][data-sidebar-size=lg] .footer {
    left: var(--vz-vertical-menu-width) !important;
  }
}
[data-layout-width=boxed][data-sidebar-size=md] #page-topbar,
[data-layout-width=boxed][data-sidebar-size=md] .footer {
  max-width: calc(var(--vz-boxed-layout-width) - var(--vz-vertical-menu-width-md));
}
@media (min-width: 768px) {
  [data-layout-width=boxed][data-sidebar-size=md] #page-topbar,
  [data-layout-width=boxed][data-sidebar-size=md] .footer {
    left: var(--vz-vertical-menu-width-md) !important;
  }
}
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] body {
  background-color: var(--vz-body-bg);
}
@media (min-width: 1024.1px) {
  [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] #layout-wrapper {
    max-width: 1300px;
    box-shadow: none;
  }
  [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached] .layout-width {
    max-width: 1300px;
  }
}
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm-hover] .footer, [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=sm] .footer, [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=md] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=md] .footer, [data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=lg] #page-topbar,
[data-layout-width=boxed][data-layout=vertical][data-layout-style=detached][data-sidebar-size=lg] .footer {
  max-width: 100%;
  left: 0 !important;
}

[data-layout=horizontal][data-layout-width=boxed] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed] #layout-wrapper,
[data-layout=horizontal][data-layout-width=boxed] .footer {
  max-width: 100%;
}
[data-layout=horizontal][data-layout-width=boxed] .container-fluid,
[data-layout=horizontal][data-layout-width=boxed] .navbar-header {
  max-width: var(--vz-boxed-layout-width);
}
[data-layout=horizontal][data-layout-width=boxed] .navbar-header {
  padding: 0 calc(var(--vz-grid-gutter-width) * 0.5) 0 0;
}
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover] .footer, [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm] [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover-active] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm] [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=sm-hover-active] .footer, [data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=lg] #page-topbar,
[data-layout=horizontal][data-layout-width=boxed][data-sidebar-size=lg] .footer {
  left: 0 !important;
}

@media (min-width: 992px) {
  [data-layout-position=scrollable] #page-topbar,
  [data-layout-position=scrollable] .navbar-menu {
    position: absolute;
  }
}
@media (min-width: 992px) {
  [data-layout-position=scrollable][data-layout=horizontal] #page-topbar,
  [data-layout-position=scrollable][data-layout=horizontal] .topnav {
    position: absolute;
  }
}

@media (min-width: 768px) {
  [data-layout=semibox][data-sidebar-visibility=hidden] .navbar-menu {
    display: none;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .horizontal-logo {
    display: inline-block;
    width: auto;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .footer {
    left: 0;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] #page-topbar {
    left: 0;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .main-content {
    margin-left: 0px;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .navbar-brand-box, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .navbar-brand-box {
    position: static;
    padding: 0 1.3rem;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .logo span.logo-lg, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .logo span.logo-lg {
    display: inline-block;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .logo span.logo-sm, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .logo span.logo-sm {
    display: none;
  }
}
[data-layout=semibox][data-sidebar-visibility=hidden]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-dark {
  display: none;
}
[data-layout=semibox][data-sidebar-visibility=hidden]:is([data-sidebar=dark], [data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) .logo-light {
  display: inline-block;
}
@media (min-width: 1440px) {
  [data-layout=semibox][data-sidebar-visibility=hidden] .navbar-menu {
    display: none;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .horizontal-logo {
    display: inline-block;
    width: auto;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .footer {
    left: calc(var(--vz-semibox-width) + var(--vz-grid-gutter-width));
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] #page-topbar {
    left: calc(var(--vz-semibox-width) + var(--vz-grid-gutter-width));
  }
  [data-layout=semibox][data-sidebar-visibility=hidden] .main-content {
    margin-left: 0px;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .navbar-brand-box, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .navbar-brand-box {
    position: static;
    padding: 0 1.3rem;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .logo span.logo-lg, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .logo span.logo-lg {
    display: inline-block;
  }
  [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm] .logo span.logo-sm, [data-layout=semibox][data-sidebar-visibility=hidden][data-sidebar-size=sm-hover] .logo span.logo-sm {
    display: none;
  }
}

.icon {
  --vz-icon-size: 1.25rem;
  stroke-width: 1.5;
  font-size: var(--vz-icon-size);
  height: var(--vz-icon-size);
  vertical-align: bottom;
  width: var(--vz-icon-size);
}

.web-logo-text {
  font-size: 28px;
  font-weight: 600;
  color: var(--vz-primary);
}

.icon-md {
  --vz-icon-size: 1.5rem;
}

.icon-lg {
  --vz-icon-size: 1.75rem;
}

.form-group {
  margin-bottom: 1rem;
}

small {
  font-weight: 500;
}

.bg-pink {
  --vz-bg-opacity: 1;
  background-color: rgba(var(--vz-pink-rgb), var(--vz-bg-opacity)) !important;
}

.text-plain {
  color: green;
  font-weight: 500;
}

.badge-sm {
  padding: 0.75rem;
  font-size: 0.7rem !important;
  color: #fff;
  height: 0;
  width: auto;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  margin: 0 2px;
}

.form-fieldset {
  background: var(--vz-body-bg);
  border: var(--vz-border-width) var(--vz-border-style) var(--vz-border-color);
  border-radius: var(--vz-border-radius);
  margin-bottom: 1rem;
  padding: 1rem;
}

fieldset:empty {
  display: none;
}

.dropdown-item {
  font-weight: 500;
}

.package-price {
  padding: 2px 4px;
  color: red;
  font-weight: 600;
}

.package-status {
  padding: 2px 4px;
  background-color: green;
  color: white;
  font-weight: 600;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
}

.package-info {
  margin-left: 10px;
  padding: 8px 14px;
  border-radius: 5px;
  margin-bottom: 15px;
  transition: all 0.3s;
  border: 1px solid #C2F7F4;
  background-color: #C2F7F4;
  color: #000;
  font-weight: 500;
}

.package-id {
  color: orangered;
}

.radio-package.stopped *,
.radio-package.paused * {
  cursor: not-allowed;
  pointer-events: none;
}

.package-status.stopped,
.radio-package.stopped .package-status,
.radio-package.paused .package-status {
  background-color: orangered;
}

.radio-package.slow .package-status {
  background-color: #FFCC00;
}

.fb-reaction {
  min-height: 90px;
  text-align: center;
}
.fb-reaction .checkbox {
  display: inline-block;
  margin-left: 1rem;
}
.fb-reaction img {
  width: 50px;
  display: block;
  transition: all 0.3s;
  cursor: pointer;
  border-bottom: 6px solid transparent;
}

.widget-top {
  display: flex;
  align-items: flex-start;
}
.widget-top__avatar img {
  width: 110px;
  border-radius: 50%;
}
.widget-top__content {
  width: 100%;
  padding-left: 1.25rem;
}

.widget-bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 1.5rem;
  border-top: 1px solid var(--vz-border-color);
}
.widget-bottom__item {
  display: flex;
  align-items: center;
  flex-grow: 1;
  padding: 1.5rem 1rem 0 0;
}
.widget-bottom__content {
  padding-left: 1rem;
}
.widget-bottom__balance {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--vz-dark);
}

.tab-filter li .count {
  background-color: whitesmoke;
  padding: 2px 7px;
  margin-left: 3px;
  border-radius: 10px;
}

.card-faq-question {
  padding: 0.5rem;
  border: 1px solid var(--vz-border-color);
  color: var(--vz-dark);
  border-radius: 4px;
}

.list-notification {
  max-height: 180px;
  overflow: auto;
  border-bottom: 1px solid #ddd;
}

.notify-item__time {
  border-right: 1px solid #ddd;
  padding-right: 10px;
  width: 50px;
  color: brown;
  padding-bottom: 10px;
  font-size: 13px;
  font-weight: 500;
}
.notify-item__content {
  padding-left: 10px;
  padding-bottom: 10px;
  font-weight: 500;
  word-break: break-word;
  font-size: 13px;
}

.post-icon {
  width: 50px;
  border-radius: 5px;
  margin-right: 8px;
}

.post-time {
  font-size: 13px;
  font-weight: 600;
}

.post-name {
  font-size: 15px;
  font-weight: 600;
  padding-bottom: 10px;
  color: cadetblue;
}

.post-content {
  margin-top: 10px;
  margin-bottom: 25px;
  font-weight: 500;
}

.post-image {
  text-align: center;
  cursor: pointer;
}
.post-image img {
  width: 400px;
  max-width: 100%;
  height: auto;
}

/*!
 * Waves v0.7.6
 * http://fian.my.id/Waves 
 * 
 * Copyright 2014-2018 Alfiana E. Sibuea and other contributors 
 * Released under the MIT license 
 * https://github.com/fians/Waves/blob/master/LICENSE */
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
  transition: all 0.5s ease-out;
  transition-property: transform, opacity;
  transform: scale(0) translate(0, 0);
  pointer-events: none;
}

.waves-effect.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);
}

.waves-effect.waves-classic .waves-ripple {
  background: rgba(0, 0, 0, 0.2);
}

.waves-effect.waves-classic.waves-light .waves-ripple {
  background: rgba(255, 255, 255, 0.4);
}

.waves-notransition {
  transition: none !important;
}

.waves-button,
.waves-circle {
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}

.waves-button,
.waves-button:hover,
.waves-button:visited,
.waves-button-input {
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: none;
  outline: none;
  color: inherit;
  background-color: rgba(0, 0, 0, 0);
  font-size: 1em;
  line-height: 1em;
  text-align: center;
  text-decoration: none;
  z-index: 1;
}

.waves-button {
  padding: 0.85em 1.1em;
  border-radius: 0.2em;
}

.waves-button-input {
  margin: 0;
  padding: 0.85em 1.1em;
}

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}

.waves-input-wrapper.waves-button {
  padding: 0;
}

.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
}

.waves-float {
  -webkit-mask-image: none;
  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);
  transition: all 300ms;
}

.waves-float:active {
  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);
}

.waves-block {
  display: block;
}

.waves-effect.waves-light .waves-ripple {
  background-color: rgba(255, 255, 255, 0.4);
}

.waves-effect.waves-primary .waves-ripple {
  background-color: rgba(53, 119, 241, 0.4);
}

.waves-effect.waves-success .waves-ripple {
  background-color: rgba(10, 187, 135, 0.4);
}

.waves-effect.waves-info .waves-ripple {
  background-color: rgba(41, 156, 219, 0.4);
}

.waves-effect.waves-warning .waves-ripple {
  background-color: rgba(255, 175, 0, 0.4);
}

.waves-effect.waves-danger .waves-ripple {
  background-color: rgba(228, 34, 34, 0.4);
}

.avatar-xxs {
  height: 1.5rem;
  width: 1.5rem;
}

.avatar-xs {
  height: 2rem;
  width: 2rem;
}

.avatar-sm {
  height: 3rem;
  width: 3rem;
}

.avatar-md {
  height: 4.5rem;
  width: 4.5rem;
}

.avatar-lg {
  height: 6rem;
  width: 6rem;
}

.avatar-xl {
  height: 7.5rem;
  width: 7.5rem;
}

.avatar-title {
  align-items: center;
  background-color: var(--vz-primary);
  color: #fff;
  display: flex;
  font-weight: var(--vz-font-weight-medium);
  height: 100%;
  justify-content: center;
  width: 100%;
}

.avatar-group {
  padding-left: 12px;
  display: flex;
  flex-wrap: wrap;
}
.avatar-group .avatar-group-item {
  margin-left: -12px;
  border: 2px solid var(--vz-secondary-bg);
  border-radius: 50%;
  transition: all 0.2s;
}
.avatar-group .avatar-group-item:hover {
  position: relative;
  transform: translateY(-2px);
  z-index: 1;
}

.accordion .accordion-button {
  font-weight: var(--vz-font-weight-medium);
}
.accordion .accordion-body {
  color: var(--vz-secondary-color);
}
.accordion.accordion-icon-none .accordion-button::after {
  content: "";
  background-image: none !important;
}
.accordion.accordion-icon-none .accordion-button:not(.collapsed)::after {
  content: "";
}

.custom-accordionwithicon .accordion-button::after {
  background-image: none !important;
  font-family: "Material Design Icons";
  content: "\f0142";
  font-size: 1.1rem;
  vertical-align: middle;
  line-height: 0.8;
}
.custom-accordionwithicon .accordion-button:not(.collapsed)::after {
  background-image: none !important;
  content: "\f0140";
  margin-right: -3px;
}

.custom-accordionwithicon-plus .accordion-button::after {
  background-image: none !important;
  font-family: "Material Design Icons";
  content: "\f0415";
  font-size: 1.1rem;
  vertical-align: middle;
  line-height: 0.8;
}
.custom-accordionwithicon-plus .accordion-button:not(.collapsed)::after {
  background-image: none !important;
  content: "\f0374";
  margin-right: -3px;
}

.lefticon-accordion .accordion-button {
  padding-left: 2.75rem;
}
.lefticon-accordion .accordion-button::after {
  position: absolute;
  left: 1.25rem;
  top: 14px;
}
.lefticon-accordion .accordion-button:not(.collapsed)::after {
  top: 20px;
}

.accordion-border-box .accordion-item {
  border-top: var(--vz-border-width) solid var(--vz-border-color);
  border-radius: var(--vz-border-radius);
}
.accordion-border-box .accordion-item:not(:first-of-type) {
  margin-top: 8px;
}
.accordion-border-box .accordion-item .accordion-button {
  border-radius: var(--vz-border-radius);
}
.accordion-border-box .accordion-item .accordion-button:not(.collapsed) {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.custom-accordion-border .accordion-item {
  border-left: 3px solid var(--vz-border-color);
}

.accordion-primary .accordion-item {
  border-color: rgba(var(--vz-primary-rgb), 0.6);
}
.accordion-primary .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-primary .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-primary);
  background-color: rgba(var(--vz-primary-rgb), 0.1) !important;
}
.accordion-primary .accordion-item .accordion-button::after {
  color: var(--vz-primary);
}

.accordion-fill-primary .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-primary .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-primary) !important;
}

.accordion-secondary .accordion-item {
  border-color: rgba(var(--vz-secondary-rgb), 0.6);
}
.accordion-secondary .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-secondary .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-secondary);
  background-color: rgba(var(--vz-secondary-rgb), 0.1) !important;
}
.accordion-secondary .accordion-item .accordion-button::after {
  color: var(--vz-secondary);
}

.accordion-fill-secondary .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-secondary .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-secondary) !important;
}

.accordion-success .accordion-item {
  border-color: rgba(var(--vz-success-rgb), 0.6);
}
.accordion-success .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-success .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-success);
  background-color: rgba(var(--vz-success-rgb), 0.1) !important;
}
.accordion-success .accordion-item .accordion-button::after {
  color: var(--vz-success);
}

.accordion-fill-success .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-success .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-success) !important;
}

.accordion-info .accordion-item {
  border-color: rgba(var(--vz-info-rgb), 0.6);
}
.accordion-info .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-info .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-info);
  background-color: rgba(var(--vz-info-rgb), 0.1) !important;
}
.accordion-info .accordion-item .accordion-button::after {
  color: var(--vz-info);
}

.accordion-fill-info .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-info .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-info) !important;
}

.accordion-warning .accordion-item {
  border-color: rgba(var(--vz-warning-rgb), 0.6);
}
.accordion-warning .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-warning .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-warning);
  background-color: rgba(var(--vz-warning-rgb), 0.1) !important;
}
.accordion-warning .accordion-item .accordion-button::after {
  color: var(--vz-warning);
}

.accordion-fill-warning .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-warning .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-warning) !important;
}

.accordion-danger .accordion-item {
  border-color: rgba(var(--vz-danger-rgb), 0.6);
}
.accordion-danger .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-danger .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-danger);
  background-color: rgba(var(--vz-danger-rgb), 0.1) !important;
}
.accordion-danger .accordion-item .accordion-button::after {
  color: var(--vz-danger);
}

.accordion-fill-danger .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-danger .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-danger) !important;
}

.accordion-light .accordion-item {
  border-color: rgba(var(--vz-light-rgb), 0.6);
}
.accordion-light .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-light .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-light);
  background-color: rgba(var(--vz-light-rgb), 0.1) !important;
}
.accordion-light .accordion-item .accordion-button::after {
  color: var(--vz-light);
}

.accordion-fill-light .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-light .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-light) !important;
}

.accordion-dark .accordion-item {
  border-color: rgba(var(--vz-dark-rgb), 0.6);
}
.accordion-dark .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-dark .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-dark);
  background-color: rgba(var(--vz-dark-rgb), 0.1) !important;
}
.accordion-dark .accordion-item .accordion-button::after {
  color: var(--vz-dark);
}

.accordion-fill-dark .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-dark .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-dark) !important;
}

.accordion-pink .accordion-item {
  border-color: rgba(var(--vz-pink-rgb), 0.6);
}
.accordion-pink .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-pink .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-pink);
  background-color: rgba(var(--vz-pink-rgb), 0.1) !important;
}
.accordion-pink .accordion-item .accordion-button::after {
  color: var(--vz-pink);
}

.accordion-fill-pink .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-pink .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-pink) !important;
}

.accordion-purple .accordion-item {
  border-color: rgba(var(--vz-purple-rgb), 0.6);
}
.accordion-purple .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-purple .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-purple);
  background-color: rgba(var(--vz-purple-rgb), 0.1) !important;
}
.accordion-purple .accordion-item .accordion-button::after {
  color: var(--vz-purple);
}

.accordion-fill-purple .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-purple .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-purple) !important;
}

.accordion-orange .accordion-item {
  border-color: rgba(var(--vz-orange-rgb), 0.6);
}
.accordion-orange .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-orange .accordion-item .accordion-button:not(.collapsed) {
  color: var(--vz-orange);
  background-color: rgba(var(--vz-orange-rgb), 0.1) !important;
}
.accordion-orange .accordion-item .accordion-button::after {
  color: var(--vz-orange);
}

.accordion-fill-orange .accordion-item .accordion-button {
  box-shadow: none;
}
.accordion-fill-orange .accordion-item .accordion-button:not(.collapsed) {
  color: #fff;
  background-color: var(--vz-orange) !important;
}

[dir=rtl] .custom-accordionwithicon .accordion-button::after {
  transform: rotate(180deg);
}

.border-double {
  border-style: double !important;
}

.border-top-double {
  border-top-style: double !important;
}

.border-bottom-double {
  border-bottom-style: double !important;
}

.border-end-double {
  border-right-style: double !important;
}

.border-start-double {
  border-left-style: double !important;
}

.list-group-flush.border-double {
  border: none !important;
}
.list-group-flush.border-double .list-group-item {
  border-style: double !important;
}

.border-dashed {
  border-style: dashed !important;
}

.border-top-dashed {
  border-top-style: dashed !important;
}

.border-bottom-dashed {
  border-bottom-style: dashed !important;
}

.border-end-dashed {
  border-right-style: dashed !important;
}

.border-start-dashed {
  border-left-style: dashed !important;
}

.list-group-flush.border-dashed {
  border: none !important;
}
.list-group-flush.border-dashed .list-group-item {
  border-style: dashed !important;
}

.border-groove {
  border-style: groove !important;
}

.border-top-groove {
  border-top-style: groove !important;
}

.border-bottom-groove {
  border-bottom-style: groove !important;
}

.border-end-groove {
  border-right-style: groove !important;
}

.border-start-groove {
  border-left-style: groove !important;
}

.list-group-flush.border-groove {
  border: none !important;
}
.list-group-flush.border-groove .list-group-item {
  border-style: groove !important;
}

.border-outset {
  border-style: outset !important;
}

.border-top-outset {
  border-top-style: outset !important;
}

.border-bottom-outset {
  border-bottom-style: outset !important;
}

.border-end-outset {
  border-right-style: outset !important;
}

.border-start-outset {
  border-left-style: outset !important;
}

.list-group-flush.border-outset {
  border: none !important;
}
.list-group-flush.border-outset .list-group-item {
  border-style: outset !important;
}

.border-ridge {
  border-style: ridge !important;
}

.border-top-ridge {
  border-top-style: ridge !important;
}

.border-bottom-ridge {
  border-bottom-style: ridge !important;
}

.border-end-ridge {
  border-right-style: ridge !important;
}

.border-start-ridge {
  border-left-style: ridge !important;
}

.list-group-flush.border-ridge {
  border: none !important;
}
.list-group-flush.border-ridge .list-group-item {
  border-style: ridge !important;
}

.border-dotted {
  border-style: dotted !important;
}

.border-top-dotted {
  border-top-style: dotted !important;
}

.border-bottom-dotted {
  border-bottom-style: dotted !important;
}

.border-end-dotted {
  border-right-style: dotted !important;
}

.border-start-dotted {
  border-left-style: dotted !important;
}

.list-group-flush.border-dotted {
  border: none !important;
}
.list-group-flush.border-dotted .list-group-item {
  border-style: dotted !important;
}

.border-inset {
  border-style: inset !important;
}

.border-top-inset {
  border-top-style: inset !important;
}

.border-bottom-inset {
  border-bottom-style: inset !important;
}

.border-end-inset {
  border-right-style: inset !important;
}

.border-start-inset {
  border-left-style: inset !important;
}

.list-group-flush.border-inset {
  border: none !important;
}
.list-group-flush.border-inset .list-group-item {
  border-style: inset !important;
}

.ff-base {
  font-family: var(--vz-font-family-primary);
}

.ff-secondary {
  font-family: var(--vz-font-family-secondary);
}

.bg-pattern {
  background: url("../images/modal-bg.png") var(--vz-secondary-bg);
}

.w-xs {
  min-width: 80px;
}

.w-sm {
  min-width: 95px;
}

.w-md {
  min-width: 110px;
}

.w-lg {
  min-width: 140px;
}

.w-xl {
  min-width: 160px;
}

.icon-xs {
  height: 16px;
  width: 16px;
}

.icon-sm {
  height: 18px;
  width: 18px;
}

.icon-md {
  height: 22px;
  width: 22px;
}

.icon-lg {
  height: 24px;
  width: 24px;
}

.icon-xl {
  height: 28px;
  width: 28px;
}

.icon-xxl {
  height: 32px;
  width: 32px;
}

.icon-dual {
  color: #adb5bd;
  fill: rgba(173, 181, 189, 0.16);
}

.icon-dual-primary {
  color: var(--vz-primary);
  fill: rgba(var(--vz-primary-rgb), 0.16);
}

.icon-dual-secondary {
  color: var(--vz-secondary);
  fill: rgba(var(--vz-secondary-rgb), 0.16);
}

.icon-dual-success {
  color: var(--vz-success);
  fill: rgba(var(--vz-success-rgb), 0.16);
}

.icon-dual-info {
  color: var(--vz-info);
  fill: rgba(var(--vz-info-rgb), 0.16);
}

.icon-dual-warning {
  color: var(--vz-warning);
  fill: rgba(var(--vz-warning-rgb), 0.16);
}

.icon-dual-danger {
  color: var(--vz-danger);
  fill: rgba(var(--vz-danger-rgb), 0.16);
}

.icon-dual-light {
  color: var(--vz-light);
  fill: rgba(var(--vz-light-rgb), 0.16);
}

.icon-dual-dark {
  color: var(--vz-dark);
  fill: rgba(var(--vz-dark-rgb), 0.16);
}

.icon-dual-pink {
  color: var(--vz-pink);
  fill: rgba(var(--vz-pink-rgb), 0.16);
}

.icon-dual-purple {
  color: var(--vz-purple);
  fill: rgba(var(--vz-purple-rgb), 0.16);
}

.icon-dual-orange {
  color: var(--vz-orange);
  fill: rgba(var(--vz-orange-rgb), 0.16);
}

.search-box {
  position: relative;
}
.search-box .form-control {
  padding-left: 40px;
}
.search-box .search-icon {
  font-size: 14px;
  position: absolute;
  left: 13px;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--vz-secondary-color);
}

.bg-overlay {
  position: absolute;
  height: 100%;
  width: 100%;
  right: 0;
  bottom: 0;
  left: 0;
  top: 0;
  opacity: 0.7;
  background-color: #000;
}

.customizer-setting {
  position: fixed;
  bottom: 40px;
  right: 20px;
  z-index: 1000;
}

code {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}

.layout-rightside {
  width: 280px;
  margin-right: calc(var(--vz-grid-gutter-width) * -0.9);
  margin-top: calc(1px - var(--vz-grid-gutter-width) * 1.1);
  height: calc(100% + var(--vz-grid-gutter-width) * 1.1);
}

@media (max-width: 1699.98px) {
  .layout-rightside-col {
    display: none;
    position: fixed !important;
    height: 100vh;
    right: 0px;
    top: 0px;
    bottom: 0px;
    z-index: 1004;
  }
  .layout-rightside-col .overlay {
    position: fixed;
    top: 0;
    right: 0px;
    bottom: 0px;
    left: 0px;
    background-color: rgba(33, 37, 41, 0.2);
  }
  .layout-rightside-col .layout-rightside {
    margin-top: 0px;
    height: 100%;
    margin-left: auto;
  }
  .layout-rightside-col .card-body {
    overflow-y: auto;
    padding-bottom: 1rem !important;
  }
}

@media (min-width: 1700px) {
  :is([data-layout=horizontal], [data-layout-style=detached]) .layout-rightside {
    margin-top: calc(28px - var(--vz-grid-gutter-width));
    margin-right: 0;
    height: calc(100% - var(--vz-grid-gutter-width) * 0.5);
  }
}

[data-layout=semibox] .layout-rightside {
  margin-right: 0px;
}

.text-truncate-two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.favourite-btn {
  border-color: transparent;
}
.favourite-btn .ri-star-fill {
  color: var(--vz-secondary-color);
}
.favourite-btn.active {
  border-color: transparent;
}
.favourite-btn.active .ri-star-fill {
  color: var(--vz-warning);
}
.favourite-btn.active .ri-star-fill:before {
  content: "\f186";
}

.card-logo-light {
  display: var(--vz-card-logo-light);
}

.card-logo-dark {
  display: var(--vz-card-logo-dark);
}

#back-to-top {
  position: fixed;
  bottom: 100px;
  right: 28px;
  transition: all 0.5s ease;
  display: none;
  z-index: 1000;
}
#back-to-top:hover {
  animation: fade-up 1.5s infinite linear;
}

@keyframes fade-up {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  75% {
    transform: translateY(-20px);
    opacity: 0;
  }
}
#preloader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--vz-secondary-bg);
  z-index: 9999;
}

[data-preloader=disable] #preloader {
  opacity: 0;
  visibility: hidden;
}

#status {
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -20px 0 0 -20px;
}

:is([type=tel], [type=url], [type=email], [type=number])::-moz-placeholder {
  text-align: left;
}

:is([type=tel], [type=url], [type=email], [type=number])::placeholder {
  text-align: left;
}

.main-chart .chart-border-left {
  border-left: 1.4px solid var(--vz-border-color);
  padding: 2px 20px;
}
.main-chart .chart-border-left:last-child {
  margin-right: 0px;
}

/* Activity */
.activity-feed {
  list-style: none;
}
.activity-feed .feed-item {
  position: relative;
  padding-bottom: 27px;
  padding-left: 16px;
  border-left: 2px solid var(--vz-border-color);
}
.activity-feed .feed-item:after {
  content: "";
  display: block;
  position: absolute;
  top: 4px;
  left: -6px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--vz-primary);
  background-color: var(--vz-secondary-bg);
}
.activity-feed .feed-item:last-child {
  border-color: transparent;
}

.mini-stats-wid {
  position: relative;
}
.mini-stats-wid .mini-stat-icon {
  overflow: hidden;
  position: relative;
}
.mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {
  content: "";
  position: absolute;
  width: 8px;
  height: 69px;
  background-color: rgba(var(--vz-success-rgb), 0.1);
  left: 3px;
  transform: rotate(32deg);
  top: -8px;
  transition: all 0.4s;
}
.mini-stats-wid .mini-stat-icon::after {
  left: 27px;
  width: 8px;
  transition: all 0.2s;
}
.mini-stats-wid:hover .mini-stat-icon::after {
  left: 60px;
}
.mini-stats-wid:hover .mini-stat-icon::before {
  left: 50px;
}

.card-wrapper .jp-card .jp-card-back, .card-wrapper .jp-card .jp-card-front {
  background-color: var(--vz-success);
}

.notification-elem {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: var(--vz-secondary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  visibility: hidden;
  opacity: 0;
}

.clothes-size li input[type=radio] {
  display: none;
}
.clothes-size li label {
  display: inline-block;
  font-size: 14px;
  border-radius: 5px;
  border: 1px solid var(--vz-border-color);
  text-transform: none;
  letter-spacing: 0;
  color: var(--vz-primary);
}
.clothes-size li input[type=radio]:checked + label {
  background-color: var(--vz-primary);
  border-color: var(--vz-primary);
  color: #fff;
}

.bs-example-modal {
  position: relative;
  top: auto;
  right: auto;
  bottom: auto;
  left: auto;
  z-index: 1;
  display: block;
}

[dir=rtl] .modal-open {
  padding-left: 0px !important;
}

.icon-demo-content {
  color: var(--vz-gray-500);
}
.icon-demo-content i {
  font-size: 24px;
  margin-right: 10px;
  color: var(--vz-gray-600);
  transition: all 0.4s;
  vertical-align: middle;
}
.icon-demo-content svg {
  margin-right: 10px;
  transition: all 0.4s;
  height: 20px;
}
.icon-demo-content .col-lg-4 {
  margin-top: 24px;
}
.icon-demo-content .col-lg-4:hover i, .icon-demo-content .col-lg-4:hover svg {
  color: var(--vz-primary);
  transform: scale(1.5);
}

.img-switch .card-radio .form-check-input {
  display: none;
}
.img-switch .card-radio .form-check-input:checked + .form-check-label::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(var(--vz-primary-rgb), 0.5);
}
.img-switch .card-radio .form-check-input:checked + .form-check-label::after {
  content: "\eb80";
  font-family: "remixicon";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  font-size: 18px;
  color: #fff;
}

[data-bs-target="#collapseBgGradient"].active {
  border-color: var(--vz-primary-rgb) !important;
}
[data-bs-target="#collapseBgGradient"].active::before {
  content: "\eb80";
  font-family: remixicon;
  position: absolute;
  top: 2px;
  right: 6px;
  font-size: 16px;
  color: var(--vz-primary-rgb);
}

@media print {
  .vertical-menu,
  .right-bar,
  .page-title-box,
  .navbar-header,
  .app-menu,
  .footer,
  #back-to-top {
    display: none !important;
  }
  .card-body,
  .main-content,
  .right-bar,
  .page-content,
  body {
    padding: 0;
    margin: 0;
  }
  .card {
    border: 0;
    box-shadow: none !important;
  }
  .invoice-details .d-sm-flex {
    display: flex !important;
  }
  .address.col-sm-6 {
    flex: 0 0 auto !important;
    width: 50% !important;
    max-width: 100% !important;
  }
}
.ribbon-box {
  position: relative;
  /* Ribbon two */
}
.ribbon-box .ribbon {
  padding: 5px 12px;
  box-shadow: 2px 5px 10px rgba(var(--vz-dark-rgb), 0.15);
  color: #fff;
  font-size: var(--vz-font-base);
  font-weight: var(--vz-font-weight-semibold);
  position: absolute;
  left: -1px;
  top: 5px;
}
.ribbon-box .ribbon.round-shape {
  border-radius: 0 30px 30px 0;
}
.ribbon-box .ribbon.ribbon-shape {
  display: inline-block;
}
.ribbon-box .ribbon.ribbon-shape::before {
  content: "";
  position: absolute;
  right: -17px;
  top: 0;
  border: 14px solid transparent;
}
.ribbon-box .ribbon.ribbon-shape::after {
  content: "";
  position: absolute;
  right: -17px;
  bottom: 0;
  border: 14px solid transparent;
}
.ribbon-box.ribbon-circle .ribbon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 20px;
  top: 20px;
}
.ribbon-box.ribbon-fill {
  overflow: hidden;
}
.ribbon-box.ribbon-fill .ribbon {
  transform: rotate(-45deg);
  width: 93px;
  height: 52px;
  left: -36px;
  top: -16px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}
.ribbon-box.ribbon-fill.ribbon-sm .ribbon {
  padding: 2px 12px;
  width: 78px;
  height: 42px;
  font-size: 12px;
  box-shadow: none;
}
.ribbon-box.right .ribbon {
  position: absolute;
  left: auto;
  right: 0;
}
.ribbon-box.right .ribbon.round-shape {
  border-radius: 30px 0 0 30px;
}
.ribbon-box.right .ribbon.ribbon-shape {
  text-align: right;
}
.ribbon-box.right .ribbon.ribbon-shape::before, .ribbon-box.right .ribbon.ribbon-shape::after {
  right: auto;
  left: -17px;
  border-left-color: transparent;
}
.ribbon-box.right.ribbon-circle .ribbon {
  left: auto;
  right: 20px;
}
.ribbon-box.right .icon-ribbon {
  right: 24px;
  left: auto;
}
.ribbon-box.right.ribbon-fill .ribbon {
  transform: rotate(45deg);
  right: -38px;
  left: auto;
}
.ribbon-box.right.ribbon-box .ribbon-two {
  left: auto;
  right: -5px;
}
.ribbon-box.right.ribbon-box .ribbon-two span {
  left: auto;
  right: -21px;
  transform: rotate(45deg);
}
.ribbon-box .ribbon-content {
  clear: both;
}
.ribbon-box .icon-ribbon {
  box-shadow: none;
  left: 24px;
  top: -12px;
  font-size: 40px;
  padding: 0;
}
.ribbon-box .ribbon-two {
  position: absolute;
  left: -5px;
  top: -5px;
  z-index: 1;
  overflow: hidden;
  width: 75px;
  height: 75px;
  text-align: right;
}
.ribbon-box .ribbon-two span {
  font-size: 13px;
  color: #fff;
  text-align: center;
  line-height: 20px;
  transform: rotate(-45deg);
  width: 100px;
  display: block;
  box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);
  position: absolute;
  top: 19px;
  left: -21px;
  font-weight: var(--vz-font-weight-semibold);
}
.ribbon-box .ribbon-two span:before {
  content: "";
  position: absolute;
  left: 0;
  top: 100%;
  z-index: -1;
  border-right: 3px solid transparent;
  border-bottom: 3px solid transparent;
}
.ribbon-box .ribbon-two span:after {
  content: "";
  position: absolute;
  right: 0;
  top: 100%;
  z-index: -1;
  border-left: 3px solid transparent;
  border-bottom: 3px solid transparent;
}

.ribbon-box.right .ribbon-three {
  position: absolute;
  top: -6.1px;
  right: 10px;
  left: auto;
}

.ribbon-three {
  position: absolute;
  top: -6.1px;
  left: 10px;
}
.ribbon-three span {
  position: relative;
  display: block;
  text-align: center;
  color: #fff;
  font-size: 14px;
  line-height: 1;
  padding: 12px 8px 10px;
  border-top-right-radius: 8px;
  width: 90px;
}
.ribbon-three span::after, .ribbon-three span::before {
  position: absolute;
  content: "";
}
.ribbon-three span::before {
  height: 6px;
  width: 6px;
  left: -6px;
  top: 0;
}
.ribbon-three span::after {
  height: 6px;
  width: 8px;
  left: -8px;
  top: 0;
  border-radius: 8px 8px 0 0;
}
.ribbon-three::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border-left: 44px solid transparent;
  border-right: 44px solid transparent;
  border-top: 10px solid;
}

.ribbon-box .trending-ribbon {
  transform: translateX(-50px);
  transition: all 0.5s ease;
}
.ribbon-box .trending-ribbon .trending-ribbon-text {
  transition: all 0.5s ease;
  opacity: 0;
}
.ribbon-box:hover .trending-ribbon {
  transform: translateX(0);
}
.ribbon-box:hover .trending-ribbon .trending-ribbon-text {
  opacity: 1;
}
.ribbon-box.right .trending-ribbon {
  transform: translateX(50px);
  transition: all 0.5s ease;
}
.ribbon-box.right .trending-ribbon .trending-ribbon-text {
  transition: all 0.5s ease;
  opacity: 0;
}
.ribbon-box.right:hover .trending-ribbon {
  transform: translateX(0);
}
.ribbon-box.right:hover .trending-ribbon .trending-ribbon-text {
  opacity: 1;
}

.ribbon-three-primary span {
  background: var(--vz-primary);
}
.ribbon-three-primary span:before {
  background: var(--vz-primary);
}
.ribbon-three-primary span:after {
  background: var(--vz-primary-text-emphasis);
}
.ribbon-three-primary::after {
  border-top-color: var(--vz-primary);
}

.ribbon-box .ribbon-primary {
  background: var(--vz-primary);
}
.ribbon-box .ribbon-primary:before {
  border-color: var(--vz-primary-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-primary.ribbon-shape::before {
  border-left-color: var(--vz-primary);
  border-top-color: var(--vz-primary);
}
.ribbon-box .ribbon-primary.ribbon-shape::after {
  border-left-color: var(--vz-primary);
  border-bottom-color: var(--vz-primary);
}
.ribbon-box.right .ribbon-primary {
  background: var(--vz-primary);
}
.ribbon-box.right .ribbon-primary.ribbon-shape::before {
  border-right-color: var(--vz-primary);
  border-top-color: var(--vz-primary);
}
.ribbon-box.right .ribbon-primary.ribbon-shape::after {
  border-right-color: var(--vz-primary);
  border-bottom-color: var(--vz-primary);
}

.ribbon-two-primary span {
  background: var(--vz-primary);
}
.ribbon-two-primary span:before {
  border-left: 3px solid var(--vz-primary-text-emphasis);
  border-top: 3px solid var(--vz-primary-text-emphasis);
}
.ribbon-two-primary span:after {
  border-right: 3px solid var(--vz-primary-text-emphasis);
  border-top: 3px solid var(--vz-primary-text-emphasis);
}

.ribbon-three-secondary span {
  background: var(--vz-secondary);
}
.ribbon-three-secondary span:before {
  background: var(--vz-secondary);
}
.ribbon-three-secondary span:after {
  background: var(--vz-secondary-text-emphasis);
}
.ribbon-three-secondary::after {
  border-top-color: var(--vz-secondary);
}

.ribbon-box .ribbon-secondary {
  background: var(--vz-secondary);
}
.ribbon-box .ribbon-secondary:before {
  border-color: var(--vz-secondary-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-secondary.ribbon-shape::before {
  border-left-color: var(--vz-secondary);
  border-top-color: var(--vz-secondary);
}
.ribbon-box .ribbon-secondary.ribbon-shape::after {
  border-left-color: var(--vz-secondary);
  border-bottom-color: var(--vz-secondary);
}
.ribbon-box.right .ribbon-secondary {
  background: var(--vz-secondary);
}
.ribbon-box.right .ribbon-secondary.ribbon-shape::before {
  border-right-color: var(--vz-secondary);
  border-top-color: var(--vz-secondary);
}
.ribbon-box.right .ribbon-secondary.ribbon-shape::after {
  border-right-color: var(--vz-secondary);
  border-bottom-color: var(--vz-secondary);
}

.ribbon-two-secondary span {
  background: var(--vz-secondary);
}
.ribbon-two-secondary span:before {
  border-left: 3px solid var(--vz-secondary-text-emphasis);
  border-top: 3px solid var(--vz-secondary-text-emphasis);
}
.ribbon-two-secondary span:after {
  border-right: 3px solid var(--vz-secondary-text-emphasis);
  border-top: 3px solid var(--vz-secondary-text-emphasis);
}

.ribbon-three-success span {
  background: var(--vz-success);
}
.ribbon-three-success span:before {
  background: var(--vz-success);
}
.ribbon-three-success span:after {
  background: var(--vz-success-text-emphasis);
}
.ribbon-three-success::after {
  border-top-color: var(--vz-success);
}

.ribbon-box .ribbon-success {
  background: var(--vz-success);
}
.ribbon-box .ribbon-success:before {
  border-color: var(--vz-success-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-success.ribbon-shape::before {
  border-left-color: var(--vz-success);
  border-top-color: var(--vz-success);
}
.ribbon-box .ribbon-success.ribbon-shape::after {
  border-left-color: var(--vz-success);
  border-bottom-color: var(--vz-success);
}
.ribbon-box.right .ribbon-success {
  background: var(--vz-success);
}
.ribbon-box.right .ribbon-success.ribbon-shape::before {
  border-right-color: var(--vz-success);
  border-top-color: var(--vz-success);
}
.ribbon-box.right .ribbon-success.ribbon-shape::after {
  border-right-color: var(--vz-success);
  border-bottom-color: var(--vz-success);
}

.ribbon-two-success span {
  background: var(--vz-success);
}
.ribbon-two-success span:before {
  border-left: 3px solid var(--vz-success-text-emphasis);
  border-top: 3px solid var(--vz-success-text-emphasis);
}
.ribbon-two-success span:after {
  border-right: 3px solid var(--vz-success-text-emphasis);
  border-top: 3px solid var(--vz-success-text-emphasis);
}

.ribbon-three-info span {
  background: var(--vz-info);
}
.ribbon-three-info span:before {
  background: var(--vz-info);
}
.ribbon-three-info span:after {
  background: var(--vz-info-text-emphasis);
}
.ribbon-three-info::after {
  border-top-color: var(--vz-info);
}

.ribbon-box .ribbon-info {
  background: var(--vz-info);
}
.ribbon-box .ribbon-info:before {
  border-color: var(--vz-info-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-info.ribbon-shape::before {
  border-left-color: var(--vz-info);
  border-top-color: var(--vz-info);
}
.ribbon-box .ribbon-info.ribbon-shape::after {
  border-left-color: var(--vz-info);
  border-bottom-color: var(--vz-info);
}
.ribbon-box.right .ribbon-info {
  background: var(--vz-info);
}
.ribbon-box.right .ribbon-info.ribbon-shape::before {
  border-right-color: var(--vz-info);
  border-top-color: var(--vz-info);
}
.ribbon-box.right .ribbon-info.ribbon-shape::after {
  border-right-color: var(--vz-info);
  border-bottom-color: var(--vz-info);
}

.ribbon-two-info span {
  background: var(--vz-info);
}
.ribbon-two-info span:before {
  border-left: 3px solid var(--vz-info-text-emphasis);
  border-top: 3px solid var(--vz-info-text-emphasis);
}
.ribbon-two-info span:after {
  border-right: 3px solid var(--vz-info-text-emphasis);
  border-top: 3px solid var(--vz-info-text-emphasis);
}

.ribbon-three-warning span {
  background: var(--vz-warning);
}
.ribbon-three-warning span:before {
  background: var(--vz-warning);
}
.ribbon-three-warning span:after {
  background: var(--vz-warning-text-emphasis);
}
.ribbon-three-warning::after {
  border-top-color: var(--vz-warning);
}

.ribbon-box .ribbon-warning {
  background: var(--vz-warning);
}
.ribbon-box .ribbon-warning:before {
  border-color: var(--vz-warning-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-warning.ribbon-shape::before {
  border-left-color: var(--vz-warning);
  border-top-color: var(--vz-warning);
}
.ribbon-box .ribbon-warning.ribbon-shape::after {
  border-left-color: var(--vz-warning);
  border-bottom-color: var(--vz-warning);
}
.ribbon-box.right .ribbon-warning {
  background: var(--vz-warning);
}
.ribbon-box.right .ribbon-warning.ribbon-shape::before {
  border-right-color: var(--vz-warning);
  border-top-color: var(--vz-warning);
}
.ribbon-box.right .ribbon-warning.ribbon-shape::after {
  border-right-color: var(--vz-warning);
  border-bottom-color: var(--vz-warning);
}

.ribbon-two-warning span {
  background: var(--vz-warning);
}
.ribbon-two-warning span:before {
  border-left: 3px solid var(--vz-warning-text-emphasis);
  border-top: 3px solid var(--vz-warning-text-emphasis);
}
.ribbon-two-warning span:after {
  border-right: 3px solid var(--vz-warning-text-emphasis);
  border-top: 3px solid var(--vz-warning-text-emphasis);
}

.ribbon-three-danger span {
  background: var(--vz-danger);
}
.ribbon-three-danger span:before {
  background: var(--vz-danger);
}
.ribbon-three-danger span:after {
  background: var(--vz-danger-text-emphasis);
}
.ribbon-three-danger::after {
  border-top-color: var(--vz-danger);
}

.ribbon-box .ribbon-danger {
  background: var(--vz-danger);
}
.ribbon-box .ribbon-danger:before {
  border-color: var(--vz-danger-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-danger.ribbon-shape::before {
  border-left-color: var(--vz-danger);
  border-top-color: var(--vz-danger);
}
.ribbon-box .ribbon-danger.ribbon-shape::after {
  border-left-color: var(--vz-danger);
  border-bottom-color: var(--vz-danger);
}
.ribbon-box.right .ribbon-danger {
  background: var(--vz-danger);
}
.ribbon-box.right .ribbon-danger.ribbon-shape::before {
  border-right-color: var(--vz-danger);
  border-top-color: var(--vz-danger);
}
.ribbon-box.right .ribbon-danger.ribbon-shape::after {
  border-right-color: var(--vz-danger);
  border-bottom-color: var(--vz-danger);
}

.ribbon-two-danger span {
  background: var(--vz-danger);
}
.ribbon-two-danger span:before {
  border-left: 3px solid var(--vz-danger-text-emphasis);
  border-top: 3px solid var(--vz-danger-text-emphasis);
}
.ribbon-two-danger span:after {
  border-right: 3px solid var(--vz-danger-text-emphasis);
  border-top: 3px solid var(--vz-danger-text-emphasis);
}

.ribbon-three-light span {
  background: var(--vz-light);
}
.ribbon-three-light span:before {
  background: var(--vz-light);
}
.ribbon-three-light span:after {
  background: var(--vz-light-text-emphasis);
}
.ribbon-three-light::after {
  border-top-color: var(--vz-light);
}

.ribbon-box .ribbon-light {
  background: var(--vz-light);
}
.ribbon-box .ribbon-light:before {
  border-color: var(--vz-light-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-light.ribbon-shape::before {
  border-left-color: var(--vz-light);
  border-top-color: var(--vz-light);
}
.ribbon-box .ribbon-light.ribbon-shape::after {
  border-left-color: var(--vz-light);
  border-bottom-color: var(--vz-light);
}
.ribbon-box.right .ribbon-light {
  background: var(--vz-light);
}
.ribbon-box.right .ribbon-light.ribbon-shape::before {
  border-right-color: var(--vz-light);
  border-top-color: var(--vz-light);
}
.ribbon-box.right .ribbon-light.ribbon-shape::after {
  border-right-color: var(--vz-light);
  border-bottom-color: var(--vz-light);
}

.ribbon-two-light span {
  background: var(--vz-light);
}
.ribbon-two-light span:before {
  border-left: 3px solid var(--vz-light-text-emphasis);
  border-top: 3px solid var(--vz-light-text-emphasis);
}
.ribbon-two-light span:after {
  border-right: 3px solid var(--vz-light-text-emphasis);
  border-top: 3px solid var(--vz-light-text-emphasis);
}

.ribbon-three-dark span {
  background: var(--vz-dark);
}
.ribbon-three-dark span:before {
  background: var(--vz-dark);
}
.ribbon-three-dark span:after {
  background: var(--vz-dark-text-emphasis);
}
.ribbon-three-dark::after {
  border-top-color: var(--vz-dark);
}

.ribbon-box .ribbon-dark {
  background: var(--vz-dark);
}
.ribbon-box .ribbon-dark:before {
  border-color: var(--vz-dark-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-dark.ribbon-shape::before {
  border-left-color: var(--vz-dark);
  border-top-color: var(--vz-dark);
}
.ribbon-box .ribbon-dark.ribbon-shape::after {
  border-left-color: var(--vz-dark);
  border-bottom-color: var(--vz-dark);
}
.ribbon-box.right .ribbon-dark {
  background: var(--vz-dark);
}
.ribbon-box.right .ribbon-dark.ribbon-shape::before {
  border-right-color: var(--vz-dark);
  border-top-color: var(--vz-dark);
}
.ribbon-box.right .ribbon-dark.ribbon-shape::after {
  border-right-color: var(--vz-dark);
  border-bottom-color: var(--vz-dark);
}

.ribbon-two-dark span {
  background: var(--vz-dark);
}
.ribbon-two-dark span:before {
  border-left: 3px solid var(--vz-dark-text-emphasis);
  border-top: 3px solid var(--vz-dark-text-emphasis);
}
.ribbon-two-dark span:after {
  border-right: 3px solid var(--vz-dark-text-emphasis);
  border-top: 3px solid var(--vz-dark-text-emphasis);
}

.ribbon-three-pink span {
  background: var(--vz-pink);
}
.ribbon-three-pink span:before {
  background: var(--vz-pink);
}
.ribbon-three-pink span:after {
  background: var(--vz-pink-text-emphasis);
}
.ribbon-three-pink::after {
  border-top-color: var(--vz-pink);
}

.ribbon-box .ribbon-pink {
  background: var(--vz-pink);
}
.ribbon-box .ribbon-pink:before {
  border-color: var(--vz-pink-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-pink.ribbon-shape::before {
  border-left-color: var(--vz-pink);
  border-top-color: var(--vz-pink);
}
.ribbon-box .ribbon-pink.ribbon-shape::after {
  border-left-color: var(--vz-pink);
  border-bottom-color: var(--vz-pink);
}
.ribbon-box.right .ribbon-pink {
  background: var(--vz-pink);
}
.ribbon-box.right .ribbon-pink.ribbon-shape::before {
  border-right-color: var(--vz-pink);
  border-top-color: var(--vz-pink);
}
.ribbon-box.right .ribbon-pink.ribbon-shape::after {
  border-right-color: var(--vz-pink);
  border-bottom-color: var(--vz-pink);
}

.ribbon-two-pink span {
  background: var(--vz-pink);
}
.ribbon-two-pink span:before {
  border-left: 3px solid var(--vz-pink-text-emphasis);
  border-top: 3px solid var(--vz-pink-text-emphasis);
}
.ribbon-two-pink span:after {
  border-right: 3px solid var(--vz-pink-text-emphasis);
  border-top: 3px solid var(--vz-pink-text-emphasis);
}

.ribbon-three-purple span {
  background: var(--vz-purple);
}
.ribbon-three-purple span:before {
  background: var(--vz-purple);
}
.ribbon-three-purple span:after {
  background: var(--vz-purple-text-emphasis);
}
.ribbon-three-purple::after {
  border-top-color: var(--vz-purple);
}

.ribbon-box .ribbon-purple {
  background: var(--vz-purple);
}
.ribbon-box .ribbon-purple:before {
  border-color: var(--vz-purple-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-purple.ribbon-shape::before {
  border-left-color: var(--vz-purple);
  border-top-color: var(--vz-purple);
}
.ribbon-box .ribbon-purple.ribbon-shape::after {
  border-left-color: var(--vz-purple);
  border-bottom-color: var(--vz-purple);
}
.ribbon-box.right .ribbon-purple {
  background: var(--vz-purple);
}
.ribbon-box.right .ribbon-purple.ribbon-shape::before {
  border-right-color: var(--vz-purple);
  border-top-color: var(--vz-purple);
}
.ribbon-box.right .ribbon-purple.ribbon-shape::after {
  border-right-color: var(--vz-purple);
  border-bottom-color: var(--vz-purple);
}

.ribbon-two-purple span {
  background: var(--vz-purple);
}
.ribbon-two-purple span:before {
  border-left: 3px solid var(--vz-purple-text-emphasis);
  border-top: 3px solid var(--vz-purple-text-emphasis);
}
.ribbon-two-purple span:after {
  border-right: 3px solid var(--vz-purple-text-emphasis);
  border-top: 3px solid var(--vz-purple-text-emphasis);
}

.ribbon-three-orange span {
  background: var(--vz-orange);
}
.ribbon-three-orange span:before {
  background: var(--vz-orange);
}
.ribbon-three-orange span:after {
  background: var(--vz-orange-text-emphasis);
}
.ribbon-three-orange::after {
  border-top-color: var(--vz-orange);
}

.ribbon-box .ribbon-orange {
  background: var(--vz-orange);
}
.ribbon-box .ribbon-orange:before {
  border-color: var(--vz-orange-text-emphasis) transparent transparent;
}
.ribbon-box .ribbon-orange.ribbon-shape::before {
  border-left-color: var(--vz-orange);
  border-top-color: var(--vz-orange);
}
.ribbon-box .ribbon-orange.ribbon-shape::after {
  border-left-color: var(--vz-orange);
  border-bottom-color: var(--vz-orange);
}
.ribbon-box.right .ribbon-orange {
  background: var(--vz-orange);
}
.ribbon-box.right .ribbon-orange.ribbon-shape::before {
  border-right-color: var(--vz-orange);
  border-top-color: var(--vz-orange);
}
.ribbon-box.right .ribbon-orange.ribbon-shape::after {
  border-right-color: var(--vz-orange);
  border-bottom-color: var(--vz-orange);
}

.ribbon-two-orange span {
  background: var(--vz-orange);
}
.ribbon-two-orange span:before {
  border-left: 3px solid var(--vz-orange-text-emphasis);
  border-top: 3px solid var(--vz-orange-text-emphasis);
}
.ribbon-two-orange span:after {
  border-right: 3px solid var(--vz-orange-text-emphasis);
  border-top: 3px solid var(--vz-orange-text-emphasis);
}

.toast:not(:last-child) {
  margin-bottom: 0.75rem;
}

.toast-border-primary .toast-body {
  color: var(--vz-primary);
  border-bottom: 3px solid var(--vz-primary);
}

.toast-border-secondary .toast-body {
  color: var(--vz-secondary);
  border-bottom: 3px solid var(--vz-secondary);
}

.toast-border-success .toast-body {
  color: var(--vz-success);
  border-bottom: 3px solid var(--vz-success);
}

.toast-border-info .toast-body {
  color: var(--vz-info);
  border-bottom: 3px solid var(--vz-info);
}

.toast-border-warning .toast-body {
  color: var(--vz-warning);
  border-bottom: 3px solid var(--vz-warning);
}

.toast-border-danger .toast-body {
  color: var(--vz-danger);
  border-bottom: 3px solid var(--vz-danger);
}

.toast-border-light .toast-body {
  color: var(--vz-light);
  border-bottom: 3px solid var(--vz-light);
}

.toast-border-dark .toast-body {
  color: var(--vz-dark);
  border-bottom: 3px solid var(--vz-dark);
}

.toast-border-pink .toast-body {
  color: var(--vz-pink);
  border-bottom: 3px solid var(--vz-pink);
}

.toast-border-purple .toast-body {
  color: var(--vz-purple);
  border-bottom: 3px solid var(--vz-purple);
}

.toast-border-orange .toast-body {
  color: var(--vz-orange);
  border-bottom: 3px solid var(--vz-orange);
}

.scrollspy-example {
  position: relative;
  height: 200px;
  margin-top: 0.5rem;
  overflow: auto;
}

.scrollspy-example-2 {
  position: relative;
  height: 370px;
  overflow: auto;
}

:root {
  --vz-card-logo-dark: block;
  --vz-card-logo-light: none;
  --vz-vertical-menu-width: 320px;
  --vz-vertical-menu-width-md: 180px;
  --vz-vertical-menu-width-sm: 70px;
  --vz-vertical-menu-item-font-family: var(--vz-font-family-primary);
  --vz-vertical-menu-sub-item-font-family: var(--vz-font-family-primary);
  --vz-topnav-bg: #fff;
  --vz-topnav-item-color: rgb(108.8918918919, 112.0945945946, 128.1081081081);
  --vz-topnav-item-active-color: var(--vz-primary);
  --vz-twocolumn-menu-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-boxed-layout-width: 1300px;
  --vz-semibox-width: 110px;
  --vz-input-bg-custom: #fff;
  --vz-input-border-custom: #ced4da;
}

:root:is([data-sidebar=gradient], [data-sidebar=gradient-2], [data-sidebar=gradient-3], [data-sidebar=gradient-4]) {
  --vz-vertical-menu-item-color: rgba(255, 255, 255, 0.5);
  --vz-vertical-menu-item-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-item-hover-color: #fff;
  --vz-vertical-menu-item-active-color: #fff;
  --vz-vertical-menu-item-active-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-sub-item-color: rgba(255, 255, 255, 0.5);
  --vz-vertical-menu-sub-item-hover-color: #fff;
  --vz-vertical-menu-sub-item-active-color: #fff;
  --vz-vertical-menu-title-color: rgba(255, 255, 255, 0.5);
  --vz-vertical-menu-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-vertical-menu-dropdown-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-sidebar-user-bg: rgba(255, 255, 255, 0.08);
  --vz-sidebar-user-name-text: #fff;
  --vz-sidebar-user-name-sub-text: rgba(255, 255, 255, 0.5);
}

[data-bs-theme=dark] {
  --vz-card-logo-dark: none;
  --vz-card-logo-light: block;
  --vz-footer-bg: #212529;
  --vz-footer-color: #878a99;
  --vz-topnav-bg: #272b30;
  --vz-topnav-item-color: #878a99;
  --vz-topnav-item-active-color: #fff;
  --vz-page-title-box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  --vz-page-title-border: none;
  --vz-twocolumn-menu-box-shadow: 0 2px 4px rgba(15, 34, 58, 0.12);
  --vz-input-bg-custom: #262a2f;
  --vz-input-border-custom: #2a2f34;
}
[data-bs-theme=dark]:is([data-theme=default], [data-theme=minimal], [data-theme=material], [data-theme=modern], [data-theme=creative], [data-theme=saas], [data-theme=interactive], [data-theme=corporate], [data-theme=classic], [data-theme=vintage]) {
  --vz-light: rgb(39.74, 42.56, 46.32);
  --vz-light-rgb: 40, 43, 46;
  --vz-light-text-emphasis: rgb(83.25, 85.5, 88.5);
  --vz-light-bg-subtle: rgb(37.45, 40.3, 44.1);
  --vz-light-border-subtle: rgb(53.48, 56.12, 59.64);
  --vz-dark: rgb(48.9, 51.6, 55.2);
  --vz-dark-rgb: 49, 52, 55;
  --vz-dark-text-emphasis: rgb(94.7, 96.8, 99.6);
  --vz-dark-bg-subtle: rgb(48.9, 51.6, 55.2);
  --vz-dark-border-subtle: rgb(60.35, 62.9, 66.3);
  --vz-body-bg: #1a1d21;
  --vz-boxed-body-bg: rgb(17.0101694915, 18.9728813559, 21.5898305085);
}
[data-bs-theme=dark] .table-light {
  --vz-table-color: white;
  --vz-table-bg: var(--vz-light);
  --vz-table-border-color: var(--vz-border-color);
  --vz-table-striped-bg: var(--vz-light);
  --vz-table-striped-color: white;
  --vz-table-active-bg: var(--vz-light);
  --vz-table-active-color: white;
  --vz-table-hover-bg: var(--vz-light);
  --vz-table-hover-color: white;
}
[data-bs-theme=dark] .btn-light,
[data-bs-theme=dark] .btn-outline-light {
  --vz-btn-color: rgb(140.5, 142, 144);
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}
[data-bs-theme=dark] .btn-outline-dark,
[data-bs-theme=dark] .btn-soft-dark, [data-bs-theme=dark] .btn-ghost-dark {
  --vz-btn-color: #fff;
}
[data-bs-theme=dark] .btn-ghost-dark {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}
[data-bs-theme=dark] .dropdown-menu {
  --vz-dropdown-bg: #292e33;
  --vz-dropdown-link-color: #adb5bd;
  --vz-dropdown-link-hover-color: #b9bfc4;
  --vz-dropdown-link-hover-bg: #2f343a;
  --vz-dropdown-border-width: 1px;
  --vz-dropdown-link-active-color: #adb5bd;
  --vz-dropdown-link-active-bg: #2f343a;
}

[data-bs-theme=dark] {
  --vz-header-bg: #292e32;
  --vz-header-border: #292e32;
  --vz-header-item-color: #e9ecef;
  --vz-header-item-bg: #31363c;
  --vz-header-item-sub-color: #878a99;
  --vz-topbar-user-bg: #31373c;
  --vz-topbar-search-bg: #202328;
  --vz-topbar-search-color: #fff;
}
[data-bs-theme=dark][data-topbar=dark] {
  --vz-header-bg: var(--vz-primary);
  --vz-header-border: var(--vz-primary);
  --vz-header-item-color: rgba(255, 255, 255, 0.85);
  --vz-header-item-bg: rgba(var(--vz-white-rgb), 0.1);
  --vz-header-item-sub-color: #b0c4d9;
  --vz-topbar-user-bg: rgba(var(--vz-white-rgb), 0.1);
  --vz-topbar-search-bg: rgba(255, 255, 255, 0.05);
  --vz-topbar-search-color: #fff;
}

[data-bs-theme=dark] {
  --vz-vertical-menu-bg: #fff;
  --vz-vertical-menu-border: #fff;
  --vz-vertical-menu-item-color: rgb(108.8918918919, 112.0945945946, 128.1081081081);
  --vz-vertical-menu-item-bg: rgba(var(--vz-primary-rgb), 0.15);
  --vz-vertical-menu-item-hover-color: var(--vz-primary);
  --vz-vertical-menu-item-active-color: var(--vz-primary);
  --vz-vertical-menu-item-active-bg: rgba(var(--vz-primary-rgb), 0.15);
  --vz-vertical-menu-sub-item-color: rgb(123.972972973, 127.2486486486, 143.627027027);
  --vz-vertical-menu-sub-item-hover-color: var(--vz-primary);
  --vz-vertical-menu-sub-item-active-color: var(--vz-primary);
  --vz-vertical-menu-title-color: #919da9;
}
[data-bs-theme=dark][data-sidebar=dark] {
  --vz-vertical-menu-bg: #212529;
  --vz-vertical-menu-border: #212529;
  --vz-vertical-menu-item-color: #7c7f90;
  --vz-vertical-menu-item-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-item-hover-color: #fff;
  --vz-vertical-menu-item-active-color: #fff;
  --vz-vertical-menu-item-active-bg: rgba(255, 255, 255, 0.15);
  --vz-vertical-menu-sub-item-color: #7c7f90;
  --vz-vertical-menu-sub-item-hover-color: #fff;
  --vz-vertical-menu-sub-item-active-color: #fff;
  --vz-vertical-menu-title-color: #5f6270;
  --vz-twocolumn-menu-iconview-bg: #292e32;
}

html {
  position: relative;
  min-height: 100%;
}

label {
  font-weight: var(--vz-font-weight-medium);
  margin-bottom: 0.5rem;
  font-size: 0.975rem;
}

b,
strong {
  font-weight: var(--vz-font-weight-semibold);
}

.blockquote {
  padding: 10px 20px;
  border-left: 4px solid var(--vz-border-color);
}

.blockquote-reverse {
  border-left: 0;
  border-right: 4px solid var(--vz-border-color);
  text-align: right;
}

@media (min-width: 1200px) {
  :is(.container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl) {
    max-width: 1140px;
  }
}

.row > * {
  position: relative;
}

.alert-label-icon {
  position: relative;
  padding-left: 60px;
  border: 0;
}
.alert-label-icon .label-icon {
  position: absolute;
  width: 45px;
  height: 100%;
  left: 0;
  top: 0;
  background-color: rgba(255, 255, 255, 0.15);
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.alert-label-icon.label-arrow {
  overflow: hidden;
}
.alert-label-icon.label-arrow .label-icon:after {
  content: "";
  position: absolute;
  border: 6px solid transparent;
  border-left-color: var(--vz-primary);
  right: -12px;
}

.alert-border-left {
  border-left: 3px solid;
}

.alert-top-border {
  background-color: var(--vz-secondary-bg);
  border-color: var(--vz-border-color);
  border-top: 2px solid;
  color: var(--vz-body-color);
}

.alert-additional {
  padding: 0;
}
.alert-additional .alert-body {
  padding: 0.8rem 1rem;
}
.alert-additional .alert-content {
  padding: 0.8rem 1rem;
  border-bottom-left-radius: var(--vz-border-radius);
  border-bottom-right-radius: var(--vz-border-radius);
  margin: 0 -var(--vz-border-width) -var(--vz-border-width) -var(--vz-border-width);
}

.rounded-label .label-icon {
  width: 45px;
  height: 26px;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 0 30px 30px 0;
}

.alert-border-left.alert-primary {
  border-left-color: var(--vz-primary);
}

.alert-top-border.alert-primary {
  border-top-color: var(--vz-primary);
}

.rounded-label.alert-primary .label-icon {
  background-color: var(--vz-primary);
}

.label-arrow.alert-primary .label-icon {
  background-color: var(--vz-primary);
}
.label-arrow.alert-primary .label-icon:after {
  border-left-color: var(--vz-primary) !important;
}

.alert.alert-primary .btn-close::after {
  color: var(--vz-primary);
}

.alert-additional.alert-primary .alert-content {
  background-color: var(--vz-primary);
}

.alert-border-left.alert-secondary {
  border-left-color: var(--vz-secondary);
}

.alert-top-border.alert-secondary {
  border-top-color: var(--vz-secondary);
}

.rounded-label.alert-secondary .label-icon {
  background-color: var(--vz-secondary);
}

.label-arrow.alert-secondary .label-icon {
  background-color: var(--vz-secondary);
}
.label-arrow.alert-secondary .label-icon:after {
  border-left-color: var(--vz-secondary) !important;
}

.alert.alert-secondary .btn-close::after {
  color: var(--vz-secondary);
}

.alert-additional.alert-secondary .alert-content {
  background-color: var(--vz-secondary);
}

.alert-border-left.alert-success {
  border-left-color: var(--vz-success);
}

.alert-top-border.alert-success {
  border-top-color: var(--vz-success);
}

.rounded-label.alert-success .label-icon {
  background-color: var(--vz-success);
}

.label-arrow.alert-success .label-icon {
  background-color: var(--vz-success);
}
.label-arrow.alert-success .label-icon:after {
  border-left-color: var(--vz-success) !important;
}

.alert.alert-success .btn-close::after {
  color: var(--vz-success);
}

.alert-additional.alert-success .alert-content {
  background-color: var(--vz-success);
}

.alert-border-left.alert-info {
  border-left-color: var(--vz-info);
}

.alert-top-border.alert-info {
  border-top-color: var(--vz-info);
}

.rounded-label.alert-info .label-icon {
  background-color: var(--vz-info);
}

.label-arrow.alert-info .label-icon {
  background-color: var(--vz-info);
}
.label-arrow.alert-info .label-icon:after {
  border-left-color: var(--vz-info) !important;
}

.alert.alert-info .btn-close::after {
  color: var(--vz-info);
}

.alert-additional.alert-info .alert-content {
  background-color: var(--vz-info);
}

.alert-border-left.alert-warning {
  border-left-color: var(--vz-warning);
}

.alert-top-border.alert-warning {
  border-top-color: var(--vz-warning);
}

.rounded-label.alert-warning .label-icon {
  background-color: var(--vz-warning);
}

.label-arrow.alert-warning .label-icon {
  background-color: var(--vz-warning);
}
.label-arrow.alert-warning .label-icon:after {
  border-left-color: var(--vz-warning) !important;
}

.alert.alert-warning .btn-close::after {
  color: var(--vz-warning);
}

.alert-additional.alert-warning .alert-content {
  background-color: var(--vz-warning);
}

.alert-border-left.alert-danger {
  border-left-color: var(--vz-danger);
}

.alert-top-border.alert-danger {
  border-top-color: var(--vz-danger);
}

.rounded-label.alert-danger .label-icon {
  background-color: var(--vz-danger);
}

.label-arrow.alert-danger .label-icon {
  background-color: var(--vz-danger);
}
.label-arrow.alert-danger .label-icon:after {
  border-left-color: var(--vz-danger) !important;
}

.alert.alert-danger .btn-close::after {
  color: var(--vz-danger);
}

.alert-additional.alert-danger .alert-content {
  background-color: var(--vz-danger);
}

.alert-border-left.alert-light {
  border-left-color: var(--vz-light);
}

.alert-top-border.alert-light {
  border-top-color: var(--vz-light);
}

.rounded-label.alert-light .label-icon {
  background-color: var(--vz-light);
}

.label-arrow.alert-light .label-icon {
  background-color: var(--vz-light);
}
.label-arrow.alert-light .label-icon:after {
  border-left-color: var(--vz-light) !important;
}

.alert.alert-light .btn-close::after {
  color: var(--vz-light);
}

.alert-additional.alert-light .alert-content {
  background-color: var(--vz-light);
}

.alert-border-left.alert-dark {
  border-left-color: var(--vz-dark);
}

.alert-top-border.alert-dark {
  border-top-color: var(--vz-dark);
}

.rounded-label.alert-dark .label-icon {
  background-color: var(--vz-dark);
}

.label-arrow.alert-dark .label-icon {
  background-color: var(--vz-dark);
}
.label-arrow.alert-dark .label-icon:after {
  border-left-color: var(--vz-dark) !important;
}

.alert.alert-dark .btn-close::after {
  color: var(--vz-dark);
}

.alert-additional.alert-dark .alert-content {
  background-color: var(--vz-dark);
}

.alert-border-left.alert-pink {
  border-left-color: var(--vz-pink);
}

.alert-top-border.alert-pink {
  border-top-color: var(--vz-pink);
}

.rounded-label.alert-pink .label-icon {
  background-color: var(--vz-pink);
}

.label-arrow.alert-pink .label-icon {
  background-color: var(--vz-pink);
}
.label-arrow.alert-pink .label-icon:after {
  border-left-color: var(--vz-pink) !important;
}

.alert.alert-pink .btn-close::after {
  color: var(--vz-pink);
}

.alert-additional.alert-pink .alert-content {
  background-color: var(--vz-pink);
}

.alert-border-left.alert-purple {
  border-left-color: var(--vz-purple);
}

.alert-top-border.alert-purple {
  border-top-color: var(--vz-purple);
}

.rounded-label.alert-purple .label-icon {
  background-color: var(--vz-purple);
}

.label-arrow.alert-purple .label-icon {
  background-color: var(--vz-purple);
}
.label-arrow.alert-purple .label-icon:after {
  border-left-color: var(--vz-purple) !important;
}

.alert.alert-purple .btn-close::after {
  color: var(--vz-purple);
}

.alert-additional.alert-purple .alert-content {
  background-color: var(--vz-purple);
}

.alert-border-left.alert-orange {
  border-left-color: var(--vz-orange);
}

.alert-top-border.alert-orange {
  border-top-color: var(--vz-orange);
}

.rounded-label.alert-orange .label-icon {
  background-color: var(--vz-orange);
}

.label-arrow.alert-orange .label-icon {
  background-color: var(--vz-orange);
}
.label-arrow.alert-orange .label-icon:after {
  border-left-color: var(--vz-orange) !important;
}

.alert.alert-orange .btn-close::after {
  color: var(--vz-orange);
}

.alert-additional.alert-orange .alert-content {
  background-color: var(--vz-orange);
}

.alert-primary:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-secondary:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-success:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-info:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-warning:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-danger:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-light:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #000;
}

.alert-dark:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-pink:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-purple:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-orange:is(.rounded-label, .label-arrow, .alert-additional) :is(.label-icon, .alert-content) {
  color: #fff;
}

.alert-dismissible .btn-close {
  background: transparent !important;
}
.alert-dismissible .btn-close::after {
  background: transparent !important;
  content: "\f0156" !important;
  font-size: 18px;
  line-height: 15px;
  font-family: "Material Design Icons" !important;
}

.badge {
  line-height: 0.95;
  padding: 0.5rem;
  font-size: 0.875rem;
}

.badge-label.bg-primary:before {
  border-right-color: var(--vz-primary);
}

.badge-label.bg-secondary:before {
  border-right-color: var(--vz-secondary);
}

.badge-label.bg-success:before {
  border-right-color: var(--vz-success);
}

.badge-label.bg-info:before {
  border-right-color: var(--vz-info);
}

.badge-label.bg-warning:before {
  border-right-color: var(--vz-warning);
}

.badge-label.bg-danger:before {
  border-right-color: var(--vz-danger);
}

.badge-label.bg-light:before {
  border-right-color: var(--vz-light);
}

.badge-label.bg-dark:before {
  border-right-color: var(--vz-dark);
}

.badge-label.bg-pink:before {
  border-right-color: var(--vz-pink);
}

.badge-label.bg-purple:before {
  border-right-color: var(--vz-purple);
}

.badge-label.bg-orange:before {
  border-right-color: var(--vz-orange);
}

.badge-border {
  border-left: 2px solid;
}

.badge-label {
  margin-left: 8px;
  position: relative;
}
.badge-label:before {
  content: "";
  position: absolute;
  border: 8px solid transparent;
  border-right-color: var(--vz-primary);
  left: -14px;
  top: 0;
}

.badge-gradient-primary {
  background: linear-gradient(135deg, var(--vz-primary) 0%, var(--vz-success) 100%);
}

.badge-gradient-secondary {
  background: linear-gradient(135deg, var(--vz-secondary) 0%, var(--vz-info) 100%);
}

.badge-gradient-success {
  background: linear-gradient(135deg, var(--vz-success) 0%, #ffaf00 100%);
}

.badge-gradient-danger {
  background: linear-gradient(135deg, var(--vz-danger) 0%, var(--vz-secondary) 100%);
}

.badge-gradient-warning {
  background: linear-gradient(135deg, #ffaf00 0%, rgb(178.5, 122.5, 0) 100%);
}

.badge-gradient-info {
  background: linear-gradient(135deg, var(--vz-info) 0%, var(--vz-success) 100%);
}

.badge-gradient-dark {
  background: linear-gradient(135deg, #212529 0%, var(--vz-primary) 100%);
}

button,
a {
  outline: none !important;
}

.btn {
  box-shadow: var(--vz-element-shadow);
  font-size: var(--vz-font-base);
  font-weight: 500;
}

:is(.btn.show, .btn:first-child:active, :not(.btn-check) + .btn:active) {
  border-color: transparent;
}

.btn-icon {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: calc(1rem + 1.5em + 2px);
  width: calc(1rem + 1.5em + 2px);
  padding: 0;
}
.btn-icon :is(i, svg, img) {
  vertical-align: middle;
}
.btn-icon.btn-sm {
  height: calc(0.5rem + 1.5em + 2px);
  width: calc(0.5rem + 1.5em + 2px);
}
.btn-icon.btn-lg {
  height: calc(1.4rem + 1.5em + 2px);
  width: calc(1.4rem + 1.5em + 2px);
}

.btn-primary {
  --vz-btn-bg: var(--vz-primary);
  --vz-btn-border-color: var(--vz-primary);
  --vz-btn-hover-bg: var(--vz-primary-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-primary-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-primary-rgb);
  --vz-btn-active-bg: var(--vz-primary-text-emphasis);
  --vz-btn-active-border-color: var(--vz-primary-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-primary);
  --vz-btn-disabled-border-color: var(--vz-primary);
}

.btn-outline-primary {
  --vz-btn-color: var(--vz-primary);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-primary);
  --vz-btn-hover-bg: var(--vz-primary);
  --vz-btn-hover-border-color: var(--vz-primary);
  --vz-btn-focus-shadow-rgb: var(--vz-primary-rgb);
  --vz-btn-active-bg: var(--vz-primary);
  --vz-btn-active-border-color: var(--vz-primary);
}

.btn-soft-primary {
  --vz-btn-color: var(--vz-primary);
  --vz-btn-bg: var(--vz-primary-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-primary);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-primary-rgb);
  --vz-btn-active-bg: var(--vz-primary);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-primary {
  --vz-btn-color: var(--vz-primary);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-primary);
  --vz-btn-hover-bg: var(--vz-primary-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-primary-rgb);
  --vz-btn-active-color: var(--vz-primary);
  --vz-btn-active-bg: var(--vz-primary-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-secondary {
  --vz-btn-bg: var(--vz-secondary);
  --vz-btn-border-color: var(--vz-secondary);
  --vz-btn-hover-bg: var(--vz-secondary-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-secondary-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-secondary-rgb);
  --vz-btn-active-bg: var(--vz-secondary-text-emphasis);
  --vz-btn-active-border-color: var(--vz-secondary-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-secondary);
  --vz-btn-disabled-border-color: var(--vz-secondary);
}

.btn-outline-secondary {
  --vz-btn-color: var(--vz-secondary);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-secondary);
  --vz-btn-hover-bg: var(--vz-secondary);
  --vz-btn-hover-border-color: var(--vz-secondary);
  --vz-btn-focus-shadow-rgb: var(--vz-secondary-rgb);
  --vz-btn-active-bg: var(--vz-secondary);
  --vz-btn-active-border-color: var(--vz-secondary);
}

.btn-soft-secondary {
  --vz-btn-color: var(--vz-secondary);
  --vz-btn-bg: var(--vz-secondary-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-secondary);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-secondary-rgb);
  --vz-btn-active-bg: var(--vz-secondary);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-secondary {
  --vz-btn-color: var(--vz-secondary);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-secondary);
  --vz-btn-hover-bg: var(--vz-secondary-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-secondary-rgb);
  --vz-btn-active-color: var(--vz-secondary);
  --vz-btn-active-bg: var(--vz-secondary-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-success {
  --vz-btn-bg: var(--vz-success);
  --vz-btn-border-color: var(--vz-success);
  --vz-btn-hover-bg: var(--vz-success-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-success-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-success-rgb);
  --vz-btn-active-bg: var(--vz-success-text-emphasis);
  --vz-btn-active-border-color: var(--vz-success-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-success);
  --vz-btn-disabled-border-color: var(--vz-success);
}

.btn-outline-success {
  --vz-btn-color: var(--vz-success);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-success);
  --vz-btn-hover-bg: var(--vz-success);
  --vz-btn-hover-border-color: var(--vz-success);
  --vz-btn-focus-shadow-rgb: var(--vz-success-rgb);
  --vz-btn-active-bg: var(--vz-success);
  --vz-btn-active-border-color: var(--vz-success);
}

.btn-soft-success {
  --vz-btn-color: var(--vz-success);
  --vz-btn-bg: var(--vz-success-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-success);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-success-rgb);
  --vz-btn-active-bg: var(--vz-success);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-success {
  --vz-btn-color: var(--vz-success);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-success);
  --vz-btn-hover-bg: var(--vz-success-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-success-rgb);
  --vz-btn-active-color: var(--vz-success);
  --vz-btn-active-bg: var(--vz-success-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-info {
  --vz-btn-bg: var(--vz-info);
  --vz-btn-border-color: var(--vz-info);
  --vz-btn-hover-bg: var(--vz-info-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-info-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-info-rgb);
  --vz-btn-active-bg: var(--vz-info-text-emphasis);
  --vz-btn-active-border-color: var(--vz-info-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-info);
  --vz-btn-disabled-border-color: var(--vz-info);
}

.btn-outline-info {
  --vz-btn-color: var(--vz-info);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-info);
  --vz-btn-hover-bg: var(--vz-info);
  --vz-btn-hover-border-color: var(--vz-info);
  --vz-btn-focus-shadow-rgb: var(--vz-info-rgb);
  --vz-btn-active-bg: var(--vz-info);
  --vz-btn-active-border-color: var(--vz-info);
}

.btn-soft-info {
  --vz-btn-color: var(--vz-info);
  --vz-btn-bg: var(--vz-info-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-info);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-info-rgb);
  --vz-btn-active-bg: var(--vz-info);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-info {
  --vz-btn-color: var(--vz-info);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-info);
  --vz-btn-hover-bg: var(--vz-info-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-info-rgb);
  --vz-btn-active-color: var(--vz-info);
  --vz-btn-active-bg: var(--vz-info-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-warning {
  --vz-btn-bg: var(--vz-warning);
  --vz-btn-border-color: var(--vz-warning);
  --vz-btn-hover-bg: var(--vz-warning-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-warning-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-warning-rgb);
  --vz-btn-active-bg: var(--vz-warning-text-emphasis);
  --vz-btn-active-border-color: var(--vz-warning-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-warning);
  --vz-btn-disabled-border-color: var(--vz-warning);
}

.btn-outline-warning {
  --vz-btn-color: var(--vz-warning);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-warning);
  --vz-btn-hover-bg: var(--vz-warning);
  --vz-btn-hover-border-color: var(--vz-warning);
  --vz-btn-focus-shadow-rgb: var(--vz-warning-rgb);
  --vz-btn-active-bg: var(--vz-warning);
  --vz-btn-active-border-color: var(--vz-warning);
}

.btn-soft-warning {
  --vz-btn-color: var(--vz-warning);
  --vz-btn-bg: var(--vz-warning-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-warning);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-warning-rgb);
  --vz-btn-active-bg: var(--vz-warning);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-warning {
  --vz-btn-color: var(--vz-warning);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-warning);
  --vz-btn-hover-bg: var(--vz-warning-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-warning-rgb);
  --vz-btn-active-color: var(--vz-warning);
  --vz-btn-active-bg: var(--vz-warning-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-danger {
  --vz-btn-bg: var(--vz-danger);
  --vz-btn-border-color: var(--vz-danger);
  --vz-btn-hover-bg: var(--vz-danger-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-danger-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-danger-rgb);
  --vz-btn-active-bg: var(--vz-danger-text-emphasis);
  --vz-btn-active-border-color: var(--vz-danger-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-danger);
  --vz-btn-disabled-border-color: var(--vz-danger);
}

.btn-outline-danger {
  --vz-btn-color: var(--vz-danger);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-danger);
  --vz-btn-hover-bg: var(--vz-danger);
  --vz-btn-hover-border-color: var(--vz-danger);
  --vz-btn-focus-shadow-rgb: var(--vz-danger-rgb);
  --vz-btn-active-bg: var(--vz-danger);
  --vz-btn-active-border-color: var(--vz-danger);
}

.btn-soft-danger {
  --vz-btn-color: var(--vz-danger);
  --vz-btn-bg: var(--vz-danger-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-danger);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-danger-rgb);
  --vz-btn-active-bg: var(--vz-danger);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-danger {
  --vz-btn-color: var(--vz-danger);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-danger);
  --vz-btn-hover-bg: var(--vz-danger-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-danger-rgb);
  --vz-btn-active-color: var(--vz-danger);
  --vz-btn-active-bg: var(--vz-danger-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-light {
  --vz-btn-bg: var(--vz-light);
  --vz-btn-border-color: var(--vz-light);
  --vz-btn-hover-bg: var(--vz-light-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-light-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-light-rgb);
  --vz-btn-active-bg: var(--vz-light-text-emphasis);
  --vz-btn-active-border-color: var(--vz-light-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-light);
  --vz-btn-disabled-border-color: var(--vz-light);
}

.btn-outline-light {
  --vz-btn-color: var(--vz-light);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-light);
  --vz-btn-hover-bg: var(--vz-light);
  --vz-btn-hover-border-color: var(--vz-light);
  --vz-btn-focus-shadow-rgb: var(--vz-light-rgb);
  --vz-btn-active-bg: var(--vz-light);
  --vz-btn-active-border-color: var(--vz-light);
}

.btn-soft-light {
  --vz-btn-color: var(--vz-light);
  --vz-btn-bg: var(--vz-light-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-light);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-light-rgb);
  --vz-btn-active-bg: var(--vz-light);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-light {
  --vz-btn-color: var(--vz-light);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-light);
  --vz-btn-hover-bg: var(--vz-light-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-light-rgb);
  --vz-btn-active-color: var(--vz-light);
  --vz-btn-active-bg: var(--vz-light-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-dark {
  --vz-btn-bg: var(--vz-dark);
  --vz-btn-border-color: var(--vz-dark);
  --vz-btn-hover-bg: var(--vz-dark-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-dark-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-dark-rgb);
  --vz-btn-active-bg: var(--vz-dark-text-emphasis);
  --vz-btn-active-border-color: var(--vz-dark-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-dark);
  --vz-btn-disabled-border-color: var(--vz-dark);
}

.btn-outline-dark {
  --vz-btn-color: var(--vz-dark);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-dark);
  --vz-btn-hover-bg: var(--vz-dark);
  --vz-btn-hover-border-color: var(--vz-dark);
  --vz-btn-focus-shadow-rgb: var(--vz-dark-rgb);
  --vz-btn-active-bg: var(--vz-dark);
  --vz-btn-active-border-color: var(--vz-dark);
}

.btn-soft-dark {
  --vz-btn-color: var(--vz-dark);
  --vz-btn-bg: var(--vz-dark-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-dark);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-dark-rgb);
  --vz-btn-active-bg: var(--vz-dark);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-dark {
  --vz-btn-color: var(--vz-dark);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-dark);
  --vz-btn-hover-bg: var(--vz-dark-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-dark-rgb);
  --vz-btn-active-color: var(--vz-dark);
  --vz-btn-active-bg: var(--vz-dark-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-pink {
  --vz-btn-bg: var(--vz-pink);
  --vz-btn-border-color: var(--vz-pink);
  --vz-btn-hover-bg: var(--vz-pink-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-pink-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-pink-rgb);
  --vz-btn-active-bg: var(--vz-pink-text-emphasis);
  --vz-btn-active-border-color: var(--vz-pink-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-pink);
  --vz-btn-disabled-border-color: var(--vz-pink);
}

.btn-outline-pink {
  --vz-btn-color: var(--vz-pink);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-pink);
  --vz-btn-hover-bg: var(--vz-pink);
  --vz-btn-hover-border-color: var(--vz-pink);
  --vz-btn-focus-shadow-rgb: var(--vz-pink-rgb);
  --vz-btn-active-bg: var(--vz-pink);
  --vz-btn-active-border-color: var(--vz-pink);
}

.btn-soft-pink {
  --vz-btn-color: var(--vz-pink);
  --vz-btn-bg: var(--vz-pink-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-pink);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-pink-rgb);
  --vz-btn-active-bg: var(--vz-pink);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-pink {
  --vz-btn-color: var(--vz-pink);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-pink);
  --vz-btn-hover-bg: var(--vz-pink-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-pink-rgb);
  --vz-btn-active-color: var(--vz-pink);
  --vz-btn-active-bg: var(--vz-pink-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-purple {
  --vz-btn-bg: var(--vz-purple);
  --vz-btn-border-color: var(--vz-purple);
  --vz-btn-hover-bg: var(--vz-purple-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-purple-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-purple-rgb);
  --vz-btn-active-bg: var(--vz-purple-text-emphasis);
  --vz-btn-active-border-color: var(--vz-purple-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-purple);
  --vz-btn-disabled-border-color: var(--vz-purple);
}

.btn-outline-purple {
  --vz-btn-color: var(--vz-purple);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-purple);
  --vz-btn-hover-bg: var(--vz-purple);
  --vz-btn-hover-border-color: var(--vz-purple);
  --vz-btn-focus-shadow-rgb: var(--vz-purple-rgb);
  --vz-btn-active-bg: var(--vz-purple);
  --vz-btn-active-border-color: var(--vz-purple);
}

.btn-soft-purple {
  --vz-btn-color: var(--vz-purple);
  --vz-btn-bg: var(--vz-purple-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-purple);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-purple-rgb);
  --vz-btn-active-bg: var(--vz-purple);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-purple {
  --vz-btn-color: var(--vz-purple);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-purple);
  --vz-btn-hover-bg: var(--vz-purple-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-purple-rgb);
  --vz-btn-active-color: var(--vz-purple);
  --vz-btn-active-bg: var(--vz-purple-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-orange {
  --vz-btn-bg: var(--vz-orange);
  --vz-btn-border-color: var(--vz-orange);
  --vz-btn-hover-bg: var(--vz-orange-text-emphasis);
  --vz-btn-hover-border-color: var(--vz-orange-text-emphasis);
  --vz-btn-focus-shadow-rgb: var(--vz-orange-rgb);
  --vz-btn-active-bg: var(--vz-orange-text-emphasis);
  --vz-btn-active-border-color: var(--vz-orange-text-emphasis);
  --vz-btn-disabled-bg: var(--vz-orange);
  --vz-btn-disabled-border-color: var(--vz-orange);
}

.btn-outline-orange {
  --vz-btn-color: var(--vz-orange);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: var(--vz-orange);
  --vz-btn-hover-bg: var(--vz-orange);
  --vz-btn-hover-border-color: var(--vz-orange);
  --vz-btn-focus-shadow-rgb: var(--vz-orange-rgb);
  --vz-btn-active-bg: var(--vz-orange);
  --vz-btn-active-border-color: var(--vz-orange);
}

.btn-soft-orange {
  --vz-btn-color: var(--vz-orange);
  --vz-btn-bg: var(--vz-orange-bg-subtle);
  --vz-btn-border-color: transparent;
  --vz-btn-hover-bg: var(--vz-orange);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-orange-rgb);
  --vz-btn-active-bg: var(--vz-orange);
  --vz-btn-active-border-color: transparent;
}

.btn-ghost-orange {
  --vz-btn-color: var(--vz-orange);
  --vz-btn-bg: transparent;
  --vz-btn-border-color: transparent;
  --vz-btn-hover-color: var(--vz-orange);
  --vz-btn-hover-bg: var(--vz-orange-bg-subtle);
  --vz-btn-hover-border-color: transparent;
  --vz-btn-focus-shadow-rgb: var(--vz-orange-rgb);
  --vz-btn-active-color: var(--vz-orange);
  --vz-btn-active-bg: var(--vz-orange-bg-subtle);
  --vz-btn-active-border-color: transparent;
}

.btn-label {
  position: relative;
  padding-left: 44px;
}
.btn-label .label-icon {
  position: absolute;
  width: 35.5px;
  left: calc(var(--vz-border-width) * -1);
  top: calc(var(--vz-border-width) * -1);
  bottom: calc(var(--vz-border-width) * -1);
  background-color: rgba(255, 255, 255, 0.1);
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-label.btn-light .label-icon {
  background-color: rgba(var(--vz-dark-rgb), 0.05);
}
.btn-label.right {
  padding-left: 0.9rem;
  padding-right: 44px;
}
.btn-label.right .label-icon {
  right: calc(var(--vz-border-width) * -1);
  left: auto;
}

.btn-animation {
  overflow: hidden;
  transition: border-color 0.3s, background-color 0.3s;
  transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.btn-animation::after {
  content: attr(data-text);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  transform: translate3d(0, 25%, 0);
  padding: 0.5rem 0.9rem;
  transition: transform 0.3s, opacity 0.3s;
  transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.btn-animation > span {
  display: block;
  transition: transform 0.3s, opacity 0.3s;
  transition-timing-function: cubic-bezier(0.2, 1, 0.3, 1);
}
.btn-animation:hover::after {
  opacity: 1;
  transform: translate3d(0, 0, 0);
}
.btn-animation:hover > span {
  opacity: 0;
  transform: translate3d(0, -25%, 0);
}

.btn-primary.btn-animation {
  background-color: var(--vz-primary);
  border-color: var(--vz-primary) !important;
}
.btn-primary.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-primary);
  background-color: rgba(var(--vz-primary-rgb), 0.1);
}

.btn-secondary.btn-animation {
  background-color: var(--vz-secondary);
  border-color: var(--vz-secondary) !important;
}
.btn-secondary.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-secondary);
  background-color: rgba(var(--vz-secondary-rgb), 0.1);
}

.btn-success.btn-animation {
  background-color: var(--vz-success);
  border-color: var(--vz-success) !important;
}
.btn-success.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-success);
  background-color: rgba(var(--vz-success-rgb), 0.1);
}

.btn-info.btn-animation {
  background-color: var(--vz-info);
  border-color: var(--vz-info) !important;
}
.btn-info.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-info);
  background-color: rgba(var(--vz-info-rgb), 0.1);
}

.btn-warning.btn-animation {
  background-color: var(--vz-warning);
  border-color: var(--vz-warning) !important;
}
.btn-warning.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-warning);
  background-color: rgba(var(--vz-warning-rgb), 0.1);
}

.btn-danger.btn-animation {
  background-color: var(--vz-danger);
  border-color: var(--vz-danger) !important;
}
.btn-danger.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-danger);
  background-color: rgba(var(--vz-danger-rgb), 0.1);
}

.btn-light.btn-animation {
  background-color: var(--vz-light);
  border-color: var(--vz-light) !important;
}
.btn-light.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-light);
  background-color: rgba(var(--vz-light-rgb), 0.1);
}

.btn-dark.btn-animation {
  background-color: var(--vz-dark);
  border-color: var(--vz-dark) !important;
}
.btn-dark.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-dark);
  background-color: rgba(var(--vz-dark-rgb), 0.1);
}

.btn-pink.btn-animation {
  background-color: var(--vz-pink);
  border-color: var(--vz-pink) !important;
}
.btn-pink.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-pink);
  background-color: rgba(var(--vz-pink-rgb), 0.1);
}

.btn-purple.btn-animation {
  background-color: var(--vz-purple);
  border-color: var(--vz-purple) !important;
}
.btn-purple.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-purple);
  background-color: rgba(var(--vz-purple-rgb), 0.1);
}

.btn-orange.btn-animation {
  background-color: var(--vz-orange);
  border-color: var(--vz-orange) !important;
}
.btn-orange.btn-animation:is(:hover, :focus, :active, :focus-visible) {
  color: var(--vz-orange);
  background-color: rgba(var(--vz-orange-rgb), 0.1);
}

.btn-group-vertical label {
  margin-bottom: 0;
}

.btn-group.radio .btn {
  border: none;
}
.btn-group.radio .btn-check:active + .btn-light,
.btn-group.radio .btn-check:checked + .btn-light,
.btn-group.radio .btn-light.active,
.btn-group.radio .btn-light.dropdown-toggle.show,
.btn-group.radio .btn-light:active {
  background-color: rgba(var(--vz-info-rgb), 0.2);
  color: var(--vz-info);
}

.btn-load .spinner-border,
.btn-load .spinner-grow {
  height: 19px;
  width: 19px;
}

:is(.btn-primary, .btn-outline-primary, .btn-soft-primary).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-primary), #2e2929 20%);
}

:is(.btn-soft-primary, .btn-outline-primary) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-secondary, .btn-outline-secondary, .btn-soft-secondary).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-secondary), #2e2929 20%);
}

:is(.btn-soft-secondary, .btn-outline-secondary) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-success, .btn-outline-success, .btn-soft-success).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-success), #2e2929 20%);
}

:is(.btn-soft-success, .btn-outline-success) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-info, .btn-outline-info, .btn-soft-info).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-info), #2e2929 20%);
}

:is(.btn-soft-info, .btn-outline-info) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-warning, .btn-outline-warning, .btn-soft-warning).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-warning), #2e2929 20%);
}

:is(.btn-soft-warning, .btn-outline-warning) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-danger, .btn-outline-danger, .btn-soft-danger).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-danger), #2e2929 20%);
}

:is(.btn-soft-danger, .btn-outline-danger) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-light, .btn-outline-light, .btn-soft-light).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-light), #2e2929 20%);
}

:is(.btn-soft-light, .btn-outline-light) {
  --vz-btn-active-color: #000;
  --vz-btn-hover-color: #000;
}

:is(.btn-dark, .btn-outline-dark, .btn-soft-dark).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-dark), #2e2929 20%);
}

:is(.btn-soft-dark, .btn-outline-dark) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-pink, .btn-outline-pink, .btn-soft-pink).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-pink), #2e2929 20%);
}

:is(.btn-soft-pink, .btn-outline-pink) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-purple, .btn-outline-purple, .btn-soft-purple).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-purple), #2e2929 20%);
}

:is(.btn-soft-purple, .btn-outline-purple) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

:is(.btn-orange, .btn-outline-orange, .btn-soft-orange).btn-border {
  border-bottom: 2px solid color-mix(in srgb, var(--vz-orange), #2e2929 20%);
}

:is(.btn-soft-orange, .btn-outline-orange) {
  --vz-btn-active-color: #fff;
  --vz-btn-hover-color: #fff;
}

.custom-toggle .icon-off {
  display: none;
}
.custom-toggle.active .icon-on {
  display: none;
}
.custom-toggle.active .icon-off {
  display: block;
}

.btn-loading {
  color: transparent !important;
  pointer-events: none;
  position: relative;
  text-shadow: none !important;
}

.btn-loading > * {
  opacity: 0;
}

.btn-loading:after {
  --vz-btn-icon-size: 1.5rem;
  animation: spinner-border 0.75s linear infinite;
  border-right-color: currentcolor;
  border: 2px var(--vz-border-style);
  border-radius: 100rem;
  border-right: 2px var(--vz-border-style) transparent;
  color: var(--vz-btn-color);
  content: "";
  display: inline-block;
  height: var(--vz-btn-icon-size);
  left: calc(50% - var(--vz-btn-icon-size) / 2);
  position: absolute;
  top: calc(50% - var(--vz-btn-icon-size) / 2);
  vertical-align: text-bottom;
  width: var(--vz-btn-icon-size);
}

.btn-action {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

.breadcrumb-item > a {
  color: var(--vz-body-color);
}
.breadcrumb-item + .breadcrumb-item::before {
  font-family: "remixicon";
  font-size: 15px;
  line-height: 20px;
  content: "/";
}

.card {
  margin-bottom: var(--vz-grid-gutter-width);
  box-shadow: var(--vz-card-shadow);
}

.card-header {
  border-bottom: var(--vz-card-header-border-width) solid var(--vz-border-color);
}

.card-header-dropdown .dropdown-btn {
  padding: 1rem 0;
}

.card-footer {
  border-top: 1px solid var(--vz-border-color);
}

.card-title {
  font-size: 16px;
  margin: 0 0 7px 0;
}

.card-height-100 {
  height: calc(100% - var(--vz-grid-gutter-width));
}

.card-animate {
  transition: all 0.4s;
}
.card-animate:hover {
  transform: translateY(calc(var(--vz-grid-gutter-width) * -0.15));
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
}

.card-primary {
  color: #fff;
}
.card-primary .card-header, .card-primary .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-primary .card-title {
  color: #fff;
}

.card-secondary {
  color: #fff;
}
.card-secondary .card-header, .card-secondary .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-secondary .card-title {
  color: #fff;
}

.card-success {
  color: #fff;
}
.card-success .card-header, .card-success .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-success .card-title {
  color: #fff;
}

.card-info {
  color: #fff;
}
.card-info .card-header, .card-info .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-info .card-title {
  color: #fff;
}

.card-warning {
  color: #fff;
}
.card-warning .card-header, .card-warning .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-warning .card-title {
  color: #fff;
}

.card-danger {
  color: #fff;
}
.card-danger .card-header, .card-danger .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-danger .card-title {
  color: #fff;
}

.card-light {
  color: #000;
}
.card-light .card-header, .card-light .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #000;
  border-color: transparent;
}
.card-light .card-title {
  color: #000;
}

.card-dark {
  color: #fff;
}
.card-dark .card-header, .card-dark .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-dark .card-title {
  color: #fff;
}

.card-pink {
  color: #fff;
}
.card-pink .card-header, .card-pink .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-pink .card-title {
  color: #fff;
}

.card-purple {
  color: #fff;
}
.card-purple .card-header, .card-purple .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-purple .card-title {
  color: #fff;
}

.card-orange {
  color: #fff;
}
.card-orange .card-header, .card-orange .card-footer {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}
.card-orange .card-title {
  color: #fff;
}

.card-primary {
  background-color: var(--vz-primary);
}

.card-border-primary {
  border-color: var(--vz-primary) !important;
}
.card-border-primary .card-header, .card-border-primary .card-footer {
  border-color: var(--vz-primary);
}

.card-secondary {
  background-color: var(--vz-secondary);
}

.card-border-secondary {
  border-color: var(--vz-secondary) !important;
}
.card-border-secondary .card-header, .card-border-secondary .card-footer {
  border-color: var(--vz-secondary);
}

.card-success {
  background-color: var(--vz-success);
}

.card-border-success {
  border-color: var(--vz-success) !important;
}
.card-border-success .card-header, .card-border-success .card-footer {
  border-color: var(--vz-success);
}

.card-info {
  background-color: var(--vz-info);
}

.card-border-info {
  border-color: var(--vz-info) !important;
}
.card-border-info .card-header, .card-border-info .card-footer {
  border-color: var(--vz-info);
}

.card-warning {
  background-color: var(--vz-warning);
}

.card-border-warning {
  border-color: var(--vz-warning) !important;
}
.card-border-warning .card-header, .card-border-warning .card-footer {
  border-color: var(--vz-warning);
}

.card-danger {
  background-color: var(--vz-danger);
}

.card-border-danger {
  border-color: var(--vz-danger) !important;
}
.card-border-danger .card-header, .card-border-danger .card-footer {
  border-color: var(--vz-danger);
}

.card-light {
  background-color: var(--vz-light);
}

.card-border-light {
  border-color: var(--vz-light) !important;
}
.card-border-light .card-header, .card-border-light .card-footer {
  border-color: var(--vz-light);
}

.card-dark {
  background-color: var(--vz-dark);
}

.card-border-dark {
  border-color: var(--vz-dark) !important;
}
.card-border-dark .card-header, .card-border-dark .card-footer {
  border-color: var(--vz-dark);
}

.card-pink {
  background-color: var(--vz-pink);
}

.card-border-pink {
  border-color: var(--vz-pink) !important;
}
.card-border-pink .card-header, .card-border-pink .card-footer {
  border-color: var(--vz-pink);
}

.card-purple {
  background-color: var(--vz-purple);
}

.card-border-purple {
  border-color: var(--vz-purple) !important;
}
.card-border-purple .card-header, .card-border-purple .card-footer {
  border-color: var(--vz-purple);
}

.card-orange {
  background-color: var(--vz-orange);
}

.card-border-orange {
  border-color: var(--vz-orange) !important;
}
.card-border-orange .card-header, .card-border-orange .card-footer {
  border-color: var(--vz-orange);
}

.card-light {
  background-color: var(--vz-light);
}
.card-light .card-header, .card-light .card-footer {
  color: var(--vz-body-color) !important;
  background-color: rgba(var(--vz-dark-rgb), 0.1);
}
.card-light .card-title, .card-light .card-text {
  color: var(--vz-body-color) !important;
}

.card-preloader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--vz-light-rgb), 0.6);
  z-index: 9999;
}

.card-status {
  width: 40px;
  height: 40px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) !important;
}

.custom-loader {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
.card-overlay {
  position: relative;
  overflow: hidden;
}
.card-overlay:before {
  content: "";
  background-color: rgba(var(--vz-primary-rgb), 0.2);
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}
.card-overlay :is(.card-header, .card-footer) {
  border-color: rgba(255, 255, 255, 0.15) !important;
}

.card-toolbar-menu {
  line-height: 0.8;
}
.card-toolbar-menu a {
  font-size: 17px;
}
.card-toolbar-menu .minimize-card .plus {
  display: none;
}
.card-toolbar-menu .minimize-card .minus {
  display: block;
}
.card-toolbar-menu .minimize-card.collapsed .plus {
  display: block;
}
.card-toolbar-menu .minimize-card.collapsed .minus {
  display: none;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  font-size: 15px;
  line-height: 15px;
  content: "\f0140";
  font-family: "Material Design Icons";
}

.dropdown-menu {
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  animation-name: DropDownSlide;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  position: absolute;
  z-index: 1000;
}
.dropdown-menu.dropdown-megamenu {
  padding: 20px;
  left: 0 !important;
  right: 0 !important;
}
.dropdown-menu[data-popper-placement=top-start] {
  animation-name: DropDownSlideDown;
}

@keyframes DropDownSlide {
  100% {
    margin-top: -1px;
  }
  0% {
    margin-top: 8px;
  }
}
@keyframes DropDownSlideDown {
  100% {
    margin-bottom: 0;
  }
  0% {
    margin-bottom: 8px;
  }
}
@media (min-width: 600px) {
  .dropdown-menu-xl {
    width: 420px;
  }
  .dropdown-menu-lg {
    width: 320px;
  }
  .dropdown-menu-md {
    width: 240px;
  }
}
.dropdown-toggle-split {
  border-left: none;
}
.dropdown-toggle-split::after {
  margin-left: 0px;
}
.dropdown-toggle-split:before {
  content: "";
  position: absolute;
  background-color: rgba(255, 255, 255, 0.12);
  top: calc(var(--vz-border-width) * -1);
  bottom: calc(var(--vz-border-width) * -1);
  right: calc(var(--vz-border-width) * -1);
  left: 0;
  border-radius: 0 var(--vz-border-radius) var(--vz-border-radius) 0;
}

.dropdown-mega {
  position: static !important;
}

.dropdown-mega-menu-xl {
  width: 38rem;
}

.dropdown-mega-menu-lg {
  width: 26rem;
}

[dir=ltr] .dropdown-menu-start {
  --vz-position: end;
}
[dir=ltr] .dropdown-menu-end {
  --vz-position: start;
}

.dropdown-head .nav-tabs-custom {
  border: 0;
}
.dropdown-head .nav-tabs-custom .nav-link {
  color: rgba(255, 255, 255, 0.6);
}
.dropdown-head .nav-tabs-custom .nav-link::after {
  display: none;
}
.dropdown-head .nav-tabs-custom .nav-link.active {
  background-color: var(--vz-dropdown-bg);
}
.dropdown-head .nav-tabs-custom .nav-link:hover {
  color: #fff;
}

.dropdownmenu-primary .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(53, 119, 241, 0.07);
  color: #3577f1;
}

.dropdownmenu-secondary .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(173, 181, 189, 0.07);
  color: #adb5bd;
}

.dropdownmenu-success .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(10, 187, 135, 0.07);
  color: #0abb87;
}

.dropdownmenu-info .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(41, 156, 219, 0.07);
  color: #299cdb;
}

.dropdownmenu-warning .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(255, 175, 0, 0.07);
  color: #ffaf00;
}

.dropdownmenu-danger .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(228, 34, 34, 0.07);
  color: #e42222;
}

.dropdownmenu-light .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(243, 246, 249, 0.07);
  color: #f3f6f9;
}

.dropdownmenu-dark .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(33, 37, 41, 0.07);
  color: #212529;
}

.dropdownmenu-pink .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(253, 57, 122, 0.07);
  color: #fd397a;
}

.dropdownmenu-purple .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(101, 89, 204, 0.07);
  color: #6559cc;
}

.dropdownmenu-orange .dropdown-item:is(:focus, :hover, .active) {
  background-color: rgba(255, 69, 0, 0.07);
  color: orangered;
}

:is(.nav-tabs, .nav-pills) > li > a {
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-medium);
}

.nav-pills > a {
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-medium);
}

.nav-pills :is(.nav-link.active, .show > .nav-link) {
  box-shadow: var(--vz-element-shadow);
}

.nav-tabs-custom {
  border-bottom: 1px solid var(--vz-border-color);
}
.nav-tabs-custom .nav-item {
  position: relative;
}
.nav-tabs-custom .nav-item .nav-link {
  border: none;
  font-weight: var(--vz-font-weight-medium);
}
.nav-tabs-custom .nav-item .nav-link::after {
  content: "";
  background: var(--vz-primary);
  height: 2px;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  transition: all 250ms ease 0s;
  transform: scale(0);
}
.nav-tabs-custom .nav-item .nav-link.active {
  color: var(--vz-primary);
}
.nav-tabs-custom .nav-item .nav-link.active:after {
  transform: scale(1);
}
.nav-tabs-custom.card-header-tabs {
  margin-top: -1rem;
}
.nav-tabs-custom.card-header-tabs .nav-link {
  padding: 1rem 1rem;
}

.vertical-nav .nav .nav-link {
  padding: 24px 16px;
  text-align: center;
  margin-bottom: 8px;
}
.vertical-nav .nav .nav-link .nav-icon {
  font-size: 24px;
}

.navtab-bg li > a {
  background-color: #e9ebec;
  margin: 0 5px;
}

.arrow-navtabs .nav-item .nav-link {
  position: relative;
  text-align: center;
}
.arrow-navtabs .nav-item .nav-link:before {
  content: "";
  position: absolute;
  border: 6px solid transparent;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .arrow-navtabs .nav-item .nav-link:before {
    transition: none;
  }
}
.arrow-navtabs .nav-item .nav-link.active:before {
  border-top-color: var(--vz-primary);
}

.custom-hover-nav-tabs .nav-item {
  text-align: center;
  overflow: hidden;
}
.custom-hover-nav-tabs .nav-item .nav-link {
  width: 120px;
  height: 45px;
  position: relative;
  border-radius: 0px;
}
.custom-hover-nav-tabs .nav-item .nav-link .nav-icon {
  font-size: 22px;
}
.custom-hover-nav-tabs .nav-item .nav-link .nav-titl {
  font-size: 14px;
}
.custom-hover-nav-tabs .nav-item .nav-link.active .nav-tab-position {
  color: #fff;
}
.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position {
  position: absolute;
  left: 0;
  right: 0;
  transition: all 0.4s;
}
.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position.nav-icon {
  top: 50%;
  transform: translateY(-50%);
}
.custom-hover-nav-tabs .nav-item .nav-link .nav-tab-position.nav-titl {
  bottom: -20px;
}
.custom-hover-nav-tabs .nav-item .nav-link:hover .nav-titl {
  bottom: 50%;
  transform: translateY(50%);
}
.custom-hover-nav-tabs .nav-item .nav-link:hover .nav-icon {
  top: -20px;
}

.custom-verti-nav-pills .nav-link {
  background-color: var(--vz-light);
  margin-top: 7px;
  position: relative;
}
@media (min-width: 992px) {
  .custom-verti-nav-pills .nav-link::before {
    content: "";
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    border: 12px solid transparent;
    border-left-color: transparent;
    transition: border-left-color 0.04 ease;
  }
  .custom-verti-nav-pills .nav-link.active::before {
    border-left-color: var(--vz-primary);
  }
}

.animation-nav li {
  position: relative;
}
.animation-nav li a {
  color: var(--vz-body-color);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s;
  z-index: 1;
}
.animation-nav li a span {
  position: relative;
}
.animation-nav li a::before {
  content: "";
  position: absolute;
  top: 0;
  width: 0;
  right: 0;
  height: 100%;
  transition: width 0.4s cubic-bezier(0.51, 0.18, 0, 0.88) 0.1s;
  background-color: var(--vz-primary);
  z-index: -1;
}
.animation-nav li a:hover, .animation-nav li a.active {
  color: #fff;
  background-color: transparent !important;
}
.animation-nav li a:hover::before, .animation-nav li a.active::before {
  width: 100%;
  left: 0;
}

.nav-border-top .nav-link {
  border-top: 3px solid transparent;
}
.nav-border-top .nav-link.active {
  border-top-color: var(--vz-primary);
}

.nav-custom {
  background-color: var(--vz-primary);
  border-radius: var(--vz-border-radius);
}
.nav-custom .nav-item .nav-link {
  color: rgba(255, 255, 255, 0.75);
}
.nav-custom .nav-item .nav-link.active {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}
.nav-custom.nav-custom-light .nav-item .nav-link {
  color: rgba(var(--vz-body-rgb), 0.75);
}
.nav-custom.nav-custom-light .nav-item .nav-link.active {
  color: var(--vz-light);
  background-color: var(--vz-primary);
}

.nav-custom-light {
  background-color: var(--vz-light);
}

.progress-nav {
  position: relative;
  margin-right: 1rem;
  margin-left: 1rem;
}
.progress-nav .progress {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
}
.progress-nav .nav {
  margin-right: -1rem;
  margin-left: -1rem;
  justify-content: space-between;
}
.progress-nav .nav .nav-link {
  width: 2rem;
  height: 2rem;
  background-color: var(--vz-light);
  padding: 0;
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-medium);
}
.progress-nav .nav .nav-link.active, .progress-nav .nav .nav-link.done {
  background-color: var(--vz-primary);
  color: #fff;
}

.step-arrow-nav .nav {
  background-color: var(--vz-light);
}
.step-arrow-nav .nav .nav-link {
  border-radius: 0;
  position: relative;
  font-weight: var(--vz-font-weight-medium);
  color: var(--vz-body-color);
}
.step-arrow-nav .nav .nav-link::before {
  content: "";
  position: absolute;
  border: 7px solid transparent;
  right: -14px;
  top: 50%;
  transform: translateY(-50%);
}
.step-arrow-nav .nav .nav-link.done {
  background-color: rgba(var(--vz-primary-rgb), 0.05);
  color: var(--vz-primary);
}
.step-arrow-nav .nav .nav-link.done::before {
  border-left-color: transparent;
}
.step-arrow-nav .nav .nav-link.active {
  background-color: rgba(var(--vz-primary-rgb), 0.1);
  color: var(--vz-primary);
  box-shadow: none;
}
.step-arrow-nav .nav .nav-link.active::before {
  border-left-color: rgba(var(--vz-primary-rgb), 0.1);
}
.step-arrow-nav .nav .nav-item:last-child .nav-link:before {
  display: none;
}

.vertical-navs-step .nav {
  gap: 16px;
}
.vertical-navs-step .nav .nav-link {
  text-align: left;
  background-color: rgba(var(--vz-light-rgb), 0.4);
  border: 1px solid var(--vz-border-color);
  color: var(--vz-body-color);
}
.vertical-navs-step .nav .nav-link .step-title {
  font-weight: var(--vz-font-weight-semibold);
}
.vertical-navs-step .nav .nav-link .step-icon {
  color: var(--vz-danger);
  vertical-align: middle;
  font-weight: var(--vz-font-weight-medium);
  float: left;
}
.vertical-navs-step .nav .nav-link.active .step-icon, .vertical-navs-step .nav .nav-link.done .step-icon {
  color: var(--vz-success);
}
.vertical-navs-step .nav .nav-link.active .step-icon:before, .vertical-navs-step .nav .nav-link.done .step-icon:before {
  content: "\eb80";
}
.vertical-navs-step .nav .nav-link.active {
  border-color: var(--vz-primary);
}
.vertical-navs-step .nav .nav-link.done {
  border-color: var(--vz-success);
}

.nav-custom-outline.nav .nav-link {
  border: 1px solid transparent;
  border-bottom: 2px solid transparent;
}
.nav-custom-outline.nav .nav-link.active {
  border-color: var(--vz-primary);
  background-color: transparent;
  color: var(--vz-primary);
}

.nav-customs.nav {
  padding-left: 34px;
  overflow: hidden;
}
.nav-customs.nav .nav-link {
  position: relative;
  display: block;
  float: right;
  background-color: var(--vz-tertiary-bg);
  margin-right: 46px;
  transition: all 0.5s ease;
}
.nav-customs.nav .nav-link::before, .nav-customs.nav .nav-link::after {
  display: block;
  content: " ";
  position: absolute;
  top: -1px;
  bottom: -1px;
  width: 37px;
  background-color: var(--vz-tertiary-bg);
  transition: all 0.5s ease;
}
.nav-customs.nav .nav-link::before {
  border-radius: 0 8px 0 0;
  right: -24px;
  transform: skew(30deg, 0deg);
}
.nav-customs.nav .nav-link::after {
  border-radius: 8px 0 0 0;
  left: -24px;
  transform: skew(-30deg, 0deg);
}
.nav-customs.nav .nav-link.active, .nav-customs.nav .nav-link.active:before, .nav-customs.nav .nav-link.active:after {
  background-color: var(--vz-primary);
  color: #fff;
}
.nav-customs.nav .nav-link.active {
  z-index: 1;
}

.nav-border-top-primary .nav-link.active {
  color: var(--vz-primary);
  border-top-color: var(--vz-primary);
}

.nav-custom-primary {
  background-color: var(--vz-primary);
}

.nav-primary .nav-link.active {
  color: #fff;
  background-color: var(--vz-primary);
}
.nav-primary.nav-tabs .nav-link.active {
  color: var(--vz-primary);
  background-color: var(--vz-secondary-bg);
}
.nav-primary.nav-tabs-custom .nav-link.active {
  color: var(--vz-primary);
  background-color: var(--vz-secondary-bg);
}
.nav-primary.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-primary);
}
.nav-primary.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-primary);
}
.nav-primary.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-primary);
}
.nav-primary.nav-custom-outline .nav-link.active {
  color: var(--vz-primary);
  border-color: var(--vz-primary);
}

.nav-border-top-secondary .nav-link.active {
  color: var(--vz-secondary);
  border-top-color: var(--vz-secondary);
}

.nav-custom-secondary {
  background-color: var(--vz-secondary);
}

.nav-secondary .nav-link.active {
  color: #fff;
  background-color: var(--vz-secondary);
}
.nav-secondary.nav-tabs .nav-link.active {
  color: var(--vz-secondary);
  background-color: var(--vz-secondary-bg);
}
.nav-secondary.nav-tabs-custom .nav-link.active {
  color: var(--vz-secondary);
  background-color: var(--vz-secondary-bg);
}
.nav-secondary.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-secondary);
}
.nav-secondary.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-secondary);
}
.nav-secondary.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-secondary);
}
.nav-secondary.nav-custom-outline .nav-link.active {
  color: var(--vz-secondary);
  border-color: var(--vz-secondary);
}

.nav-border-top-success .nav-link.active {
  color: var(--vz-success);
  border-top-color: var(--vz-success);
}

.nav-custom-success {
  background-color: var(--vz-success);
}

.nav-success .nav-link.active {
  color: #fff;
  background-color: var(--vz-success);
}
.nav-success.nav-tabs .nav-link.active {
  color: var(--vz-success);
  background-color: var(--vz-secondary-bg);
}
.nav-success.nav-tabs-custom .nav-link.active {
  color: var(--vz-success);
  background-color: var(--vz-secondary-bg);
}
.nav-success.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-success);
}
.nav-success.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-success);
}
.nav-success.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-success);
}
.nav-success.nav-custom-outline .nav-link.active {
  color: var(--vz-success);
  border-color: var(--vz-success);
}

.nav-border-top-info .nav-link.active {
  color: var(--vz-info);
  border-top-color: var(--vz-info);
}

.nav-custom-info {
  background-color: var(--vz-info);
}

.nav-info .nav-link.active {
  color: #fff;
  background-color: var(--vz-info);
}
.nav-info.nav-tabs .nav-link.active {
  color: var(--vz-info);
  background-color: var(--vz-secondary-bg);
}
.nav-info.nav-tabs-custom .nav-link.active {
  color: var(--vz-info);
  background-color: var(--vz-secondary-bg);
}
.nav-info.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-info);
}
.nav-info.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-info);
}
.nav-info.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-info);
}
.nav-info.nav-custom-outline .nav-link.active {
  color: var(--vz-info);
  border-color: var(--vz-info);
}

.nav-border-top-warning .nav-link.active {
  color: var(--vz-warning);
  border-top-color: var(--vz-warning);
}

.nav-custom-warning {
  background-color: var(--vz-warning);
}

.nav-warning .nav-link.active {
  color: #fff;
  background-color: var(--vz-warning);
}
.nav-warning.nav-tabs .nav-link.active {
  color: var(--vz-warning);
  background-color: var(--vz-secondary-bg);
}
.nav-warning.nav-tabs-custom .nav-link.active {
  color: var(--vz-warning);
  background-color: var(--vz-secondary-bg);
}
.nav-warning.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-warning);
}
.nav-warning.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-warning);
}
.nav-warning.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-warning);
}
.nav-warning.nav-custom-outline .nav-link.active {
  color: var(--vz-warning);
  border-color: var(--vz-warning);
}

.nav-border-top-danger .nav-link.active {
  color: var(--vz-danger);
  border-top-color: var(--vz-danger);
}

.nav-custom-danger {
  background-color: var(--vz-danger);
}

.nav-danger .nav-link.active {
  color: #fff;
  background-color: var(--vz-danger);
}
.nav-danger.nav-tabs .nav-link.active {
  color: var(--vz-danger);
  background-color: var(--vz-secondary-bg);
}
.nav-danger.nav-tabs-custom .nav-link.active {
  color: var(--vz-danger);
  background-color: var(--vz-secondary-bg);
}
.nav-danger.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-danger);
}
.nav-danger.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-danger);
}
.nav-danger.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-danger);
}
.nav-danger.nav-custom-outline .nav-link.active {
  color: var(--vz-danger);
  border-color: var(--vz-danger);
}

.nav-border-top-light .nav-link.active {
  color: var(--vz-light);
  border-top-color: var(--vz-light);
}

.nav-custom-light {
  background-color: var(--vz-light);
}

.nav-light .nav-link.active {
  color: #fff;
  background-color: var(--vz-light);
}
.nav-light.nav-tabs .nav-link.active {
  color: var(--vz-light);
  background-color: var(--vz-secondary-bg);
}
.nav-light.nav-tabs-custom .nav-link.active {
  color: var(--vz-light);
  background-color: var(--vz-secondary-bg);
}
.nav-light.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-light);
}
.nav-light.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-light);
}
.nav-light.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-light);
}
.nav-light.nav-custom-outline .nav-link.active {
  color: var(--vz-light);
  border-color: var(--vz-light);
}

.nav-border-top-dark .nav-link.active {
  color: var(--vz-dark);
  border-top-color: var(--vz-dark);
}

.nav-custom-dark {
  background-color: var(--vz-dark);
}

.nav-dark .nav-link.active {
  color: #fff;
  background-color: var(--vz-dark);
}
.nav-dark.nav-tabs .nav-link.active {
  color: var(--vz-dark);
  background-color: var(--vz-secondary-bg);
}
.nav-dark.nav-tabs-custom .nav-link.active {
  color: var(--vz-dark);
  background-color: var(--vz-secondary-bg);
}
.nav-dark.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-dark);
}
.nav-dark.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-dark);
}
.nav-dark.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-dark);
}
.nav-dark.nav-custom-outline .nav-link.active {
  color: var(--vz-dark);
  border-color: var(--vz-dark);
}

.nav-border-top-pink .nav-link.active {
  color: var(--vz-pink);
  border-top-color: var(--vz-pink);
}

.nav-custom-pink {
  background-color: var(--vz-pink);
}

.nav-pink .nav-link.active {
  color: #fff;
  background-color: var(--vz-pink);
}
.nav-pink.nav-tabs .nav-link.active {
  color: var(--vz-pink);
  background-color: var(--vz-secondary-bg);
}
.nav-pink.nav-tabs-custom .nav-link.active {
  color: var(--vz-pink);
  background-color: var(--vz-secondary-bg);
}
.nav-pink.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-pink);
}
.nav-pink.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-pink);
}
.nav-pink.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-pink);
}
.nav-pink.nav-custom-outline .nav-link.active {
  color: var(--vz-pink);
  border-color: var(--vz-pink);
}

.nav-border-top-purple .nav-link.active {
  color: var(--vz-purple);
  border-top-color: var(--vz-purple);
}

.nav-custom-purple {
  background-color: var(--vz-purple);
}

.nav-purple .nav-link.active {
  color: #fff;
  background-color: var(--vz-purple);
}
.nav-purple.nav-tabs .nav-link.active {
  color: var(--vz-purple);
  background-color: var(--vz-secondary-bg);
}
.nav-purple.nav-tabs-custom .nav-link.active {
  color: var(--vz-purple);
  background-color: var(--vz-secondary-bg);
}
.nav-purple.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-purple);
}
.nav-purple.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-purple);
}
.nav-purple.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-purple);
}
.nav-purple.nav-custom-outline .nav-link.active {
  color: var(--vz-purple);
  border-color: var(--vz-purple);
}

.nav-border-top-orange .nav-link.active {
  color: var(--vz-orange);
  border-top-color: var(--vz-orange);
}

.nav-custom-orange {
  background-color: var(--vz-orange);
}

.nav-orange .nav-link.active {
  color: #fff;
  background-color: var(--vz-orange);
}
.nav-orange.nav-tabs .nav-link.active {
  color: var(--vz-orange);
  background-color: var(--vz-secondary-bg);
}
.nav-orange.nav-tabs-custom .nav-link.active {
  color: var(--vz-orange);
  background-color: var(--vz-secondary-bg);
}
.nav-orange.nav-tabs-custom .nav-link.active::after {
  background-color: var(--vz-orange);
}
.nav-orange.arrow-navtabs .nav-link.active::before {
  border-top-color: var(--vz-orange);
}
.nav-orange.custom-verti-nav-pills .nav-link.active::before {
  border-left-color: var(--vz-orange);
}
.nav-orange.nav-custom-outline .nav-link.active {
  color: var(--vz-orange);
  border-color: var(--vz-orange);
}

.table > thead {
  border-color: var(--vz-border-color);
}
.table > :not(:first-child) {
  border-top-width: var(--vz-border-width);
}

.table-nowrap th,
.table-nowrap td {
  white-space: nowrap;
}

.table-card {
  margin: -1rem -1rem;
}
.table-card th:first-child,
.table-card td:first-child {
  padding-left: 16px;
}
.table-card th:last-child,
.table-card td:last-child {
  padding-right: 16px;
}
.table-card .table > :not(:first-child) {
  border-top-width: var(--vz-border-width);
}

.border-primary.table > thead {
  border-color: var(--vz-primary) !important;
}

.table-primary.table > thead {
  border-bottom-color: var(--vz-primary-border-subtle) !important;
}

.border-secondary.table > thead {
  border-color: var(--vz-secondary) !important;
}

.table-secondary.table > thead {
  border-bottom-color: var(--vz-secondary-border-subtle) !important;
}

.border-success.table > thead {
  border-color: var(--vz-success) !important;
}

.table-success.table > thead {
  border-bottom-color: var(--vz-success-border-subtle) !important;
}

.border-info.table > thead {
  border-color: var(--vz-info) !important;
}

.table-info.table > thead {
  border-bottom-color: var(--vz-info-border-subtle) !important;
}

.border-warning.table > thead {
  border-color: var(--vz-warning) !important;
}

.table-warning.table > thead {
  border-bottom-color: var(--vz-warning-border-subtle) !important;
}

.border-danger.table > thead {
  border-color: var(--vz-danger) !important;
}

.table-danger.table > thead {
  border-bottom-color: var(--vz-danger-border-subtle) !important;
}

.border-light.table > thead {
  border-color: var(--vz-light) !important;
}

.table-light.table > thead {
  border-bottom-color: var(--vz-light-border-subtle) !important;
}

.border-dark.table > thead {
  border-color: var(--vz-dark) !important;
}

.table-dark.table > thead {
  border-bottom-color: var(--vz-dark-border-subtle) !important;
}

.border-pink.table > thead {
  border-color: var(--vz-pink) !important;
}

.table-pink.table > thead {
  border-bottom-color: var(--vz-pink-border-subtle) !important;
}

.border-purple.table > thead {
  border-color: var(--vz-purple) !important;
}

.table-purple.table > thead {
  border-bottom-color: var(--vz-purple-border-subtle) !important;
}

.border-orange.table > thead {
  border-color: var(--vz-orange) !important;
}

.table-orange.table > thead {
  border-bottom-color: var(--vz-orange-border-subtle) !important;
}

.table > :not(caption) > * > * {
  color: var(--vz-table-color) !important;
  font-weight: 400;
}

.table .form-check {
  padding-left: 0px;
  margin-bottom: 0px;
}
.table .form-check .form-check-input {
  margin-left: 0px;
  margin-top: 0px;
  float: none;
  vertical-align: middle;
}
.table .sort {
  position: relative;
}
.table .sort::before {
  content: "\f035d";
  position: absolute;
  right: 0.5rem;
  top: 18px;
  font-size: 0.8rem;
  font-family: "remixicon";
}
.table .sort::after {
  position: absolute;
  right: 0.5rem;
  content: "\f0360";
  font-family: "remixicon";
  font-size: 0.8rem;
  top: 12px;
}

.table-vcenter > :not(caption) > * > * {
  vertical-align: middle;
}

.modal-title {
  font-weight: var(--vz-font-weight-semibold);
}

.modal-dialog:not(.modal-dialog-scrollable) .modal-header {
  padding-bottom: 0;
}
.modal-dialog:not(.modal-dialog-scrollable) .modal-header .btn-close {
  margin-top: -1.25rem 1.25rem;
}
.modal-dialog:not(.modal-dialog-scrollable) .modal-footer {
  padding-top: 0;
}

.modal.fadeInRight .modal-dialog {
  opacity: 0;
  transform: translateX(20%);
  transition: all 0.3s ease-in-out;
}
.modal.fadeInRight.show .modal-dialog {
  opacity: 1;
  transform: translateX(0);
}
.modal.fadeInLeft .modal-dialog {
  animation: fadeInLeft 0.3s ease-in-out;
  transform: translate(-50%, 0);
}
.modal.fadeInLeft.show .modal-dialog {
  transform: none;
}
.modal.fadeInUp .modal-dialog {
  animation: fadeInUp 0.3s ease-in-out;
  transform: translate(0, 30%);
}
.modal.fadeInUp.show .modal-dialog {
  transform: none;
}
.modal.flip {
  perspective: 1300px;
}
.modal.flip .modal-dialog {
  opacity: 0;
  transform: rotateY(-70deg);
  transition: all 0.3s;
}
.modal.flip.show .modal-dialog {
  opacity: 1;
  transform: rotateY(0deg);
}
.modal.zoomIn .modal-dialog {
  opacity: 0;
  transform: scale(0.7);
  transition: all 0.3s ease;
}
.modal.zoomIn.show .modal-dialog {
  opacity: 1;
  transform: scale(1);
}

.modal-dialog-right {
  margin-right: 1.75rem;
}

.modal-dialog-bottom {
  display: flex;
  align-items: flex-end;
  min-height: calc(100% - 1.75rem);
}
@media (min-width: 576px) {
  .modal-dialog-bottom {
    min-height: calc(100% - 3.5rem);
  }
}

.modal-dialog-bottom-right {
  display: flex;
  align-items: flex-end;
  min-height: calc(100% - 1.75rem);
  margin-right: 1.75rem;
}
@media (min-width: 576px) {
  .modal-dialog-bottom-right {
    min-height: calc(100% - 3.5rem);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translate3d(-30%, 0, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 30%, 0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}
@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
.login-modal {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1016%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(64%2c 81%2c 137%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c650.704C122.328%2c648.746%2c159.175%2c473.043%2c255.674%2c397.837C339.724%2c332.333%2c461.529%2c324.924%2c526.449%2c240.421C598.428%2c146.73%2c655.546%2c24.847%2c631.015%2c-90.726C606.666%2c-205.444%2c482.926%2c-263.497%2c401.565%2c-347.958C325.215%2c-427.217%2c275.543%2c-549.012%2c167.826%2c-571.563C60.344%2c-594.065%2c-27.703%2c-482.932%2c-135.163%2c-460.325C-256.336%2c-434.833%2c-401.929%2c-509.651%2c-497.972%2c-431.495C-592.807%2c-354.321%2c-579.865%2c-206.886%2c-595.603%2c-85.635C-611.133%2c34.016%2c-656.761%2c169.183%2c-588.884%2c268.934C-520.854%2c368.909%2c-362.458%2c340.324%2c-260.989%2c406.106C-158.875%2c472.306%2c-121.679%2c652.651%2c0%2c650.704' fill='%2333416e'%3e%3c/path%3e%3cpath d='M1440 995.672C1519.728 984.741 1563.12 899.779 1626.466 850.1469999999999 1682.6390000000001 806.135 1756.261 782.602 1791.2939999999999 720.431 1827.571 656.052 1835.537 577.6610000000001 1820.814 505.247 1806.518 434.933 1753.2640000000001 383.16999999999996 1710.941 325.228 1664.475 261.614 1634.992 175.16000000000003 1560.657 149.07999999999998 1485.96 122.87299999999999 1402.146 155.543 1332.03 192.289 1269.541 225.038 1232.754 287.251 1189.969 343.347 1149.925 395.849 1115.781 448.9 1089.96 509.672 1056 589.599 988.9680000000001 671.1659999999999 1015.557 753.837 1041.91 835.774 1142.714 863.61 1217.498 906.22 1288.388 946.611 1359.167 1006.755 1440 995.672' fill='%234d61a4'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1016'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
  background-size: cover;
  background-position: center;
}

.pagination-separated .page-item .page-link {
  margin-left: 0.35rem;
  border-radius: var(--vz-border-radius);
}

.pagination-rounded .page-link {
  border-radius: 30px !important;
  margin: 0 3px !important;
  border: none;
  min-width: 32px;
  min-height: 32px;
  text-align: center;
}
.pagination-rounded.pagination-sm .page-link {
  min-width: 25px;
  min-height: 25px;
}

.page-item.active .page-link {
  box-shadow: var(--vz-element-shadow);
}

.progress-sm {
  height: 5px;
}

.progress-lg {
  height: 12px;
}

.progress-xl {
  height: 16px;
}

.custom-progess {
  position: relative;
}
.custom-progess .progress-icon {
  position: absolute;
  top: -12px;
}
.custom-progess .progress-icon .avatar-title {
  background: var(--vz-secondary-bg);
}

.animated-progress {
  position: relative;
}
.animated-progress .progress-bar {
  position: relative;
  border-radius: 6px;
  animation: animate-positive 2s;
}

@keyframes animate-positive {
  0% {
    width: 0;
  }
}
.custom-progress {
  height: 15px;
  padding: 4px;
  border-radius: 30px;
}
.custom-progress .progress-bar {
  position: relative;
  border-radius: 30px;
}
.custom-progress .progress-bar::before {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: #fff;
  border-radius: 7px;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
}

.progress-label {
  overflow: visible;
}
.progress-label .progress-bar {
  position: relative;
  overflow: visible;
}
.progress-label .progress-bar .label {
  position: absolute;
  top: -25px;
  right: -9px;
  background-color: var(--vz-primary);
  color: #fff;
  display: inline-block;
  line-height: 18px;
  padding: 0 4px;
  border-radius: 4px;
}
.progress-label .progress-bar .label:after {
  content: "";
  position: absolute;
  border: 4px solid transparent;
  border-top-color: var(--vz-primary);
  bottom: -7px;
  left: 50%;
  transform: translateX(-50%);
}

.progress-step-arrow {
  height: 3.25rem;
}
.progress-step-arrow .progress-bar {
  position: relative;
  overflow: initial;
  font-size: 0.875rem;
  color: #fff;
}
.progress-step-arrow .progress-bar::after {
  content: "";
  position: absolute;
  border: 10px solid transparent;
  bottom: 15px;
  right: -20px;
  z-index: 1;
}

.progress-primary .progress-bar {
  background-color: var(--vz-primary);
}
.progress-primary .progress-bar::after {
  border-left-color: var(--vz-primary);
}
.progress-primary .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-primary-rgb), 0.1) !important;
  color: var(--vz-primary) !important;
}
.progress-primary .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-primary-rgb), 0.1);
}

.progress-secondary .progress-bar {
  background-color: var(--vz-secondary);
}
.progress-secondary .progress-bar::after {
  border-left-color: var(--vz-secondary);
}
.progress-secondary .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-secondary-rgb), 0.1) !important;
  color: var(--vz-secondary) !important;
}
.progress-secondary .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-secondary-rgb), 0.1);
}

.progress-success .progress-bar {
  background-color: var(--vz-success);
}
.progress-success .progress-bar::after {
  border-left-color: var(--vz-success);
}
.progress-success .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-success-rgb), 0.1) !important;
  color: var(--vz-success) !important;
}
.progress-success .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-success-rgb), 0.1);
}

.progress-info .progress-bar {
  background-color: var(--vz-info);
}
.progress-info .progress-bar::after {
  border-left-color: var(--vz-info);
}
.progress-info .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-info-rgb), 0.1) !important;
  color: var(--vz-info) !important;
}
.progress-info .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-info-rgb), 0.1);
}

.progress-warning .progress-bar {
  background-color: var(--vz-warning);
}
.progress-warning .progress-bar::after {
  border-left-color: var(--vz-warning);
}
.progress-warning .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-warning-rgb), 0.1) !important;
  color: var(--vz-warning) !important;
}
.progress-warning .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-warning-rgb), 0.1);
}

.progress-danger .progress-bar {
  background-color: var(--vz-danger);
}
.progress-danger .progress-bar::after {
  border-left-color: var(--vz-danger);
}
.progress-danger .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-danger-rgb), 0.1) !important;
  color: var(--vz-danger) !important;
}
.progress-danger .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-danger-rgb), 0.1);
}

.progress-light .progress-bar {
  background-color: var(--vz-light);
}
.progress-light .progress-bar::after {
  border-left-color: var(--vz-light);
}
.progress-light .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-light-rgb), 0.1) !important;
  color: var(--vz-light) !important;
}
.progress-light .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-light-rgb), 0.1);
}

.progress-dark .progress-bar {
  background-color: var(--vz-dark);
}
.progress-dark .progress-bar::after {
  border-left-color: var(--vz-dark);
}
.progress-dark .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-dark-rgb), 0.1) !important;
  color: var(--vz-dark) !important;
}
.progress-dark .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-dark-rgb), 0.1);
}

.progress-pink .progress-bar {
  background-color: var(--vz-pink);
}
.progress-pink .progress-bar::after {
  border-left-color: var(--vz-pink);
}
.progress-pink .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-pink-rgb), 0.1) !important;
  color: var(--vz-pink) !important;
}
.progress-pink .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-pink-rgb), 0.1);
}

.progress-purple .progress-bar {
  background-color: var(--vz-purple);
}
.progress-purple .progress-bar::after {
  border-left-color: var(--vz-purple);
}
.progress-purple .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-purple-rgb), 0.1) !important;
  color: var(--vz-purple) !important;
}
.progress-purple .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-purple-rgb), 0.1);
}

.progress-orange .progress-bar {
  background-color: var(--vz-orange);
}
.progress-orange .progress-bar::after {
  border-left-color: var(--vz-orange);
}
.progress-orange .progress-bar:nth-child(2) {
  background-color: rgba(var(--vz-orange-rgb), 0.1) !important;
  color: var(--vz-orange) !important;
}
.progress-orange .progress-bar:nth-child(2)::after {
  border-left-color: rgba(var(--vz-orange-rgb), 0.1);
}

.popover {
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
}

.custom-blockquote.blockquote {
  padding: 16px;
  border-left: 3px solid;
}
.custom-blockquote.blockquote.blockquote-outline {
  background-color: var(--vz-secondary-bg) !important;
  border: 1px solid;
  border-left: 3px solid;
}

.custom-blockquote.blockquote.blockquote-primary {
  border-color: var(--vz-primary);
  background-color: var(--vz-primary-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-primary .blockquote-footer {
  color: var(--vz-primary-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-primary {
  border-color: var(--vz-primary);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-primary .blockquote-footer {
  color: var(--vz-primary-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-secondary {
  border-color: var(--vz-secondary);
  background-color: var(--vz-secondary-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-secondary .blockquote-footer {
  color: var(--vz-secondary-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-secondary {
  border-color: var(--vz-secondary);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-secondary .blockquote-footer {
  color: var(--vz-secondary-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-success {
  border-color: var(--vz-success);
  background-color: var(--vz-success-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-success .blockquote-footer {
  color: var(--vz-success-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-success {
  border-color: var(--vz-success);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-success .blockquote-footer {
  color: var(--vz-success-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-info {
  border-color: var(--vz-info);
  background-color: var(--vz-info-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-info .blockquote-footer {
  color: var(--vz-info-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-info {
  border-color: var(--vz-info);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-info .blockquote-footer {
  color: var(--vz-info-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-warning {
  border-color: var(--vz-warning);
  background-color: var(--vz-warning-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-warning .blockquote-footer {
  color: var(--vz-warning-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-warning {
  border-color: var(--vz-warning);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-warning .blockquote-footer {
  color: var(--vz-warning-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-danger {
  border-color: var(--vz-danger);
  background-color: var(--vz-danger-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-danger .blockquote-footer {
  color: var(--vz-danger-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-danger {
  border-color: var(--vz-danger);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-danger .blockquote-footer {
  color: var(--vz-danger-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-light {
  border-color: var(--vz-light);
  background-color: var(--vz-light-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-light .blockquote-footer {
  color: var(--vz-light-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-light {
  border-color: var(--vz-light);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-light .blockquote-footer {
  color: var(--vz-light-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-dark {
  border-color: var(--vz-dark);
  background-color: var(--vz-dark-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-dark .blockquote-footer {
  color: var(--vz-dark-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-dark {
  border-color: var(--vz-dark);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-dark .blockquote-footer {
  color: var(--vz-dark-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-pink {
  border-color: var(--vz-pink);
  background-color: var(--vz-pink-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-pink .blockquote-footer {
  color: var(--vz-pink-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-pink {
  border-color: var(--vz-pink);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-pink .blockquote-footer {
  color: var(--vz-pink-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-purple {
  border-color: var(--vz-purple);
  background-color: var(--vz-purple-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-purple .blockquote-footer {
  color: var(--vz-purple-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-purple {
  border-color: var(--vz-purple);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-purple .blockquote-footer {
  color: var(--vz-purple-text-emphasis);
}

.custom-blockquote.blockquote.blockquote-orange {
  border-color: var(--vz-orange);
  background-color: var(--vz-orange-bg-subtle);
}
.custom-blockquote.blockquote.blockquote-orange .blockquote-footer {
  color: var(--vz-orange-text-emphasis);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-orange {
  border-color: var(--vz-orange);
}
.custom-blockquote.blockquote.blockquote-outline.blockquote-orange .blockquote-footer {
  color: var(--vz-orange-text-emphasis);
}

.form-check {
  position: relative;
  text-align: left;
}
.form-check .form-check-input {
  cursor: pointer;
}
.form-check label {
  cursor: pointer;
}

.form-check-label {
  cursor: pointer;
  margin-bottom: 0;
}

.form-check-right {
  padding-left: 0;
  display: inline-block;
}
.form-check-right .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.75em;
}
.form-check-right .form-check-label {
  display: block;
}
.form-check-right.form-switch .form-check-input {
  margin-right: -2.8em;
}

.form-check-outline .form-check-input {
  position: relative;
}
.form-check-outline .form-check-input:checked[type=checkbox] {
  background-image: none;
}
.form-check-outline .form-check-input:checked[type=checkbox]::before {
  content: "\f012c";
  font-family: "Material Design Icons";
  top: -2px;
  position: absolute;
  font-weight: 700;
  font-size: 11px;
  left: 1px;
}

.form-radio-outline .form-check-input {
  position: relative;
}
.form-radio-outline .form-check-input:checked[type=radio] {
  background-image: none;
}
.form-radio-outline .form-check-input:checked[type=radio]::before {
  content: "\f0765";
  font-family: "Material Design Icons";
  top: 1px;
  position: absolute;
  font-size: 8px;
  left: 2.7px;
}

.form-switch-md {
  padding-left: 2.5rem;
  min-height: 22px;
  line-height: 22px;
}
.form-switch-md .form-check-input {
  width: 40px;
  height: 20px;
  left: -0.5rem;
  position: relative;
}
.form-switch-md .form-check-label {
  vertical-align: middle;
}

.form-switch-lg {
  padding-left: 2.75rem;
  min-height: 28px;
  line-height: 28px;
}
.form-switch-lg .form-check-input {
  width: 48px;
  height: 24px;
  left: -0.75rem;
  position: relative;
}

.input-group-text {
  margin-bottom: 0px;
}

.form-switch-custom .form-check-input {
  position: relative;
  background-image: none;
}
.form-switch-custom .form-check-input::before {
  content: "\f0765";
  font-family: "Material Design Icons";
  top: -8.3px;
  position: absolute;
  font-size: 20px;
  left: -3px;
  color: var(--vz-secondary-color);
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch-custom .form-check-input::before {
    transition: none;
  }
}
.form-switch-custom .form-check-input:checked {
  background-image: none;
  background-color: var(--vz-input-bg-custom) !important;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch-custom .form-check-input:checked {
    transition: none;
  }
}
.form-switch-custom .form-check-input:checked::before {
  right: -3px;
  left: auto;
}
.form-switch-custom .form-check-input:focus {
  background-image: none;
}

.form-switch-right {
  display: inline-block;
  padding-right: 0.875em;
  margin-bottom: 0;
  padding-left: 0 !important;
}
.form-switch-right .form-check-input {
  float: right;
  margin-left: 0;
  margin-right: -1.75em;
  margin-top: 0.1em !important;
}
.form-switch-right label {
  margin-bottom: 0;
  margin-right: 1rem;
}

.card-radio {
  padding: 0;
}
.card-radio .form-check-label {
  background-color: var(--vz-secondary-bg);
  border: 1px solid var(--vz-border-color);
  border-radius: 0.25rem;
  padding: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  position: relative;
  padding-right: 32px;
}
.card-radio .form-check-label:hover {
  cursor: pointer;
}
.card-radio .form-check-input {
  display: none;
}
.card-radio .form-check-input:checked + .form-check-label {
  border-color: var(--vz-primary) !important;
}
.card-radio .form-check-input:checked + .form-check-label:before {
  content: "\eb80";
  font-family: "remixicon";
  position: absolute;
  top: 2px;
  right: 6px;
  font-size: 16px;
  color: var(--vz-primary);
}
.card-radio.dark .form-check-input:checked + .form-check-label:before {
  color: #fff;
}

.form-radio-outline.form-radio-primary .form-check-input:checked[type=radio] {
  color: var(--vz-primary);
  background-color: transparent;
  border-color: var(--vz-primary);
}

.form-check-primary .form-check-input:checked {
  background-color: var(--vz-primary);
  border-color: var(--vz-primary);
}

.form-radio-primary .form-check-input:checked {
  border-color: var(--vz-primary);
  background-color: var(--vz-primary);
}
.form-radio-primary .form-check-input:checked:after {
  background-color: var(--vz-primary);
}

.form-check-outline.form-check-primary .form-check-input:checked[type=checkbox] {
  color: var(--vz-primary);
  background-color: transparent;
  border-color: var(--vz-primary);
}

.form-switch-primary .form-check-input:checked {
  background-color: var(--vz-primary);
  border-color: var(--vz-primary);
}

.form-switch-custom.form-switch-primary .form-check-input:checked::before {
  color: var(--vz-primary);
}

.form-radio-outline.form-radio-secondary .form-check-input:checked[type=radio] {
  color: var(--vz-secondary);
  background-color: transparent;
  border-color: var(--vz-secondary);
}

.form-check-secondary .form-check-input:checked {
  background-color: var(--vz-secondary);
  border-color: var(--vz-secondary);
}

.form-radio-secondary .form-check-input:checked {
  border-color: var(--vz-secondary);
  background-color: var(--vz-secondary);
}
.form-radio-secondary .form-check-input:checked:after {
  background-color: var(--vz-secondary);
}

.form-check-outline.form-check-secondary .form-check-input:checked[type=checkbox] {
  color: var(--vz-secondary);
  background-color: transparent;
  border-color: var(--vz-secondary);
}

.form-switch-secondary .form-check-input:checked {
  background-color: var(--vz-secondary);
  border-color: var(--vz-secondary);
}

.form-switch-custom.form-switch-secondary .form-check-input:checked::before {
  color: var(--vz-secondary);
}

.form-radio-outline.form-radio-success .form-check-input:checked[type=radio] {
  color: var(--vz-success);
  background-color: transparent;
  border-color: var(--vz-success);
}

.form-check-success .form-check-input:checked {
  background-color: var(--vz-success);
  border-color: var(--vz-success);
}

.form-radio-success .form-check-input:checked {
  border-color: var(--vz-success);
  background-color: var(--vz-success);
}
.form-radio-success .form-check-input:checked:after {
  background-color: var(--vz-success);
}

.form-check-outline.form-check-success .form-check-input:checked[type=checkbox] {
  color: var(--vz-success);
  background-color: transparent;
  border-color: var(--vz-success);
}

.form-switch-success .form-check-input:checked {
  background-color: var(--vz-success);
  border-color: var(--vz-success);
}

.form-switch-custom.form-switch-success .form-check-input:checked::before {
  color: var(--vz-success);
}

.form-radio-outline.form-radio-info .form-check-input:checked[type=radio] {
  color: var(--vz-info);
  background-color: transparent;
  border-color: var(--vz-info);
}

.form-check-info .form-check-input:checked {
  background-color: var(--vz-info);
  border-color: var(--vz-info);
}

.form-radio-info .form-check-input:checked {
  border-color: var(--vz-info);
  background-color: var(--vz-info);
}
.form-radio-info .form-check-input:checked:after {
  background-color: var(--vz-info);
}

.form-check-outline.form-check-info .form-check-input:checked[type=checkbox] {
  color: var(--vz-info);
  background-color: transparent;
  border-color: var(--vz-info);
}

.form-switch-info .form-check-input:checked {
  background-color: var(--vz-info);
  border-color: var(--vz-info);
}

.form-switch-custom.form-switch-info .form-check-input:checked::before {
  color: var(--vz-info);
}

.form-radio-outline.form-radio-warning .form-check-input:checked[type=radio] {
  color: var(--vz-warning);
  background-color: transparent;
  border-color: var(--vz-warning);
}

.form-check-warning .form-check-input:checked {
  background-color: var(--vz-warning);
  border-color: var(--vz-warning);
}

.form-radio-warning .form-check-input:checked {
  border-color: var(--vz-warning);
  background-color: var(--vz-warning);
}
.form-radio-warning .form-check-input:checked:after {
  background-color: var(--vz-warning);
}

.form-check-outline.form-check-warning .form-check-input:checked[type=checkbox] {
  color: var(--vz-warning);
  background-color: transparent;
  border-color: var(--vz-warning);
}

.form-switch-warning .form-check-input:checked {
  background-color: var(--vz-warning);
  border-color: var(--vz-warning);
}

.form-switch-custom.form-switch-warning .form-check-input:checked::before {
  color: var(--vz-warning);
}

.form-radio-outline.form-radio-danger .form-check-input:checked[type=radio] {
  color: var(--vz-danger);
  background-color: transparent;
  border-color: var(--vz-danger);
}

.form-check-danger .form-check-input:checked {
  background-color: var(--vz-danger);
  border-color: var(--vz-danger);
}

.form-radio-danger .form-check-input:checked {
  border-color: var(--vz-danger);
  background-color: var(--vz-danger);
}
.form-radio-danger .form-check-input:checked:after {
  background-color: var(--vz-danger);
}

.form-check-outline.form-check-danger .form-check-input:checked[type=checkbox] {
  color: var(--vz-danger);
  background-color: transparent;
  border-color: var(--vz-danger);
}

.form-switch-danger .form-check-input:checked {
  background-color: var(--vz-danger);
  border-color: var(--vz-danger);
}

.form-switch-custom.form-switch-danger .form-check-input:checked::before {
  color: var(--vz-danger);
}

.form-radio-outline.form-radio-light .form-check-input:checked[type=radio] {
  color: var(--vz-light);
  background-color: transparent;
  border-color: var(--vz-light);
}

.form-check-light .form-check-input:checked {
  background-color: var(--vz-light);
  border-color: var(--vz-light);
}

.form-radio-light .form-check-input:checked {
  border-color: var(--vz-light);
  background-color: var(--vz-light);
}
.form-radio-light .form-check-input:checked:after {
  background-color: var(--vz-light);
}

.form-check-outline.form-check-light .form-check-input:checked[type=checkbox] {
  color: var(--vz-light);
  background-color: transparent;
  border-color: var(--vz-light);
}

.form-switch-light .form-check-input:checked {
  background-color: var(--vz-light);
  border-color: var(--vz-light);
}

.form-switch-custom.form-switch-light .form-check-input:checked::before {
  color: var(--vz-light);
}

.form-radio-outline.form-radio-dark .form-check-input:checked[type=radio] {
  color: var(--vz-dark);
  background-color: transparent;
  border-color: var(--vz-dark);
}

.form-check-dark .form-check-input:checked {
  background-color: var(--vz-dark);
  border-color: var(--vz-dark);
}

.form-radio-dark .form-check-input:checked {
  border-color: var(--vz-dark);
  background-color: var(--vz-dark);
}
.form-radio-dark .form-check-input:checked:after {
  background-color: var(--vz-dark);
}

.form-check-outline.form-check-dark .form-check-input:checked[type=checkbox] {
  color: var(--vz-dark);
  background-color: transparent;
  border-color: var(--vz-dark);
}

.form-switch-dark .form-check-input:checked {
  background-color: var(--vz-dark);
  border-color: var(--vz-dark);
}

.form-switch-custom.form-switch-dark .form-check-input:checked::before {
  color: var(--vz-dark);
}

.form-radio-outline.form-radio-pink .form-check-input:checked[type=radio] {
  color: var(--vz-pink);
  background-color: transparent;
  border-color: var(--vz-pink);
}

.form-check-pink .form-check-input:checked {
  background-color: var(--vz-pink);
  border-color: var(--vz-pink);
}

.form-radio-pink .form-check-input:checked {
  border-color: var(--vz-pink);
  background-color: var(--vz-pink);
}
.form-radio-pink .form-check-input:checked:after {
  background-color: var(--vz-pink);
}

.form-check-outline.form-check-pink .form-check-input:checked[type=checkbox] {
  color: var(--vz-pink);
  background-color: transparent;
  border-color: var(--vz-pink);
}

.form-switch-pink .form-check-input:checked {
  background-color: var(--vz-pink);
  border-color: var(--vz-pink);
}

.form-switch-custom.form-switch-pink .form-check-input:checked::before {
  color: var(--vz-pink);
}

.form-radio-outline.form-radio-purple .form-check-input:checked[type=radio] {
  color: var(--vz-purple);
  background-color: transparent;
  border-color: var(--vz-purple);
}

.form-check-purple .form-check-input:checked {
  background-color: var(--vz-purple);
  border-color: var(--vz-purple);
}

.form-radio-purple .form-check-input:checked {
  border-color: var(--vz-purple);
  background-color: var(--vz-purple);
}
.form-radio-purple .form-check-input:checked:after {
  background-color: var(--vz-purple);
}

.form-check-outline.form-check-purple .form-check-input:checked[type=checkbox] {
  color: var(--vz-purple);
  background-color: transparent;
  border-color: var(--vz-purple);
}

.form-switch-purple .form-check-input:checked {
  background-color: var(--vz-purple);
  border-color: var(--vz-purple);
}

.form-switch-custom.form-switch-purple .form-check-input:checked::before {
  color: var(--vz-purple);
}

.form-radio-outline.form-radio-orange .form-check-input:checked[type=radio] {
  color: var(--vz-orange);
  background-color: transparent;
  border-color: var(--vz-orange);
}

.form-check-orange .form-check-input:checked {
  background-color: var(--vz-orange);
  border-color: var(--vz-orange);
}

.form-radio-orange .form-check-input:checked {
  border-color: var(--vz-orange);
  background-color: var(--vz-orange);
}
.form-radio-orange .form-check-input:checked:after {
  background-color: var(--vz-orange);
}

.form-check-outline.form-check-orange .form-check-input:checked[type=checkbox] {
  color: var(--vz-orange);
  background-color: transparent;
  border-color: var(--vz-orange);
}

.form-switch-orange .form-check-input:checked {
  background-color: var(--vz-orange);
  border-color: var(--vz-orange);
}

.form-switch-custom.form-switch-orange .form-check-input:checked::before {
  color: var(--vz-orange);
}

.col-form-label.required:after, .form-label.required:after {
  color: #d63939;
  content: "*";
  margin-left: 0.25rem;
}

.form-icon {
  position: relative;
}
.form-icon .form-control-icon {
  padding-left: calc(0.9rem * 3);
  position: relative;
}
.form-icon i {
  position: absolute;
  top: 0px;
  bottom: 0px;
  left: 18px;
  display: flex;
  align-items: center;
}
.form-icon.right .form-control-icon {
  padding-right: calc(0.9rem * 3);
  padding-left: 0.9rem;
  position: relative;
}
.form-icon.right i {
  left: auto;
  right: 18px;
}

.list-group-fill-primary.list-group-item {
  color: #fff;
  background-color: var(--vz-primary) !important;
  border-color: var(--vz-primary);
}

.list-group-fill-secondary.list-group-item {
  color: #fff;
  background-color: var(--vz-secondary) !important;
  border-color: var(--vz-secondary);
}

.list-group-fill-success.list-group-item {
  color: #fff;
  background-color: var(--vz-success) !important;
  border-color: var(--vz-success);
}

.list-group-fill-info.list-group-item {
  color: #fff;
  background-color: var(--vz-info) !important;
  border-color: var(--vz-info);
}

.list-group-fill-warning.list-group-item {
  color: #fff;
  background-color: var(--vz-warning) !important;
  border-color: var(--vz-warning);
}

.list-group-fill-danger.list-group-item {
  color: #fff;
  background-color: var(--vz-danger) !important;
  border-color: var(--vz-danger);
}

.list-group-fill-light.list-group-item {
  color: #000;
  background-color: var(--vz-light) !important;
  border-color: var(--vz-light);
}

.list-group-fill-dark.list-group-item {
  color: #fff;
  background-color: var(--vz-dark) !important;
  border-color: var(--vz-dark);
}

.list-group-fill-pink.list-group-item {
  color: #fff;
  background-color: var(--vz-pink) !important;
  border-color: var(--vz-pink);
}

.list-group-fill-purple.list-group-item {
  color: #fff;
  background-color: var(--vz-purple) !important;
  border-color: var(--vz-purple);
}

.list-group-fill-orange.list-group-item {
  color: #fff;
  background-color: var(--vz-orange) !important;
  border-color: var(--vz-orange);
}

.list-group-item {
  margin-bottom: 0px;
}
.list-group-item .list-text {
  color: var(--vz-secondary-color);
}
.list-group-item.active {
  box-shadow: var(--vz-element-shadow);
}
.list-group-item.active .list-title {
  color: #fff;
}
.list-group-item.active .list-text {
  color: rgba(255, 255, 255, 0.5);
}

[data-simplebar] {
  position: relative;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-content: flex-start;
  align-items: flex-start;
}

.simplebar-wrapper {
  overflow: hidden;
  width: inherit;
  height: inherit;
  max-width: inherit;
  max-height: inherit;
}

.simplebar-mask {
  direction: inherit;
  position: absolute;
  overflow: hidden;
  padding: 0;
  margin: 0;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: auto !important;
  height: auto !important;
  z-index: 0;
}

.simplebar-offset {
  direction: inherit !important;
  box-sizing: inherit !important;
  resize: none !important;
  position: absolute;
  top: 0;
  left: 0 !important;
  bottom: 0;
  right: 0 !important;
  padding: 0;
  margin: 0;
  -webkit-overflow-scrolling: touch;
}

.simplebar-content-wrapper {
  direction: inherit;
  box-sizing: border-box !important;
  position: relative;
  display: block;
  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
  width: auto;
  visibility: visible;
  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */
  max-width: 100%; /* Not required for horizontal scroll to trigger */
  max-height: 100%; /* Needed for vertical scroll to trigger */
  scrollbar-width: none;
  padding: 0px !important;
}

.simplebar-content-wrapper::-webkit-scrollbar,
.simplebar-hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.simplebar-content:before,
.simplebar-content:after {
  content: " ";
  display: table;
}

.simplebar-placeholder {
  max-height: 100%;
  max-width: 100%;
  width: 100%;
  pointer-events: none;
}

.simplebar-height-auto-observer-wrapper {
  box-sizing: inherit !important;
  height: 100%;
  width: 100%;
  max-width: 1px;
  position: relative;
  float: left;
  max-height: 1px;
  overflow: hidden;
  z-index: -1;
  padding: 0;
  margin: 0;
  pointer-events: none;
  flex-grow: inherit;
  flex-shrink: 0;
  flex-basis: 0;
}

.simplebar-height-auto-observer {
  box-sizing: inherit;
  display: block;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  height: 1000%;
  width: 1000%;
  min-height: 1px;
  min-width: 1px;
  overflow: hidden;
  pointer-events: none;
  z-index: -1;
}

.simplebar-track {
  z-index: 1;
  position: absolute;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

[data-simplebar].simplebar-dragging .simplebar-content {
  pointer-events: none;
  -moz-user-select: none;
       user-select: none;
  -webkit-user-select: none;
}

[data-simplebar].simplebar-dragging .simplebar-track {
  pointer-events: all;
}

.simplebar-scrollbar {
  position: absolute;
  right: 2px;
  width: 6px;
  min-height: 10px;
}

.simplebar-scrollbar:before {
  position: absolute;
  content: "";
  background: #a2adb7;
  border-radius: 7px;
  left: 0;
  right: 0;
  opacity: 0;
  transition: opacity 0.2s linear;
}

.simplebar-scrollbar.simplebar-visible:before {
  /* When hovered, remove all transitions from drag handle */
  opacity: 0.5;
  transition: opacity 0s linear;
}

.simplebar-track.simplebar-vertical {
  top: 0;
  width: 11px;
}

.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
  top: 2px;
  bottom: 2px;
}

.simplebar-track.simplebar-horizontal {
  left: 0;
  height: 11px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
  height: 100%;
  left: 2px;
  right: 2px;
}

.simplebar-track.simplebar-horizontal .simplebar-scrollbar {
  right: auto;
  left: 0;
  top: 2px;
  height: 7px;
  min-height: 0;
  min-width: 10px;
  width: auto;
}

/* Rtl support */
[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {
  right: auto;
  left: 0;
}

.hs-dummy-scrollbar-size {
  direction: rtl;
  position: fixed;
  opacity: 0;
  visibility: hidden;
  height: 500px;
  width: 500px;
  overflow-y: hidden;
  overflow-x: scroll;
}

.simplebar-hide-scrollbar {
  position: fixed;
  left: 0;
  visibility: hidden;
  overflow-y: scroll;
  scrollbar-width: none;
}

.custom-scroll {
  height: 100%;
}

[data-simplebar-track=primary] .simplebar-scrollbar:before {
  background: var(--vz-primary);
}

[data-simplebar-track=secondary] .simplebar-scrollbar:before {
  background: var(--vz-secondary);
}

[data-simplebar-track=success] .simplebar-scrollbar:before {
  background: var(--vz-success);
}

[data-simplebar-track=info] .simplebar-scrollbar:before {
  background: var(--vz-info);
}

[data-simplebar-track=warning] .simplebar-scrollbar:before {
  background: var(--vz-warning);
}

[data-simplebar-track=danger] .simplebar-scrollbar:before {
  background: var(--vz-danger);
}

[data-simplebar-track=light] .simplebar-scrollbar:before {
  background: var(--vz-light);
}

[data-simplebar-track=dark] .simplebar-scrollbar:before {
  background: var(--vz-dark);
}

[data-simplebar-track=pink] .simplebar-scrollbar:before {
  background: var(--vz-pink);
}

[data-simplebar-track=purple] .simplebar-scrollbar:before {
  background: var(--vz-purple);
}

[data-simplebar-track=orange] .simplebar-scrollbar:before {
  background: var(--vz-orange);
}

/**
 * prism.js default theme for JavaScript, CSS and HTML
 * Based on dabblet (http://dabblet.com)
 * <AUTHOR> Verou
 */
code[class*=language-],
pre[class*=language-] {
  color: black;
  background: none;
  text-shadow: 0 1px white;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 1em;
  text-align: left;
  white-space: pre;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  hyphens: none;
}

pre[class*=language-]::-moz-selection,
pre[class*=language-] ::-moz-selection,
code[class*=language-]::-moz-selection,
code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}

pre[class*=language-]::-moz-selection, pre[class*=language-] ::-moz-selection, code[class*=language-]::-moz-selection, code[class*=language-] ::-moz-selection {
  text-shadow: none;
  background: #b3d4fc;
}

pre[class*=language-]::selection,
pre[class*=language-] ::selection,
code[class*=language-]::selection,
code[class*=language-] ::selection {
  text-shadow: none;
  background: #b3d4fc;
}

@media print {
  code[class*=language-],
  pre[class*=language-] {
    text-shadow: none;
  }
}
/* Code blocks */
pre[class*=language-] {
  padding: 1em;
  margin: 0;
  overflow: auto;
}

:not(pre) > code[class*=language-],
pre[class*=language-] {
  background: var(--vz-light) !important;
}

/* Inline code */
:not(pre) > code[class*=language-] {
  padding: 0.1em;
  border-radius: 0.3em;
  white-space: normal;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: slategray;
}

.token.punctuation {
  color: #999;
}

.token.namespace {
  opacity: 0.7;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #e42222;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #690;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #9a6e3a;
  /* This background color was intended by the author of this theme. */
  background: hsla(0, 0%, 100%, 0.5);
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #07a;
}

.token.function,
.token.class-name {
  color: #dd4a68;
}

.token.regex,
.token.important,
.token.variable {
  color: #e90;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* Prism editor */
:not(pre) > code[class*=language-],
pre[class*=language-] {
  background: #eff2f7;
}

code[class*=language-],
pre[class*=language-] {
  color: #878a99;
  text-shadow: none;
}

.language-markup::-webkit-scrollbar {
  -webkit-appearance: none;
}
.language-markup::-webkit-scrollbar:vertical {
  width: 10px;
}
.language-markup::-webkit-scrollbar:horizontal {
  height: 10px;
}
.language-markup::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-body-color-rgb), 0.1);
  border-radius: 10px;
  border: 2px solid var(--vz-light);
}
.language-markup::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: var(--vz-light);
}

#scroll-top {
  width: 30px;
  height: 30px;
  position: fixed;
  bottom: 65px;
  right: 30px;
  background: #343a40;
  border-color: transparent;
  border-radius: 3px;
  color: #ffffff;
  transition: all 0.5s ease;
}

.btn-clipboard {
  position: absolute !important;
  right: 15px !important;
  z-index: 1 !important;
}

div.code-toolbar > .toolbar {
  opacity: 1 !important;
}
div.code-toolbar > .toolbar button {
  display: inline-block !important;
  margin: 0.375rem 0.5rem !important;
  padding: 0.25rem 0.75rem !important;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
  border-radius: 0.2rem !important;
  border: 1px solid rgba(53, 119, 241, 0.35) !important;
  background-color: transparent;
  color: #3577f1 !important;
  box-shadow: none !important;
}
div.code-toolbar > .toolbar button:focus {
  outline: none !important;
  box-shadow: none !important;
}
div.code-toolbar > .toolbar button:hover {
  background-color: #3577f1 !important;
  color: #fff !important;
}

.swal2-container .swal2-title {
  padding: 24px 24px 0;
  font-size: 20px;
  font-weight: var(--vz-font-weight-medium);
}

.swal2-popup {
  padding-bottom: 24px;
  border-radius: var(--vz-border-radius-lg);
  background-color: var(--vz-secondary-bg);
  color: var(--vz-body-color);
}
.swal2-popup .swal2-title {
  color: var(--vz-heading-color);
}
.swal2-popup .swal2-html-container {
  color: var(--vz-body-color);
}

.swal2-footer {
  border-top: 1px solid var(--vz-border-color);
  color: var(--vz-body-color);
}

.swal2-html-container {
  font-size: 16px;
}

.swal2-icon.swal2-question {
  border-color: var(--vz-info);
  color: var(--vz-info);
}
.swal2-icon.swal2-success [class^=swal2-success-line] {
  background-color: var(--vz-success);
}
.swal2-icon.swal2-success .swal2-success-ring {
  border-color: rgba(var(--vz-success-rgb), 0.3);
}
.swal2-icon.swal2-warning {
  border-color: var(--vz-warning);
  color: var(--vz-warning);
}

.swal2-styled:focus {
  box-shadow: none;
}

.swal2-loader {
  border-color: var(--vz-primary) transparent var(--vz-primary) transparent;
}

.swal2-timer-progress-bar {
  background-color: rgba(var(--vz-success-rgb), 0.4);
}

.swal2-progress-steps .swal2-progress-step {
  background: var(--vz-primary);
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
  background: var(--vz-primary);
}
.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {
  background: rgba(var(--vz-primary-rgb), 0.3);
}
.swal2-progress-steps .swal2-progress-step-line {
  background: var(--vz-primary);
}

.swal2-actions.swal2-loading .swal2-styled.swal2-confirm {
  border-left-color: var(--vz-primary) !important;
  border-right-color: var(--vz-primary) !important;
}

.swal2-file, .swal2-input, .swal2-textarea {
  border: 1px solid var(--vz-input-border-custom);
}
.swal2-file:focus, .swal2-input:focus, .swal2-textarea:focus {
  box-shadow: none;
  border-color: var(--vz-primary-border-subtle);
}

.swal2-input {
  height: auto;
  display: block;
  padding: 0.5rem 0.9rem;
  font-size: var(--vz-font-base);
  font-weight: var(--vz-font-weight-medium);
  line-height: 1.5;
  color: var(--vz-body-color);
  background-color: var(--vz-input-bg-custom);
  background-clip: padding-box;
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
}

.swal2-close {
  font-family: var(--vz-font-sans-serif);
  font-weight: var(--vz-font-weight-light);
  font-size: 28px;
}
.swal2-close:focus {
  box-shadow: none;
}
.swal2-close:hover {
  color: var(--vz-primary);
}

.swal2-validation-message {
  background-color: transparent;
}

.dropzone {
  min-height: 230px;
  border: 2px dashed var(--vz-border-color);
  background: var(--vz-secondary-bg);
  border-radius: 6px;
}
.dropzone .dz-message {
  font-size: 24px;
  width: 100%;
  margin: 1em 0;
}

.noUi-connect {
  background: var(--vz-success);
}

.noUi-handle {
  background: var(--vz-success);
  border: 2px solid var(--vz-card-bg);
  box-shadow: none;
}

.noUi-horizontal {
  height: 4px;
}
.noUi-horizontal .noUi-handle {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  right: -10px !important;
  top: -7px;
}
.noUi-horizontal .noUi-handle::before, .noUi-horizontal .noUi-handle::after {
  display: none;
}
.noUi-horizontal .noUi-handle:focus {
  outline: 0;
}

.noUi-pips-horizontal {
  height: 50px;
}

.noUi-tooltip {
  padding: 0.4rem 0.7rem;
  border-color: var(--vz-border-color);
  border-radius: var(--vz-border-radius);
  background-color: var(--vz-secondary-bg);
  color: var(--vz-body-color);
}

.noUi-vertical {
  width: 4px;
}
.noUi-vertical .noUi-handle {
  height: 16px;
  width: 16px;
  right: -8px;
  top: -12px;
  left: auto;
  border-radius: 50%;
}
.noUi-vertical .noUi-handle::before, .noUi-vertical .noUi-handle::after {
  display: none;
}
.noUi-vertical .noUi-handle:focus {
  outline: 0;
}
.noUi-vertical .noUi-origin {
  top: 0;
}

.noUi-value {
  font-size: 12px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 12px;
}

.noUi-value-horizontal {
  padding-top: 4px;
}

.noUi-target {
  box-shadow: none;
  background-color: var(--vz-light);
  border-color: var(--vz-light);
}

.noUi-touch-area:focus {
  outline: 0;
}

#red, #green, #blue {
  margin: 10px;
  display: inline-block;
  height: 200px;
}

#colorpicker {
  height: 240px;
  width: 310px;
  margin: 0 auto;
  padding: 10px;
  border: 1px solid var(--vz-border-color);
}

#result {
  margin: 60px 26px;
  height: 100px;
  width: 100px;
  display: inline-block;
  vertical-align: top;
  border: 1px solid var(--vz-border-color);
  box-shadow: 0 0 3px;
  border-radius: 7px;
}

#red .noUi-connect {
  background: var(--vz-danger);
}

#green .noUi-connect {
  background: var(--vz-success);
}

#blue .noUi-connect {
  background: var(--vz-primary);
}

.form-control.keyboard {
  max-width: 340px !important;
}

.example-val {
  font-size: 12px;
  color: var(--vz-secondary-color);
  display: block;
  margin: 15px 0;
}
.example-val:before {
  content: "Value: ";
  font-size: 12px;
  font-weight: 600;
}

.noUi-tooltip {
  display: none;
}

.noUi-active .noUi-tooltip {
  display: block;
}

.c-1-color {
  background: var(--vz-danger);
}

.c-2-color {
  background: var(--vz-warning);
}

.c-3-color {
  background: var(--vz-success);
}

.c-4-color {
  background: var(--vz-primary);
}

.c-5-color {
  background: #6559cc;
}

#slider-toggle {
  height: 50px;
}
#slider-toggle.off .noUi-handle {
  border-color: var(--vz-danger);
}

.noUi-marker {
  background-color: var(--vz-border-color);
}

.noUi-pips {
  color: var(--vz-body-color);
}

[data-slider-color=primary] .noUi-connect {
  background: var(--vz-primary);
}
[data-slider-color=primary] .noUi-handle {
  background: var(--vz-primary);
}
[data-slider-color=primary][data-slider-style=border] .noUi-handle, [data-slider-color=primary][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-primary);
}

[data-slider-color=secondary] .noUi-connect {
  background: var(--vz-secondary);
}
[data-slider-color=secondary] .noUi-handle {
  background: var(--vz-secondary);
}
[data-slider-color=secondary][data-slider-style=border] .noUi-handle, [data-slider-color=secondary][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-secondary);
}

[data-slider-color=success] .noUi-connect {
  background: var(--vz-success);
}
[data-slider-color=success] .noUi-handle {
  background: var(--vz-success);
}
[data-slider-color=success][data-slider-style=border] .noUi-handle, [data-slider-color=success][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-success);
}

[data-slider-color=info] .noUi-connect {
  background: var(--vz-info);
}
[data-slider-color=info] .noUi-handle {
  background: var(--vz-info);
}
[data-slider-color=info][data-slider-style=border] .noUi-handle, [data-slider-color=info][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-info);
}

[data-slider-color=warning] .noUi-connect {
  background: var(--vz-warning);
}
[data-slider-color=warning] .noUi-handle {
  background: var(--vz-warning);
}
[data-slider-color=warning][data-slider-style=border] .noUi-handle, [data-slider-color=warning][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-warning);
}

[data-slider-color=danger] .noUi-connect {
  background: var(--vz-danger);
}
[data-slider-color=danger] .noUi-handle {
  background: var(--vz-danger);
}
[data-slider-color=danger][data-slider-style=border] .noUi-handle, [data-slider-color=danger][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-danger);
}

[data-slider-color=light] .noUi-connect {
  background: var(--vz-light);
}
[data-slider-color=light] .noUi-handle {
  background: var(--vz-light);
}
[data-slider-color=light][data-slider-style=border] .noUi-handle, [data-slider-color=light][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-light);
}

[data-slider-color=dark] .noUi-connect {
  background: var(--vz-dark);
}
[data-slider-color=dark] .noUi-handle {
  background: var(--vz-dark);
}
[data-slider-color=dark][data-slider-style=border] .noUi-handle, [data-slider-color=dark][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-dark);
}

[data-slider-color=pink] .noUi-connect {
  background: var(--vz-pink);
}
[data-slider-color=pink] .noUi-handle {
  background: var(--vz-pink);
}
[data-slider-color=pink][data-slider-style=border] .noUi-handle, [data-slider-color=pink][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-pink);
}

[data-slider-color=purple] .noUi-connect {
  background: var(--vz-purple);
}
[data-slider-color=purple] .noUi-handle {
  background: var(--vz-purple);
}
[data-slider-color=purple][data-slider-style=border] .noUi-handle, [data-slider-color=purple][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-purple);
}

[data-slider-color=orange] .noUi-connect {
  background: var(--vz-orange);
}
[data-slider-color=orange] .noUi-handle {
  background: var(--vz-orange);
}
[data-slider-color=orange][data-slider-style=border] .noUi-handle, [data-slider-color=orange][data-slider-style=square] .noUi-handle {
  border-color: var(--vz-orange);
}

[data-slider-size=lg].noUi-horizontal {
  height: 12px;
}
[data-slider-size=lg].noUi-horizontal .noUi-handle {
  width: 24px;
  height: 24px;
}

[data-slider-size=md].noUi-horizontal {
  height: 8px;
}
[data-slider-size=md].noUi-horizontal .noUi-handle {
  width: 20px;
  height: 20px;
}

[data-slider-size=sm].noUi-horizontal {
  height: 4px;
}
[data-slider-size=sm].noUi-horizontal .noUi-handle {
  width: 16px;
  height: 16px;
}

[data-slider-style=line].noUi-horizontal .noUi-handle {
  width: 8px;
  border-radius: 4px;
  right: -8px;
}
[data-slider-style=line].noUi-vertical .noUi-handle {
  height: 8px;
  border-radius: 4px;
  top: -3px;
}

[data-slider-style=border] .noUi-handle {
  border-color: var(--vz-success);
  background-color: var(--vz-card-bg);
}

[data-slider-style=square] .noUi-handle {
  border-radius: 0px;
  transform: rotate(45deg);
  height: 10px;
  width: 10px;
  top: -4px;
  border-color: var(--vz-success);
  background-color: var(--vz-card-bg);
}

.nested-list .list-group-item {
  background-color: rgba(var(--vz-primary-rgb), 0.05);
  border-color: rgba(var(--vz-primary-rgb), 0.05);
}

.nested-list, .nested-1, .nested-2, .nested-3 {
  margin-top: 5px;
}

.nested-sortable-handle .handle {
  position: absolute;
  left: 0;
  top: 0;
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
}
.nested-sortable-handle .list-group-item {
  padding-left: 42px;
}

.shepherd-element {
  background: var(--vz-secondary-bg);
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
}

.shepherd-has-title .shepherd-content .shepherd-header {
  background-color: var(--vz-light);
  padding: 0.5rem 0.75rem;
}
.shepherd-has-title .shepherd-content .shepherd-cancel-icon {
  color: rgba(var(--vz-body-color-rgb), 0.75);
}
.shepherd-has-title .shepherd-content .shepherd-cancel-icon:hover {
  color: rgba(var(--vz-body-color-rgb), 1);
}

.shepherd-element.shepherd-has-title[data-popper-placement^=bottom] > .shepherd-arrow:before {
  background-color: var(--vz-light);
}

.shepherd-title {
  font-size: 15px;
  font-weight: var(--vz-font-weight-medium);
  color: var(--vz-body-color);
}

.shepherd-text {
  padding: 0.75rem;
  font-size: var(--vz-font-base);
  color: var(--vz-body-color);
}

.shepherd-button.btn-success:not(:disabled):hover {
  background: var(--vz-success-text-emphasis);
  color: #fff;
}
.shepherd-button.btn-light:not(:disabled):hover {
  background: rgba(var(--vz-light-rgb), 0.75);
  color: var(--vz-body-color);
}
.shepherd-button.btn-primary:not(:disabled):hover {
  background: var(--vz-primary-text-emphasis);
  color: #fff;
}

.shepherd-footer {
  padding: 0 0.75rem 0.75rem;
}

.shepherd-arrow,
.shepherd-arrow:before {
  content: "\ea75";
  font-family: "remixicon";
  font-size: 24px;
  z-index: 1;
  background-color: transparent !important;
  transform: rotate(0deg);
  color: var(--vz-primary);
}

.shepherd-element[data-popper-placement^=bottom] > .shepherd-arrow {
  top: -18px;
}

.shepherd-button {
  margin-right: 0.5rem;
}

.swiper-button-next, .swiper-button-prev {
  height: 32px;
  width: 32px;
  background-color: rgba(var(--vz-primary-rgb), 0.2);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  border-radius: 8px;
}
.swiper-button-next::after, .swiper-button-prev::after {
  font-family: remixicon;
  font-size: 28px;
  color: rgba(var(--vz-primary-rgb), 1);
  transition: all 0.3s ease;
}

.swiper-button-prev::after {
  content: "\ea64" !important;
}

.swiper-button-next::after {
  content: "\ea6e" !important;
}

.swiper-pagination-bullet {
  width: 22px;
  height: 5px;
  background-color: #fff;
  border-radius: 50px;
  box-shadow: var(--vz-element-shadow);
}
.swiper-pagination-bullet .swiper-pagination-bullet-active {
  opacity: 1;
}

.dynamic-pagination .swiper-pagination-bullet {
  width: 8px;
  height: 8px;
  background-color: #fff;
  opacity: 0.5;
  transition: all 0.5s ease;
}
.dynamic-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  width: 20px;
}

.swiper-pagination-fraction {
  color: #fff;
  font-size: 16px;
  background-color: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
}

.pagination-custom .swiper-pagination-bullet {
  height: 25px;
  width: 25px;
  line-height: 25px;
  border-radius: 8px;
  background-color: #fff;
  opacity: 0.5;
  transition: all 0.5s ease;
}
.pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active {
  color: var(--vz-secondary);
  opacity: 1;
}

.swiper-pagination-progressbar {
  height: 6px !important;
  background-color: rgba(var(--vz-success-rgb), 0.25);
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background-color: var(--vz-success);
}

.swiper-scrollbar {
  background-color: rgba(255, 255, 255, 0.35);
  -webkit-backdrop-filter: blur(2px);
          backdrop-filter: blur(2px);
  padding: 1.2px;
  height: 6px !important;
}
.swiper-scrollbar .swiper-scrollbar-drag {
  background-color: #fff;
}

.swiper-pagination-dark .swiper-pagination-bullet {
  background-color: var(--vz-secondary);
}
.swiper-pagination-dark .dynamic-pagination .swiper-pagination-bullet {
  background-color: var(--vz-secondary);
}
.swiper-pagination-dark.pagination-custom .swiper-pagination-bullet {
  color: #fff;
}
.swiper-pagination-dark.pagination-custom .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
}
.swiper-pagination-dark.swiper-scrollbar {
  background-color: rgba(var(--vz-dark-rgb), 0.35);
}

.multi-wrapper {
  border: none;
  position: relative;
}
.multi-wrapper::before {
  content: "\ea61";
  position: absolute;
  font-family: "remixicon";
  left: 50%;
  transform: translateX(-50%);
  bottom: 86px;
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  border-radius: 50%;
  color: var(--vz-secondary-color);
  background-color: var(--vz-light);
  z-index: 1;
}
.multi-wrapper .non-selected-wrapper {
  border: 1px solid var(--vz-input-border-custom);
  background-color: var(--vz-input-bg-custom);
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.multi-wrapper .selected-wrapper::-webkit-scrollbar, .multi-wrapper .non-selected-wrapper::-webkit-scrollbar {
  -webkit-appearance: none;
}
.multi-wrapper .selected-wrapper::-webkit-scrollbar:vertical, .multi-wrapper .non-selected-wrapper::-webkit-scrollbar:vertical {
  width: 10px;
}
.multi-wrapper .selected-wrapper::-webkit-scrollbar:horizontal, .multi-wrapper .non-selected-wrapper::-webkit-scrollbar:horizontal {
  height: 9px;
}
.multi-wrapper .selected-wrapper::-webkit-scrollbar-thumb, .multi-wrapper .non-selected-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-dark-rgb), 0.2);
  border-radius: 10px;
  border: 2px solid var(--vz-input-bg-custom);
}
.multi-wrapper .selected-wrapper::-webkit-scrollbar-track, .multi-wrapper .non-selected-wrapper::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: var(--vz-input-bg-custom);
}
.multi-wrapper .item-group .group-label {
  font-size: 12px;
}
.multi-wrapper .item {
  color: var(--vz-body-color);
}
.multi-wrapper .item:hover {
  background-color: rgba(var(--vz-primary-rgb), 0.1);
}
.multi-wrapper .selected-wrapper {
  border: 1px solid var(--vz-input-border-custom);
  background: var(--vz-input-bg-custom);
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.multi-wrapper .search-input {
  flex: 0 0 auto;
  padding: 0.5rem 0.9rem;
  font-size: var(--vz-font-base);
  color: var(--vz-body-color);
  background-color: var(--vz-input-bg-custom);
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  border-radius: var(--vz-border-radius);
  margin-bottom: 16px;
}
.multi-wrapper .search-input::-moz-placeholder {
  color: var(--vz-secondary-color);
}
.multi-wrapper .search-input::placeholder {
  color: var(--vz-secondary-color);
}
.multi-wrapper .header {
  font-weight: var(--vz-font-weight-semibold);
  color: var(--vz-gray-600);
}

.pcr-app {
  background: var(--vz-secondary-bg);
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  border-radius: 4px;
  border: 1px solid var(--vz-border-color);
}

.pickr .pcr-button {
  border: 4px solid var(--vz-secondary-bg);
  box-shadow: 0px 0px 0 2px var(--vz-border-color);
  border-radius: 50%;
  box-shadow: var(--vz-element-shadow);
}
.pickr .pcr-button::after, .pickr .pcr-button::before {
  border-radius: 50%;
}

.pcr-app[data-theme=classic] .pcr-selection .pcr-color-preview {
  margin-right: 0.75em;
  margin-left: 0;
}
.pcr-app[data-theme=classic] .pcr-selection .pcr-color-chooser, .pcr-app[data-theme=classic] .pcr-selection .pcr-color-opacity {
  margin-left: 0.75em;
  margin-right: 0;
}
.pcr-app[data-theme=monolith] .pcr-result {
  min-width: 100%;
}
.pcr-app .pcr-interaction .pcr-type.active {
  background: var(--vz-primary);
}
.pcr-app .pcr-interaction .pcr-result {
  background-color: var(--vz-input-bg-custom);
  color: var(--vz-body-color);
  border: 1px solid var(--vz-input-border-custom);
  border-radius: var(--vz-border-radius);
}
.pcr-app .pcr-interaction input {
  border-radius: var(--vz-border-radius) !important;
}
.pcr-app .pcr-interaction input:focus {
  box-shadow: none;
  background-color: var(--vz-input-bg-custom);
  border-color: var(--vz-primary-border-subtle);
}
.pcr-app .pcr-interaction .pcr-save {
  background: var(--vz-success) !important;
}
.pcr-app .pcr-interaction .pcr-clear, .pcr-app .pcr-interaction .pcr-cancel {
  background: var(--vz-danger) !important;
}

.filepond--root {
  margin-bottom: 0;
}
.filepond--root[data-style-panel-layout~=circle] .filepond--drop-label label {
  font-size: 14px;
}

.filepond--panel-root {
  border: 2px dashed var(--vz-border-color);
  background: var(--vz-secondary-bg);
}

.filepond--drop-label {
  color: var(--vz-body-color);
}
.filepond--drop-label label {
  font-weight: var(--vz-font-weight-medium);
}

.filepond--credits {
  display: none;
}

.filepond--item-panel {
  background-color: var(--vz-primary) !important;
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  height: auto;
}

.input-step {
  border: 1px solid var(--vz-input-border-custom);
  display: inline-flex;
  overflow: visible;
  height: 37.5px;
  border-radius: var(--vz-border-radius);
  background: var(--vz-input-bg-custom);
  padding: 4px;
}
.input-step input {
  width: 4em;
  height: 100%;
  text-align: center;
  border: 0;
  background: transparent;
  color: var(--vz-body-color);
  border-radius: var(--vz-border-radius);
}
.input-step input:focus-visible {
  outline: 0;
}
.input-step button {
  width: 1.4em;
  font-weight: 300;
  height: 100%;
  line-height: 0.1em;
  font-size: 1.4em;
  padding: 0.2em !important;
  background: var(--vz-light);
  color: var(--vz-body-color);
  border: none;
  border-radius: var(--vz-border-radius);
}
.input-step.light {
  background: var(--vz-light);
}
.input-step.light button {
  background-color: var(--vz-input-bg-custom);
}
.input-step.light-input {
  background: var(--vz-light);
}
.input-step.light-input input {
  background-color: var(--vz-input-bg-custom);
}
.input-step.full-width {
  display: flex;
  width: 100%;
}
.input-step.full-width button {
  flex-shrink: 0;
}
.input-step.full-width input {
  flex-grow: 1;
}

.input-step.step-primary button {
  background-color: var(--vz-primary);
  color: #fff;
}

.input-step.step-secondary button {
  background-color: var(--vz-secondary);
  color: #fff;
}

.input-step.step-success button {
  background-color: var(--vz-success);
  color: #fff;
}

.input-step.step-info button {
  background-color: var(--vz-info);
  color: #fff;
}

.input-step.step-warning button {
  background-color: var(--vz-warning);
  color: #fff;
}

.input-step.step-danger button {
  background-color: var(--vz-danger);
  color: #fff;
}

.input-step.step-light button {
  background-color: var(--vz-light);
  color: #fff;
}

.input-step.step-dark button {
  background-color: var(--vz-dark);
  color: #fff;
}

.input-step.step-pink button {
  background-color: var(--vz-pink);
  color: #fff;
}

.input-step.step-purple button {
  background-color: var(--vz-purple);
  color: #fff;
}

.input-step.step-orange button {
  background-color: var(--vz-orange);
  color: #fff;
}

.ck {
  font-family: var(--vz-font-sans-serif) !important;
}
.ck.ck-reset_all, .ck.ck-reset_all * {
  color: var(--vz-body-color) !important;
}
.ck.ck-toolbar {
  background: rgba(var(--vz-light-rgb), 0.75) !important;
}
.ck p {
  margin-bottom: 0;
}
.ck.ck-toolbar {
  border: 1px solid var(--vz-input-border-custom) !important;
}
.ck.ck-toolbar.ck-toolbar_grouping > .ck-toolbar__items {
  flex-wrap: wrap !important;
}
.ck.ck-toolbar .ck.ck-toolbar__separator {
  background: transparent !important;
}
.ck.ck-editor__main > .ck-editor__editable {
  border-top: 0 !important;
  background-color: var(--vz-secondary-bg) !important;
  border-color: var(--vz-input-border-custom) !important;
  box-shadow: none !important;
}
.ck.ck-dropdown__panel {
  background: var(--vz-secondary-bg) !important;
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  animation-name: DropDownSlide;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  border-radius: var(--vz-border-radius-lg);
}
.ck.ck-list {
  background: var(--vz-secondary-bg) !important;
}
.ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_ne, .ck.ck-dropdown .ck-dropdown__panel.ck-dropdown__panel_se {
  left: 0;
  right: auto !important;
}
.ck.ck-editor__editable_inline[dir=ltr] {
  text-align: left !important;
}
.ck.ck-dropdown__panel {
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12) !important;
  border-radius: var(--vz-border-radius-lg) !important;
  border: 1px solid var(--vz-border-color) !important;
}

.ck.ck-button:focus, .ck.ck-button:active,
a.ck.ck-button:focus,
a.ck.ck-button:active {
  box-shadow: none !important;
  border: 1px solid var(--vz-light) !important;
}
.ck.ck-button:not(.ck-disabled):hover,
a.ck.ck-button:not(.ck-disabled):hover {
  background: var(--vz-light) !important;
}
.ck.ck-button.ck-on,
a.ck.ck-button.ck-on {
  background: var(--vz-light) !important;
}

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,
.ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners {
  border-radius: 0.25rem !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.ck-rounded-corners .ck.ck-editor__main > .ck-editor__editable,
.ck.ck-editor__main > .ck-editor__editable.ck-rounded-corners {
  border-radius: 0.25rem !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.ck-editor__editable {
  min-height: 245px !important;
}

.ck[class*=ck-heading_heading] {
  font-weight: var(--vz-font-weight-medium) !important;
}

.ck.ck-button.ck-on:not(.ck-disabled):hover, .ck.ck-button.ck-on:not(.ck-disabled):active,
a.ck.ck-button.ck-on:not(.ck-disabled):hover,
a.ck.ck-button.ck-on:not(.ck-disabled):active {
  box-shadow: none !important;
}

.ck.ck-tooltip .ck-tooltip__text {
  background: var(--vz-dark) !important;
  color: var(--vz-light) !important;
}
.ck.ck-input-text {
  background: var(--vz-input-bg-custom) !important;
  border: 1px solid var(--vz-input-border-custom) !important;
}
.ck.ck-input-text:focus {
  border: 1px solid var(--vz-primary-border-subtle) !important;
  box-shadow: none !important;
}
.ck.ck-balloon-panel {
  background: var(--vz-secondary-bg) !important;
  border: 1px solid var(--vz-border-color) !important;
}
.ck.ck-balloon-panel[class*=arrow_n]:after {
  border-bottom-color: var(--vz-secondary-bg) !important;
}
.ck.ck-balloon-panel[class*=arrow_n]::before {
  border-bottom-color: var(--vz-border-color) !important;
}

.ck.ck-labeled-field-view > .ck.ck-labeled-field-view__input-wrapper > .ck.ck-label {
  background: var(--vz-secondary-bg) !important;
}

.ck-editor-reverse .ck-editor {
  display: flex;
  flex-direction: column-reverse;
}
.ck-editor-reverse .ck.ck-editor__main > .ck-editor__editable {
  border: 1px solid var(--vz-input-border-custom) !important;
  border-bottom: 0 !important;
}
.ck-editor-reverse .ck-rounded-corners .ck.ck-editor__main > .ck-editor__editable,
.ck-editor-reverse .ck.ck-editor__main > .ck-editor__editable.ck-rounded-corners {
  border-radius: 0.25rem !important;
  border-bottom-left-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.ck-editor-reverse .ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,
.ck-editor-reverse .ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners {
  border-radius: 0.25rem !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

[dir=rtl] .ck.ck-toolbar > .ck-toolbar__items {
  flex-direction: row-reverse;
}

.ql-editor {
  text-align: left;
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
  padding-right: 0;
}
.ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
  margin-right: 0.3em;
  text-align: right;
}

.ql-container {
  font-family: var(--vz-font-sans-serif);
}
.ql-container.ql-snow {
  border-color: var(--vz-input-border-custom);
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.ql-bubble {
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  border-radius: var(--vz-border-radius);
}

.ql-toolbar {
  font-family: var(--vz-font-sans-serif) !important;
}
.ql-toolbar span {
  outline: none !important;
  color: var(--vz-body-color);
}
.ql-toolbar span:hover {
  color: var(--vz-primary) !important;
}
.ql-toolbar.ql-snow {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-color: var(--vz-input-border-custom);
}
.ql-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: transparent;
}
.ql-toolbar.ql-snow .ql-picker-options {
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  border-radius: var(--vz-border-radius-lg);
}

.ql-snow .ql-stroke,
.ql-snow .ql-script,
.ql-snow .ql-strike svg {
  stroke: var(--vz-body-color);
}
.ql-snow .ql-fill {
  fill: var(--vz-body-color);
}
.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
  right: 0;
  left: auto;
}
.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: var(--vz-secondary-color);
}

.ql-snow .ql-picker-options {
  background-color: var(--vz-secondary-bg);
  border-color: var(--vz-border-color) !important;
}

.gridjs-container {
  color: #30323e;
  padding: 0;
  display: block;
}

.gridjs-wrapper {
  box-shadow: none;
  border: 1px solid var(--vz-border-color);
  border-radius: 0px;
}
.gridjs-wrapper::-webkit-scrollbar {
  -webkit-appearance: none;
}
.gridjs-wrapper::-webkit-scrollbar:vertical {
  width: 12px;
}
.gridjs-wrapper::-webkit-scrollbar:horizontal {
  height: 12px;
}
.gridjs-wrapper::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-dark-rgb), 0.075);
  border-radius: 10px;
  border: 2px solid var(--vz-secondary-bg);
}
.gridjs-wrapper::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: var(--vz-secondary-bg);
}

.gridjs-footer {
  border: none !important;
  padding: 12px 0 0;
}

.gridjs-table {
  width: 100%;
}

.gridjs-tbody, td.gridjs-td {
  background-color: transparent;
}

th.gridjs-th,
td.gridjs-td {
  border: 1px solid var(--vz-border-color);
  padding: 0.75rem 0.6rem;
}

th.gridjs-th {
  border-top: 0;
  color: #30323e;
  background-color: rgba(var(--vz-light-rgb), 0.75);
}
th.gridjs-th-sort:focus, th.gridjs-th-sort:hover {
  background-color: rgba(var(--vz-light-rgb), 0.85);
}

.gridjs-head {
  padding-top: 0;
}

.gridjs-footer {
  box-shadow: none;
  border: 1px solid var(--vz-border-color);
  border-top: 0;
  background-color: transparent;
}

.gridjs-summary {
  color: var(--vz-secondary-color);
  margin-top: 8px !important;
}

.gridjs-pagination .gridjs-pages button {
  margin-left: 0.3rem;
  border-radius: 0.25rem !important;
  border: 1px solid var(--vz-border-color);
  background-color: var(--vz-secondary-bg);
  color: var(--vz-link-color);
}
.gridjs-pagination .gridjs-pages button:last-child {
  border-right: 1px solid var(--vz-border-color);
}
.gridjs-pagination .gridjs-pages button:disabled, .gridjs-pagination .gridjs-pages button:hover:disabled, .gridjs-pagination .gridjs-pages button[disabled] {
  color: var(--vz-secondary-color);
  background-color: var(--vz-secondary-bg);
}
.gridjs-pagination .gridjs-pages button:hover {
  background-color: var(--vz-tertiary-bg);
  color: var(--vz-link-hover-color);
}
.gridjs-pagination .gridjs-pages button:focus {
  box-shadow: none;
}
.gridjs-pagination .gridjs-pages button:last-child:hover, .gridjs-pagination .gridjs-pages button:first-child:hover {
  background-color: transparent;
}
.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
  background-color: var(--vz-primary);
  color: #fff;
  border-color: var(--vz-primary);
  font-weight: var(--vz-font-weight-medium);
}

.gridjs-search {
  position: relative;
  float: left;
}
.gridjs-search::before {
  content: "\f0d1";
  font-family: "remixicon";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
  font-size: 14px;
  color: var(--vz-secondary-color);
}

input.gridjs-input {
  border-color: var(--vz-input-border-custom);
  background-color: var(--vz-input-bg-custom);
  color: var(--vz-body-color);
  line-height: 1.5;
  padding: 0.5rem 0.9rem 0.5rem 2.025rem;
  border-radius: var(--vz-border-radius);
  font-size: var(--vz-font-base);
}
input.gridjs-input:focus {
  box-shadow: none;
  border-color: var(--vz-primary-border-subtle);
  background-color: var(--vz-input-bg-custom);
}
input.gridjs-input::-moz-placeholder {
  color: var(--vz-secondary-color);
}
input.gridjs-input::placeholder {
  color: var(--vz-secondary-color);
}

th.gridjs-th .gridjs-th-content {
  float: none;
  display: inline-block;
  vertical-align: middle;
  font-weight: var(--vz-font-weight-semibold);
}

button.gridjs-sort {
  float: none;
  display: inline-block;
  vertical-align: middle;
  width: 10px;
  height: 20px;
}

th.gridjs-th-sort .gridjs-th-content {
  width: calc(100% - 10px);
}

button.gridjs-sort-asc, button.gridjs-sort-desc {
  background-size: 7px;
}

.table-card .gridjs-head {
  padding: 16px 16px 5px;
}
.table-card .gridjs-wrapper {
  border-top: 0;
  border-radius: 0;
  border-width: 1px 0;
}
.table-card .gridjs-footer {
  padding: 8px 16px;
}

.gridjs-tr-selected td {
  background-color: rgba(var(--vz-body-color-rgb), 0.04);
}
.gridjs-tr-selected .gridjs-td .gridjs-checkbox[type=checkbox] {
  background-color: var(--vz-primary);
  border-color: var(--vz-primary);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23fff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M20 6 9 17l-5-5'/%3E%3C/svg%3E");
}

.gridjs-td .gridjs-checkbox {
  width: 1.25em;
  height: 1.25em;
  vertical-align: top;
  background-color: var(--vz-input-bg-custom);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: var(--vz-border-width) solid var(--vz-border-color-translucent);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
}
.gridjs-td .gridjs-checkbox[type=checkbox] {
  border-radius: 0.25em;
}

.gridjs-border-none td.gridjs-td, .gridjs-border-none th.gridjs-th {
  border-right-width: 0;
  border-left-width: 0;
}

.gridjs-loading-bar {
  background-color: var(--vz-secondary-bg);
}

[data-bs-theme=dark] button:is(.gridjs-sort-neutral, .gridjs-sort-asc, .gridjs-sort-desc) {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.listjs-pagination {
  margin-bottom: 0;
  justify-content: flex-end;
  gap: 8px;
}
.listjs-pagination li .page {
  display: block;
  padding: 0.375rem 0.75rem;
  color: var(--vz-link-color);
  background-color: var(--vz-secondary-bg);
  border: var(--vz-border-width) solid var(--vz-border-color);
  border-radius: 0.25rem;
}
.listjs-pagination li.active .page {
  color: #fff;
  background-color: var(--vz-primary);
  border-color: var(--vz-primary);
}

.pagination-wrap {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  display: flex;
  align-items: center;
}
.pagination-wrap a {
  text-decoration: none;
  display: inline-block;
}

.pagination-next,
.pagination-prev {
  color: var(--vz-primary);
  font-weight: var(--vz-font-weight-medium);
  padding: 0.375rem 0.75rem;
  background-color: var(--vz-secondary-bg);
  border: var(--vz-border-width) solid var(--vz-border-color);
  border-radius: 0.25rem;
}
.pagination-next:hover,
.pagination-prev:hover {
  color: rgb(148.3888888889, 183.2222222222, 247.6111111111);
}
.pagination-next.disabled,
.pagination-prev.disabled {
  color: var(--vz-secondary-color);
  cursor: default;
}
.pagination-next.disabled:hover,
.pagination-prev.disabled:hover {
  color: var(--vz-secondary-color);
}

.apex-charts {
  min-height: 10px !important;
}
.apex-charts text {
  font-family: var(--vz-font-sans-serif) !important;
}
.apex-charts .apexcharts-canvas {
  margin: 0 auto;
}

@keyframes opaque {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes resizeanim {
  0%, to {
    opacity: 0;
  }
}
.apexcharts-canvas {
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.apexcharts-canvas ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 6px;
}
.apexcharts-canvas ::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}
.apexcharts-canvas :is(.apexcharts-reset-zoom-icon, .apexcharts-selection-icon, .apexcharts-zoom-icon).apexcharts-selected svg {
  fill: var(--vz-primary);
}
@media screen and (min-width: 768px) {
  .apexcharts-canvas:hover .apexcharts-toolbar {
    opacity: 1;
  }
}

.apexcharts-inner {
  position: relative;
}

.apexcharts-gridline {
  pointer-events: none;
  stroke: rgba(var(--vz-light-rgb), 1);
}

.apexcharts-text tspan {
  font-family: inherit;
}

.apexcharts-yaxis text,
.apexcharts-xaxis text {
  font-family: var(--vz-font-sans-serif) !important;
  fill: #878a99;
}

.apexcharts-title-text,
.apexcharts-subtitle-text {
  fill: #adb5bd;
}

.legend-mouseover-inactive {
  transition: 0.15s ease all;
  opacity: 0.2;
}

.apexcharts-legend-text {
  color: #878a99 !important;
  font-family: var(--vz-font-sans-serif) !important;
  font-size: 13px !important;
  padding-left: 15px;
  margin-left: -15px;
}

.apexcharts-series-collapsed {
  opacity: 0;
}

.apexcharts-xaxis-tick {
  stroke: var(--vz-border-color);
}

.apexcharts-marker {
  stroke: var(--vz-secondary-bg);
}

.apexcharts-tooltip {
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15) !important;
  cursor: default;
  font-size: 14px;
  opacity: 0;
  pointer-events: none;
  position: absolute;
  top: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  white-space: nowrap;
  z-index: 12;
  transition: 0.15s ease all;
  font-family: var(--vz-font-sans-serif) !important;
}
.apexcharts-tooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}
.apexcharts-tooltip.apexcharts-theme-light {
  border: 1px solid var(--vz-border-color) !important;
  background: rgba(var(--vz-secondary-bg-rgb), 0.96) !important;
}
.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  background: var(--vz-secondary-bg) !important;
  border-bottom: 1px solid var(--vz-border-color) !important;
}
.apexcharts-tooltip.apexcharts-theme-dark {
  color: #fff;
  background: rgba(30, 30, 30, 0.8);
}
.apexcharts-tooltip.apexcharts-theme-dark .apexcharts-tooltip-title {
  background: rgba(0, 0, 0, 0.7);
  border-bottom: 1px solid #333;
}
.apexcharts-tooltip * {
  font-family: inherit !important;
}
.apexcharts-tooltip .apexcharts-tooltip-title {
  font-family: var(--vz-font-sans-serif) !important;
  padding: 6px;
  font-size: 15px;
  margin-bottom: 4px;
}

.apexcharts-tooltip-text-goals-value,
.apexcharts-tooltip-text-y-value,
.apexcharts-tooltip-text-z-value {
  display: inline-block;
  margin-left: 5px;
  font-weight: 600;
}

.apexcharts-tooltip-text-goals-label:empty,
.apexcharts-tooltip-text-goals-value:empty,
.apexcharts-tooltip-text-y-label:empty,
.apexcharts-tooltip-text-y-value:empty,
.apexcharts-tooltip-text-z-value:empty,
.apexcharts-tooltip-title:empty {
  display: none;
}

.apexcharts-tooltip-text-goals-label,
.apexcharts-tooltip-text-goals-value {
  padding: 6px 0 5px;
}

.apexcharts-tooltip-goals-group,
.apexcharts-tooltip-text-goals-label,
.apexcharts-tooltip-text-goals-value {
  display: flex;
}

.apexcharts-tooltip-text-goals-label:not(:empty),
.apexcharts-tooltip-text-goals-value:not(:empty) {
  margin-top: -6px;
}

.apexcharts-tooltip-marker {
  width: 12px;
  height: 12px;
  position: relative;
  top: 0;
  margin-right: 10px;
  border-radius: 50%;
}

.apexcharts-tooltip-series-group {
  padding: 0 10px;
  display: none;
  text-align: left;
  justify-content: left;
  align-items: center;
}
.apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-marker {
  opacity: 1;
}
.apexcharts-tooltip-series-group.apexcharts-active, .apexcharts-tooltip-series-group:last-child {
  padding-bottom: 4px;
}

.apexcharts-tooltip-series-group-hidden {
  opacity: 0;
  height: 0;
  line-height: 0;
  padding: 0 !important;
}

.apexcharts-tooltip-y-group {
  padding: 6px 0 5px;
}

.apexcharts-custom-tooltip,
.apexcharts-tooltip-box {
  padding: 4px 8px;
}

.apexcharts-tooltip-boxPlot {
  display: flex;
  flex-direction: column-reverse;
}

.apexcharts-tooltip-box > div {
  margin: 4px 0;
}
.apexcharts-tooltip-box span.value {
  font-weight: 700;
}

.apexcharts-tooltip-rangebar {
  padding: 5px 8px;
}
.apexcharts-tooltip-rangebar .category {
  font-weight: 600;
  color: #777;
}
.apexcharts-tooltip-rangebar .series-name {
  font-weight: 700;
  display: block;
  margin-bottom: 5px;
}

.apexcharts-xaxistooltip,
.apexcharts-yaxistooltip {
  opacity: 0;
  pointer-events: none;
  color: #373d3f;
  font-size: 13px;
  text-align: center;
  border-radius: 2px;
  position: absolute;
  z-index: 10;
  background: #eceff1;
  border: 1px solid #90a4ae;
}

.apexcharts-xaxistooltip {
  padding: 9px 10px;
  transition: 0.15s ease all;
}
.apexcharts-xaxistooltip.apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}
.apexcharts-xaxistooltip::after, .apexcharts-xaxistooltip::before {
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.apexcharts-xaxistooltip::after {
  border-color: transparent;
  border-width: 6px;
  margin-left: -6px;
}
.apexcharts-xaxistooltip::before {
  border-color: transparent;
  border-width: 7px;
  margin-left: -7px;
}
.apexcharts-xaxistooltip.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-xaxistooltip-bottom::before, .apexcharts-xaxistooltip-bottom::after {
  bottom: 100%;
}
.apexcharts-xaxistooltip-bottom::after {
  border-bottom-color: #eceff1;
}
.apexcharts-xaxistooltip-bottom::before {
  border-bottom-color: #90a4ae;
}
.apexcharts-xaxistooltip-bottom.apexcharts-theme-dark::before, .apexcharts-xaxistooltip-bottom.apexcharts-theme-dark::after {
  border-bottom-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-xaxistooltip-top::before, .apexcharts-xaxistooltip-top::after {
  top: 100%;
}
.apexcharts-xaxistooltip-top::after {
  border-top-color: #eceff1;
}
.apexcharts-xaxistooltip-top::before {
  border-top-color: #90a4ae;
}
.apexcharts-xaxistooltip-top.apexcharts-theme-dark::before, .apexcharts-xaxistooltip-top.apexcharts-theme-dark::after {
  border-top-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip {
  padding: 4px 10px;
}
.apexcharts-yaxistooltip .apexcharts-theme-dark {
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.5);
  color: #fff;
}
.apexcharts-yaxistooltip::before, .apexcharts-yaxistooltip::after {
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.apexcharts-yaxistooltip::after {
  border-color: transparent;
  border-width: 6px;
  margin-top: -6px;
}
.apexcharts-yaxistooltip::before {
  border-color: transparent;
  border-width: 7px;
  margin-top: -7px;
}
.apexcharts-yaxistooltip.apexcharts-active {
  opacity: 1;
}

.apexcharts-yaxistooltip-left::before, .apexcharts-yaxistooltip-left::after {
  left: 100%;
}
.apexcharts-yaxistooltip-left::after {
  border-left-color: #eceff1;
}
.apexcharts-yaxistooltip-left::before {
  border-left-color: #90a4ae;
}
.apexcharts-yaxistooltip-left.apexcharts-theme-dark::before, .apexcharts-yaxistooltip-left.apexcharts-theme-dark::after {
  border-left-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-right::before, .apexcharts-yaxistooltip-right::after {
  right: 100%;
}
.apexcharts-yaxistooltip-right::after {
  border-right-color: #eceff1;
}
.apexcharts-yaxistooltip-right::before {
  border-right-color: #90a4ae;
}
.apexcharts-yaxistooltip-right.apexcharts-theme-dark::before, .apexcharts-yaxistooltip-right.apexcharts-theme-dark::after {
  border-right-color: rgba(0, 0, 0, 0.5);
}

.apexcharts-yaxistooltip-hidden {
  display: none;
}

.apexcharts-xcrosshairs,
.apexcharts-ycrosshairs {
  pointer-events: none;
  opacity: 0;
  transition: 0.15s ease all;
}
.apexcharts-xcrosshairs.apexcharts-active,
.apexcharts-ycrosshairs.apexcharts-active {
  opacity: 1;
  transition: 0.15s ease all;
}

.apexcharts-ycrosshairs-hidden {
  opacity: 0;
}

.apexcharts-selection-rect {
  cursor: move;
}

.svg_select_boundingRect,
.svg_select_points_rot {
  pointer-events: none;
  opacity: 0;
  visibility: hidden;
}

.apexcharts-selection-rect + g .svg_select_boundingRect,
.apexcharts-selection-rect + g .svg_select_points_rot {
  opacity: 0;
  visibility: hidden;
}
.apexcharts-selection-rect + g .svg_select_points_l,
.apexcharts-selection-rect + g .svg_select_points_r {
  cursor: ew-resize;
  opacity: 1;
  visibility: visible;
}

.svg_select_points {
  fill: #efefef;
  stroke: #333;
  rx: 2;
}

.apexcharts-svg.apexcharts-zoomable.hovering-zoom {
  cursor: crosshair;
}
.apexcharts-svg.apexcharts-zoomable.hovering-pan {
  cursor: move;
}

.apexcharts-menu-icon,
.apexcharts-pan-icon,
.apexcharts-reset-icon,
.apexcharts-selection-icon,
.apexcharts-toolbar-custom-icon,
.apexcharts-zoom-icon,
.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
  cursor: pointer;
  width: 20px;
  height: 20px;
  line-height: 24px;
  color: #6e8192;
  text-align: center;
}

:is(.apexcharts-menu-icon, .apexcharts-reset-icon, .apexcharts-zoom-icon, .apexcharts-zoomin-icon, .apexcharts-zoomout-icon) svg {
  fill: #6e8192;
}

.apexcharts-selection-icon svg {
  fill: #444;
  transform: scale(0.76);
}

.apexcharts-theme-dark :is(.apexcharts-menu-icon, .apexcharts-pan-icon, .apexcharts-reset-icon, .apexcharts-selection-icon, .apexcharts-toolbar-custom-icon, .apexcharts-zoom-icon, .apexcharts-zoomin-icon, .apexcharts-zoomout-icon) svg {
  fill: #f3f4f5;
}
.apexcharts-theme-dark .apexcharts-menu {
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
}

.apexcharts-theme-light .apexcharts-menu-icon:hover svg,
.apexcharts-theme-light .apexcharts-reset-icon:hover svg,
.apexcharts-theme-light .apexcharts-selection-icon:not(.apexcharts-selected):hover svg,
.apexcharts-theme-light .apexcharts-zoom-icon:not(.apexcharts-selected):hover svg,
.apexcharts-theme-light .apexcharts-zoomout-icon:hover svg {
  fill: #333;
}
.apexcharts-theme-light .apexcharts-menu-item:hover {
  background: #eee;
}

.apexcharts-menu-icon,
.apexcharts-selection-icon {
  position: relative;
}

.apexcharts-reset-icon {
  margin-left: 5px;
}

.apexcharts-menu-icon,
.apexcharts-reset-icon,
.apexcharts-zoom-icon {
  transform: scale(0.85);
}

.apexcharts-zoomin-icon,
.apexcharts-zoomout-icon {
  transform: scale(0.7);
}

.apexcharts-zoomout-icon {
  margin-right: 3px;
}

.apexcharts-pan-icon {
  transform: scale(0.62);
  position: relative;
  left: 1px;
  top: 0;
}
.apexcharts-pan-icon svg {
  fill: #fff;
  stroke: #6e8192;
  stroke-width: 2;
}
.apexcharts-pan-icon:not(.apexcharts-selected):hover svg {
  stroke: #333;
}

.apexcharts-toolbar {
  position: absolute;
  z-index: 11;
  max-width: 176px;
  text-align: right;
  border-radius: 3px;
  padding: 0 6px 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.apexcharts-menu {
  background: #fff;
  position: absolute;
  top: 100%;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 3px;
  right: 10px;
  opacity: 0;
  min-width: 110px;
  transition: 0.15s ease all;
  pointer-events: none;
}
.apexcharts-menu .apexcharts-menu-open {
  opacity: 1;
  pointer-events: all;
  transition: 0.15s ease all;
}

.apexcharts-menu-item {
  padding: 6px 7px;
  font-size: 12px;
  cursor: pointer;
}

.apexcharts-canvas .apexcharts-element-hidden,
.apexcharts-datalabel.apexcharts-element-hidden,
.apexcharts-hide .apexcharts-series-points {
  opacity: 0;
}

.apexcharts-datalabel,
.apexcharts-datalabel-label,
.apexcharts-datalabel-value,
.apexcharts-datalabels,
.apexcharts-pie-label {
  cursor: default;
  pointer-events: none;
}

.apexcharts-pie-label-delay {
  opacity: 0;
  animation-name: opaque;
  animation-duration: 0.3s;
  animation-fill-mode: forwards;
  animation-timing-function: ease;
}

.apexcharts-annotation-rect,
.apexcharts-area-series .apexcharts-area,
.apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-gridline,
.apexcharts-line,
.apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,
.apexcharts-point-annotation-label,
.apexcharts-radar-series path,
.apexcharts-radar-series polygon,
.apexcharts-toolbar svg,
.apexcharts-tooltip .apexcharts-marker,
.apexcharts-xaxis-annotation-label,
.apexcharts-yaxis-annotation-label,
.apexcharts-zoom-rect {
  pointer-events: none;
}

.apexcharts-marker {
  transition: 0.15s ease all;
}

.resize-triggers {
  animation: 1ms resizeanim;
  visibility: hidden;
  opacity: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.contract-trigger:before,
.resize-triggers,
.resize-triggers > div {
  content: " ";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.resize-triggers > div {
  height: 100%;
  width: 100%;
  background: #eee;
  overflow: auto;
}
.resize-triggers::before {
  overflow: hidden;
  width: 200%;
  height: 200%;
}

#chart-year,
#chart-quarter {
  width: 96%;
  max-width: 48%;
  box-shadow: none;
  padding-left: 0;
  padding-top: 20px;
  background: var(--vz-secondary-bg);
  border: 1px solid var(--vz-border-color);
}

#chart-year {
  float: left;
  position: relative;
  transition: 1s ease transform;
  z-index: 3;
}
#chart-year.chart-quarter-activated {
  transform: translateX(0);
  transition: 1s ease transform;
}

#chart-quarter {
  float: left;
  position: relative;
  z-index: -2;
  transition: 1s ease transform;
}
#chart-quarter.active {
  transition: 1.1s ease-in-out transform;
  transform: translateX(0);
  z-index: 1;
}

@media screen and (min-width: 480px) {
  #chart-year {
    transform: translateX(50%);
  }
  #chart-quarter {
    transform: translateX(-50%);
  }
}
.apexcharts-treemap rect,
.apexcharts-heatmap-series rect {
  stroke: var(--vz-secondary-bg);
}

.apexcharts-pie-series path,
.apexcharts-bar-series path {
  stroke: var(--vz-secondary-bg);
}

.apexcharts-radialbar .apexcharts-datalabels-group text {
  fill: var(--vz-body-color);
}

.apexcharts-radialbar-track path {
  stroke: var(--vz-border-color);
}

.apexcharts-radar-series polygon,
.apexcharts-radar-series line {
  stroke: var(--vz-border-color);
}

.apexcharts-pie circle,
.apexcharts-pie line {
  stroke: var(--vz-border-color);
}
.apexcharts-pie text {
  fill: var(--vz-white);
}

.apexcharts-xaxistooltip.apexcharts-theme-light {
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  color: var(--vz-secondary-color);
  background: var(--vz-secondary-bg);
  border: 1px solid var(--vz-border-color);
  font-family: var(--vz-font-sans-serif);
}
.apexcharts-xaxistooltip.apexcharts-theme-light::before {
  border-bottom-color: var(--vz-border-color);
}

.effect-chart :is(.apexcharts-pie-series, .apexcharts-bar-series) path {
  clip-path: polygon(50% 2%, 100% 0, 100% 100%, 0 100%, 0 0);
}

.apexcharts-grid-borders line {
  stroke: var(--vz-border-color);
}

#monochrome_polar_area svg {
  background: transparent !important;
}

.chartjs-chart {
  max-height: 320px;
}

.e-charts {
  height: 350px;
}

.e-charts-height {
  height: 300px;
}

.gmaps, .gmaps-panaroma {
  height: 300px;
  background: #f3f6f9;
  border-radius: 3px;
}

.gmaps-overlay {
  display: block;
  text-align: center;
  color: #fff;
  font-size: 16px;
  line-height: 40px;
  background: #3577f1;
  border-radius: 4px;
  padding: 10px 20px;
}

.gmaps-overlay_arrow {
  left: 50%;
  margin-left: -16px;
  width: 0;
  height: 0;
  position: absolute;
}
.gmaps-overlay_arrow.above {
  bottom: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-top: 16px solid var(--vz-primary);
}
.gmaps-overlay_arrow.below {
  top: -15px;
  border-left: 16px solid transparent;
  border-right: 16px solid transparent;
  border-bottom: 16px solid var(--vz-primary);
}

.autoComplete_wrapper {
  display: block;
}
.autoComplete_wrapper > input {
  display: block;
  width: 100%;
  height: auto;
  padding: 0.5rem 0.9rem;
  font-size: var(--vz-font-base);
  font-weight: var(--vz-font-weight-medium);
  line-height: 1.5;
  color: var(--vz-body-color);
  background-color: var(--vz-input-bg-custom);
  background-clip: padding-box;
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  border-radius: var(--vz-border-radius);
  background-image: none;
}
.autoComplete_wrapper > input::-moz-placeholder {
  padding: 0 !important;
  color: var(--vz-secondary-color) !important;
  font-size: var(--vz-font-base) !important;
}
.autoComplete_wrapper > input::placeholder {
  padding: 0 !important;
  color: var(--vz-secondary-color) !important;
  font-size: var(--vz-font-base) !important;
}
.autoComplete_wrapper > input:focus {
  border: var(--vz-border-width) solid var(--vz-primary-border-subtle);
  color: var(--vz-body-color);
}
.autoComplete_wrapper > input:hover {
  color: var(--vz-body-color);
}
.autoComplete_wrapper > ul {
  border-radius: 0.25rem;
  border-color: var(--vz-border-color);
  background-color: var(--vz-secondary-bg);
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  padding: 0;
  overflow: auto;
  max-height: 160px;
  margin: 0;
  animation-name: DropDownSlide;
  animation-duration: 0.3s;
  animation-fill-mode: both;
}
.autoComplete_wrapper > ul > li {
  font-size: var(--vz-font-base);
  margin: 0;
  padding: 0.35rem 1.2rem;
  border-radius: 0;
  background-color: var(--vz-secondary-bg);
  color: var(--vz-body-color);
}
.autoComplete_wrapper > ul > li mark {
  color: var(--vz-danger);
  font-weight: var(--vz-font-weight-semibold);
  padding: 1px;
}
.autoComplete_wrapper > ul > li[aria-selected=true], .autoComplete_wrapper > ul > li:hover {
  color: var(--vz-body-color);
  background-color: var(--vz-tertiary-bg);
}
.autoComplete_wrapper > ul .no_result {
  padding: 0.7rem 1.2rem;
  font-style: italic;
  font-weight: var(--vz-font-weight-medium);
}

.jvm-tooltip {
  border-radius: 3px;
  background-color: var(--vz-primary);
  font-family: var(--vz-font-sans-serif);
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  padding: 5px 9px;
}

.jvm-container text {
  font-family: var(--vz-font-sans-serif);
  font-size: var(--vz-font-base);
  fill: var(--vz-body-color);
}

.jvm-zoom-btn {
  background-color: var(--vz-primary);
}

.leaflet-map {
  height: 300px;
}
.leaflet-map.leaflet-container {
  z-index: 0;
  font-family: var(--vz-font-family-primary);
}

:root {
  --fc-border-color: var(--vz-border-color);
  --fc-page-bg-color: var(--vz-secondary-bg);
  --fc-neutral-bg-color: var(--vz-light);
}

.fc td,
.fc th {
  border: var(--vz-border-width) solid var(--vz-border-color);
}

.fc .fc-toolbar h2 {
  font-size: 16px;
  line-height: 30px;
  text-transform: uppercase;
}
@media (max-width: 767.98px) {
  .fc .fc-toolbar .fc-left,
  .fc .fc-toolbar .fc-right,
  .fc .fc-toolbar .fc-center {
    float: none;
    display: block;
    text-align: center;
    clear: both;
    margin: 10px 0;
  }
  .fc .fc-toolbar > * > * {
    float: none;
  }
  .fc .fc-toolbar .fc-today-button {
    display: none;
  }
}
.fc .fc-toolbar .btn {
  text-transform: capitalize;
}
.fc .fc-col-header-cell {
  background-color: var(--vz-light);
}
.fc .fc-col-header-cell-cushion {
  display: block;
  padding: 8px 4px;
  color: var(--vz-body-color);
}
.fc .fc-daygrid-day-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: var(--vz-font-weight-medium);
  margin: 2px;
  color: var(--vz-body-color);
}
.fc .fc-daygrid-day.fc-day-today {
  background-color: rgba(var(--vz-primary-rgb), 0.1);
}
.fc .fc-daygrid-day.fc-day-today .fc-daygrid-day-number {
  background-color: var(--vz-primary);
  color: #fff;
}
.fc .fc-daygrid-day.fc-day-today {
  background-color: rgba(var(--vz-primary-rgb), 0.1);
}
.fc .fc-timegrid-col.fc-day-today {
  background-color: rgba(var(--vz-primary-rgb), 0.1);
}
.fc .fc-list-event:hover td {
  background: transparent;
}
.fc .fc-list-event-title a {
  color: #fff !important;
}
.fc .fc-col-header,
.fc .fc-daygrid-body,
.fc .fc-scrollgrid-sync-table {
  width: 100% !important;
}
.fc .fc-scrollgrid-section > * {
  border-left: var(--vz-border-width) solid var(--vz-border-color);
  border-top: var(--vz-border-width) solid var(--vz-border-color);
}
.fc .fc-scrollgrid {
  border: 0;
}
.fc .fc-scrollgrid-section-liquid > td {
  border-top: 0;
}
.fc .fc-multimonth {
  border-color: var(--vz-border-color);
}

.fc-theme-bootstrap a:not([href]) {
  color: var(--vz-body-color);
}

.fc-event {
  color: #fff;
}

.fc th.fc-widget-header {
  background: #e9ebec;
  line-height: 20px;
  padding: 10px 0;
  text-transform: uppercase;
  font-weight: var(--vz-font-weight-bold);
}

.fc-unthemed .fc-content,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-list-heading td,
.fc-unthemed .fc-list-view,
.fc-unthemed .fc-popover,
.fc-unthemed .fc-row,
.fc-unthemed tbody,
.fc-unthemed td,
.fc-unthemed th,
.fc-unthemed thead {
  border-color: #e9ebec;
}
.fc-unthemed td.fc-today {
  background: rgb(245.8, 247.525, 250.4);
}

.fc-button {
  background: var(--vz-secondary-bg);
  border-color: var(--vz-border-color);
  color: #495057;
  text-transform: capitalize;
  box-shadow: none;
  padding: 6px 12px !important;
  height: auto !important;
}

.fc .fc-multimonth-multicol .fc-daygrid-more-link {
  border: 1px solid var(--vz-primary);
  display: block;
  float: none;
  padding: 1px;
  font-weight: var(--vz-font-weight-medium);
  font-family: var(--vz-font-family-primary);
}

.fc-state-down,
.fc-state-active,
.fc-state-disabled {
  background-color: var(--vz-primary);
  color: #fff;
  text-shadow: none;
}

.fc-event {
  border-radius: 2px;
  border: none;
  cursor: move;
  font-size: 0.8125rem;
  margin: 5px 7px;
  padding: 5px 5px;
  text-align: center;
}

.fc-theme-standard .fc-popover .fc-popover-header {
  padding: 8px 14px;
}

.fc-event,
.fc-event-dot {
  background-color: var(--vz-primary);
}

.fc-daygrid-dot-event.fc-event-mirror,
.fc-daygrid-dot-event:hover {
  background-color: var(--vz-primary);
}

.fc-event-title,
.fc-sticky {
  font-weight: var(--vz-font-weight-semibold) !important;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fc-daygrid-event-dot {
  border-color: #fff !important;
  display: none;
}

.fc-event-time {
  display: none;
}

.fc-event .fc-content {
  color: #fff;
}

#external-events .external-event {
  text-align: left;
  padding: 8px 16px;
  margin: 6px 0;
}

.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark .fc-content {
  color: var(--vz-light);
}

.fc-prev-button,
.fc-next-button {
  position: relative;
  padding: 6px 8px !important;
}

.fc-toolbar-chunk .fc-button-group {
  box-shadow: var(--vz-element-shadow);
}
.fc-toolbar-chunk .fc-button-group .fc-button {
  color: var(--vz-secondary);
  background-color: rgba(var(--vz-secondary-rgb), 0.15);
  border: none;
  box-shadow: none;
}
.fc-toolbar-chunk .fc-button-group .fc-button:hover, .fc-toolbar-chunk .fc-button-group .fc-button.active {
  color: #fff;
  background-color: var(--vz-secondary);
}
.fc-toolbar-chunk .fc-today-button {
  background-color: var(--vz-secondary) !important;
  border: var(--vz-secondary);
}

.fc .fc-button-primary:not(:disabled).fc-button-active,
.fc .fc-button-primary:not(:disabled):active {
  background-color: var(--vz-secondary) !important;
  border-color: var(--vz-secondary) !important;
  color: #fff !important;
}
.fc .fc-button-primary {
  text-transform: capitalize;
}

@media (max-width: 575.98px) {
  .fc-toolbar {
    flex-direction: column;
    gap: 16px;
  }
}

#upcoming-event-list .card:last-child {
  margin-bottom: 6px !important;
}

#event-modal .event-details {
  display: none;
}
#event-modal .view-event .event-form {
  display: none;
}
#event-modal .view-event #event-category-tag {
  display: none;
}
#event-modal .view-event .event-details {
  display: block;
}

.fc-daygrid-event-harness .fc-daygrid-event {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.fc-timegrid-event-harness .fc-timegrid-event {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.fc-timegrid-slots table tr {
  border-color: var(--vz-border-color) !important;
}

.fc-list-table {
  border-color: var(--vz-border-color);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-primary-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-primary-subtle .fc-event-main {
  color: var(--vz-primary) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-primary-subtle .fc-event-title {
  color: var(--vz-primary) !important;
}

.fc-list-table .fc-list-event.bg-primary-subtle {
  color: var(--vz-primary) !important;
}
.fc-list-table .fc-list-event.bg-primary-subtle .fc-list-event-title > a {
  color: var(--vz-primary) !important;
}
.fc-list-table .fc-list-event.bg-primary-subtle .fc-list-event-dot {
  border-color: var(--vz-primary);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-secondary-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-secondary-subtle .fc-event-main {
  color: var(--vz-secondary) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-secondary-subtle .fc-event-title {
  color: var(--vz-secondary) !important;
}

.fc-list-table .fc-list-event.bg-secondary-subtle {
  color: var(--vz-secondary) !important;
}
.fc-list-table .fc-list-event.bg-secondary-subtle .fc-list-event-title > a {
  color: var(--vz-secondary) !important;
}
.fc-list-table .fc-list-event.bg-secondary-subtle .fc-list-event-dot {
  border-color: var(--vz-secondary);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-success-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-success-subtle .fc-event-main {
  color: var(--vz-success) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-success-subtle .fc-event-title {
  color: var(--vz-success) !important;
}

.fc-list-table .fc-list-event.bg-success-subtle {
  color: var(--vz-success) !important;
}
.fc-list-table .fc-list-event.bg-success-subtle .fc-list-event-title > a {
  color: var(--vz-success) !important;
}
.fc-list-table .fc-list-event.bg-success-subtle .fc-list-event-dot {
  border-color: var(--vz-success);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-info-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-info-subtle .fc-event-main {
  color: var(--vz-info) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-info-subtle .fc-event-title {
  color: var(--vz-info) !important;
}

.fc-list-table .fc-list-event.bg-info-subtle {
  color: var(--vz-info) !important;
}
.fc-list-table .fc-list-event.bg-info-subtle .fc-list-event-title > a {
  color: var(--vz-info) !important;
}
.fc-list-table .fc-list-event.bg-info-subtle .fc-list-event-dot {
  border-color: var(--vz-info);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-warning-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-warning-subtle .fc-event-main {
  color: var(--vz-warning) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-warning-subtle .fc-event-title {
  color: var(--vz-warning) !important;
}

.fc-list-table .fc-list-event.bg-warning-subtle {
  color: var(--vz-warning) !important;
}
.fc-list-table .fc-list-event.bg-warning-subtle .fc-list-event-title > a {
  color: var(--vz-warning) !important;
}
.fc-list-table .fc-list-event.bg-warning-subtle .fc-list-event-dot {
  border-color: var(--vz-warning);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-danger-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-danger-subtle .fc-event-main {
  color: var(--vz-danger) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-danger-subtle .fc-event-title {
  color: var(--vz-danger) !important;
}

.fc-list-table .fc-list-event.bg-danger-subtle {
  color: var(--vz-danger) !important;
}
.fc-list-table .fc-list-event.bg-danger-subtle .fc-list-event-title > a {
  color: var(--vz-danger) !important;
}
.fc-list-table .fc-list-event.bg-danger-subtle .fc-list-event-dot {
  border-color: var(--vz-danger);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-light-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-light-subtle .fc-event-main {
  color: var(--vz-light) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-light-subtle .fc-event-title {
  color: var(--vz-light) !important;
}

.fc-list-table .fc-list-event.bg-light-subtle {
  color: var(--vz-light) !important;
}
.fc-list-table .fc-list-event.bg-light-subtle .fc-list-event-title > a {
  color: var(--vz-light) !important;
}
.fc-list-table .fc-list-event.bg-light-subtle .fc-list-event-dot {
  border-color: var(--vz-light);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-dark-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-dark-subtle .fc-event-main {
  color: var(--vz-dark) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-dark-subtle .fc-event-title {
  color: var(--vz-dark) !important;
}

.fc-list-table .fc-list-event.bg-dark-subtle {
  color: var(--vz-dark) !important;
}
.fc-list-table .fc-list-event.bg-dark-subtle .fc-list-event-title > a {
  color: var(--vz-dark) !important;
}
.fc-list-table .fc-list-event.bg-dark-subtle .fc-list-event-dot {
  border-color: var(--vz-dark);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-pink-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-pink-subtle .fc-event-main {
  color: var(--vz-pink) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-pink-subtle .fc-event-title {
  color: var(--vz-pink) !important;
}

.fc-list-table .fc-list-event.bg-pink-subtle {
  color: var(--vz-pink) !important;
}
.fc-list-table .fc-list-event.bg-pink-subtle .fc-list-event-title > a {
  color: var(--vz-pink) !important;
}
.fc-list-table .fc-list-event.bg-pink-subtle .fc-list-event-dot {
  border-color: var(--vz-pink);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-purple-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-purple-subtle .fc-event-main {
  color: var(--vz-purple) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-purple-subtle .fc-event-title {
  color: var(--vz-purple) !important;
}

.fc-list-table .fc-list-event.bg-purple-subtle {
  color: var(--vz-purple) !important;
}
.fc-list-table .fc-list-event.bg-purple-subtle .fc-list-event-title > a {
  color: var(--vz-purple) !important;
}
.fc-list-table .fc-list-event.bg-purple-subtle .fc-list-event-dot {
  border-color: var(--vz-purple);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-orange-subtle .fc-event-title,
.fc-daygrid-event-harness .fc-daygrid-event.bg-orange-subtle .fc-event-main {
  color: var(--vz-orange) !important;
}

.fc-timegrid-event-harness .fc-timegrid-event.bg-orange-subtle .fc-event-title {
  color: var(--vz-orange) !important;
}

.fc-list-table .fc-list-event.bg-orange-subtle {
  color: var(--vz-orange) !important;
}
.fc-list-table .fc-list-event.bg-orange-subtle .fc-list-event-title > a {
  color: var(--vz-orange) !important;
}
.fc-list-table .fc-list-event.bg-orange-subtle .fc-list-event-dot {
  border-color: var(--vz-orange);
}

.fc-daygrid-event-harness .fc-daygrid-event.bg-dark-subtle .fc-event-main, .fc-daygrid-event-harness .fc-daygrid-event.bg-dark-subtle .fc-event-title {
  color: var(--vz-dark-text-emphasis) !important;
}

.fc-direction-ltr {
  direction: ltr;
}
.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
  margin-left: 0.75em;
}

.fg-emoji-picker {
  width: 250px !important;
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15) !important;
  top: auto !important;
  bottom: 130px;
}
.fg-emoji-picker * {
  font-family: var(--vz-font-sans-serif) !important;
  color: var(--vz-body-color) !important;
}
@media (max-width: 991.98px) {
  .fg-emoji-picker {
    left: 14px !important;
    top: auto !important;
    bottom: 118px;
  }
}
.fg-emoji-picker .fg-emoji-picker-container-title {
  color: #212529 !important;
}
.fg-emoji-picker .fg-emoji-picker-search {
  height: 40px !important;
}
.fg-emoji-picker .fg-emoji-picker-search input {
  background-color: var(--vz-input-bg-custom) !important;
  color: var(--vz-body-color) !important;
  padding: 0.5rem 0.9rem !important;
  font-size: var(--vz-font-base) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::-moz-placeholder {
  color: var(--vz-secondary-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search input::placeholder {
  color: var(--vz-secondary-color) !important;
}
.fg-emoji-picker .fg-emoji-picker-search svg {
  fill: var(--vz-body-color) !important;
  right: 11px;
  top: 12px;
}
.fg-emoji-picker .fg-emoji-picker-categories {
  background-color: #f3f6f9 !important;
}
.fg-emoji-picker .fg-emoji-picker-categories li.active {
  background-color: rgba(var(--vz-primary-rgb), 0.2);
}
.fg-emoji-picker .fg-emoji-picker-categories a:hover {
  background-color: rgba(var(--vz-primary-rgb), 0.2);
}

.fg-emoji-picker-grid > li:hover {
  background-color: rgba(var(--vz-primary-rgb), 0.2) !important;
}

a.fg-emoji-picker-close-button {
  background-color: rgb(229.4, 235.8, 242.2) !important;
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
  text-align: center;
  padding: 50px;
  font-weight: var(--vz-font-weight-semibold);
  --vz-table-accent-bg: var(--vz-secondary-bg);
}

table.dataTable > thead .sorting:before,
table.dataTable > thead .sorting_asc:before,
table.dataTable > thead .sorting_desc:before,
table.dataTable > thead .sorting_asc_disabled:before,
table.dataTable > thead .sorting_desc_disabled:before {
  content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-arrow-narrow-up' width='24' height='24' stroke-width='2' stroke='currentColor' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M0 0h24v24H0z' stroke='none'/%3E%3Cpath d='M12 5v14M16 9l-4-4M8 9l4-4'/%3E%3C/svg%3E") !important;
  inset-inline-end: 0.2rem !important;
  top: 0.6rem !important;
  transform: scale(0.5);
}

table.dataTable > thead .sorting:after,
table.dataTable > thead .sorting_asc:after,
table.dataTable > thead .sorting_desc:after,
table.dataTable > thead .sorting_asc_disabled:after,
table.dataTable > thead .sorting_desc_disabled:after {
  content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-tabler icon-tabler-arrow-narrow-down' width='24' height='24' stroke-width='2' stroke='currentColor' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M0 0h24v24H0z' stroke='none'/%3E%3Cpath d='M12 5v14M16 15l-4 4M8 15l4 4'/%3E%3C/svg%3E") !important;
  transform: scale(0.5);
  top: 0.4rem !important;
}

table.dataTable > tbody > tr.child span.dtr-title {
  font-weight: var(--vz-font-weight-semibold);
  min-width: 150px;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control::before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control::before {
  border-color: var(--vz-secondary-bg);
}
table.dataTable > tbody > tr.child span.dtr-title {
  min-width: 135px;
}

.table-card .dataTables_length,
.table-card .dataTables_filter {
  padding: 1rem 1rem;
  padding-bottom: 0px;
}
.table-card .dataTables_info,
.table-card .dataTables_paginate {
  padding: 1rem 1rem;
}
.table-card div.dataTables_wrapper .col-md-6 {
  width: 100%;
}
.table-card div.dataTables_wrapper div.dataTables_filter input {
  width: calc(100% - 52px);
}
.table-card div.dataTables_wrapper div.dataTables_filter label {
  display: block;
}

div.dtr-modal div.dtr-modal-display {
  background-color: var(--vz-secondary-bg);
  border-color: var(--vz-border-color);
  box-shadow: none;
  height: 70%;
}
div.dtr-modal div.dtr-modal-content {
  padding: 1.25rem;
}
div.dtr-modal div.dtr-modal-content h2 {
  font-size: calc(var(--vz-font-base) * 1.25);
  font-weight: var(--vz-font-weight-semibold);
  margin-bottom: 15px;
}
div.dtr-modal div.dtr-modal-content .dtr-details tr td {
  padding: 0.75rem 0.6rem;
}
div.dtr-modal div.dtr-modal-close {
  font-size: 24px;
  top: 9px;
  right: 11px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  background-color: var(--vz-secondary-bg);
  border-color: var(--vz-border-color);
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: var(--vz-light);
}

.dt-buttons :is(button.dt-button, div.dt-button, a.dt-button, input.dt-button) {
  border-color: var(--vz-border-color);
  background: var(--vz-light);
  color: var(--vz-body-color);
}

:is(button, div, a, input):is(.dt-button:hover:not(.disabled)) {
  border-color: var(--vz-border-color);
  background: var(--vz-light);
  color: var(--vz-body-color);
}

:is(button, div, a, input):is(.dt-button:active:not(.disabled)),
:is(button, div, a, input):is(.dt-button.active:not(.disabled)) {
  border-color: var(--vz-border-color);
  background: var(--vz-light);
  color: var(--vz-body-color);
  box-shadow: none;
}

:is(button, div, a, input):is(.dt-button:focus:not(.disabled)) {
  border-color: var(--vz-border-color);
  background: var(--vz-light);
  color: var(--vz-body-color);
  text-shadow: none;
  box-shadow: none;
}

:is(button, div, a, input):is(.dt-button:active:not(.disabled):hover:not(.disabled)),
:is(button, div, a, input):is(.dt-button.active:not(.disabled):hover:not(.disabled)) {
  border-color: var(--vz-border-color);
  background: var(--vz-light);
  color: var(--vz-body-color);
  text-shadow: none;
  box-shadow: none;
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
  gap: 4px;
}

.select2.select2-container {
  width: 100% !important;
}

.select2-container .select2-selection--single {
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  height: calc(1.5em + 1rem + calc(var(--vz-border-width) * 2));
  background-color: var(--vz-input-bg-custom);
  outline: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
  line-height: 36px;
  padding-left: 12px;
  color: var(--vz-body-color);
  font-weight: 500;
}
.select2-container .select2-selection--single .select2-selection__arrow {
  height: 34px;
  width: 34px;
  /*rtl:ignore*/
  right: 3px;
}
.select2-container .select2-selection--single .select2-selection__arrow b {
  border-color: var(--vz-input-border-custom) transparent transparent transparent;
  border-width: 6px 6px 0 6px;
}

.select2-container--open .select2-selection--single .select2-selection__arrow b {
  border-color: transparent transparent var(--vz-input-border-custom) transparent !important;
  border-width: 0 6px 6px 6px !important;
}

.select2-results__option {
  padding: 6px 12px;
  font-weight: 500;
}

.select2-dropdown {
  border: var(--vz-dropdown-border-width) solid var(--vz-border-color);
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  background-color: var(--vz-secondary-bg);
  z-index: 1056;
}

.select2-container--default .select2-results__option--selected {
  background-color: var(--vz-tertiary-bg);
}
.select2-container--default .select2-search--dropdown {
  padding: 10px;
  background-color: var(--vz-secondary-bg);
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  outline: none;
  border: 1px solid var(--vz-input-border-custom);
  background-color: var(--vz-input-bg-custom);
  color: var(--vz-body-color);
  border-radius: var(--vz-border-radius);
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--vz-primary);
}
.select2-container--default .select2-results__option[aria-selected=true] {
  background-color: var(--vz-secondary-bg);
  color: var(--vz-dropdown-link-active-color);
}
.select2-container--default .select2-results__option[aria-selected=true]:hover {
  background-color: var(--vz-primary);
  color: #fff;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__display {
  padding-left: 36px;
  padding-right: 5px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  /*rtl:ignore*/
  right: 1px;
  /*rtl:ignore*/
  left: auto;
}

.select2-container .select2-selection--multiple {
  min-height: calc(1.5em + 1rem + calc(var(--vz-border-width) * 2));
  border: var(--vz-border-width) solid var(--vz-input-border-custom) !important;
  background-color: var(--vz-input-bg-custom);
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
  padding: 1px 4px;
}
.select2-container .select2-selection--multiple .select2-search__field {
  border: 0;
  color: var(--vz-body-color);
}
.select2-container .select2-selection--multiple .select2-selection__choice {
  background-color: var(--vz-primary);
  border: none;
  color: #fff;
  border-radius: 3px;
  padding: 3px;
  margin-top: 6px;
}
.select2-container .select2-selection--multiple .select2-selection__choice__remove {
  color: #fff;
  margin-right: 7px;
  border-color: var(--vz-primary-text-emphasis);
  padding: 0 8px;
  top: 3px;
}
.select2-container .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: #fff;
  background-color: var(--vz-primary);
}
.select2-container .select2-search--inline .select2-search__field {
  margin-top: 7px;
  height: 22px;
  font-family: var(--vz-font-sans-serif);
}
.select2-container .select2-search textarea::-moz-placeholder {
  color: var(--vz-secondary-color);
}
.select2-container .select2-search textarea::placeholder {
  color: var(--vz-secondary-color);
}

.select2-container--default.select2-container--disabled .select2-selection--single, .select2-container--default.select2-container--disabled .select2-selection--multiple {
  background-color: var(--vz-tertiary-bg);
  cursor: default;
}

.toastify {
  padding: 12px 16px;
  color: #fff;
  display: inline-block;
  box-shadow: 0 3px 6px -1px rgba(0, 0, 0, 0.12), 0 10px 36px -4px rgba(77, 96, 232, 0.3);
  background: var(--vz-success);
  position: fixed;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-radius: 2px;
  cursor: pointer;
  text-decoration: none;
  max-width: calc(50% - 20px);
  z-index: 2147483647;
}
.toastify.on {
  opacity: 1;
}

.toast-close {
  opacity: 0.4;
  padding: 0 5px;
  position: relative;
  left: 4px;
  margin-left: 4px;
  border: none;
  background: none;
  color: #fff;
}

.toastify-right {
  right: 15px;
}

.toastify-left {
  left: 15px;
}
.toastify-left .toast-close {
  left: -4px;
  margin-left: 0;
  margin-right: 4px;
}

.toastify-top {
  top: -150px;
}

.toastify-bottom {
  bottom: -150px;
}

.toastify-rounded {
  border-radius: 25px;
}

.toastify-avatar {
  width: 1.5em;
  height: 1.5em;
  margin: -7px 5px;
  border-radius: 2px;
}

.toastify-center {
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  max-width: fit-content;
  max-width: -moz-fit-content;
}

@media only screen and (max-width: 360px) {
  .toastify-right, .toastify-left {
    margin-left: auto;
    margin-right: auto;
    left: 0;
    right: 0;
    max-width: -moz-fit-content;
    max-width: fit-content;
  }
}
.choices {
  position: relative;
  margin-bottom: 24px;
  font-size: 16px;
}
.choices:focus {
  outline: none;
}
.choices:last-child {
  margin-bottom: 0;
}
.choices.is-disabled .choices__inner,
.choices.is-disabled .choices__input {
  background-color: rgba(var(--vz-light-rgb), 0.75);
  cursor: not-allowed;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.choices.is-disabled .choices__item {
  cursor: not-allowed;
}
.choices [hidden] {
  display: none !important;
}
.choices[data-type*=select-one] {
  cursor: pointer;
}
.choices[data-type*=select-one] .choices__inner {
  padding-bottom: 0.25rem;
}
.choices[data-type*=select-one] .choices__input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.9rem;
  background-color: var(--vz-input-bg-custom);
  border: 1px solid var(--vz-input-border-custom);
  font-size: var(--vz-font-base);
  border-radius: var(--vz-border-radius);
  color: var(--vz-body-color);
  margin-bottom: 10px;
}
.choices[data-type*=select-one] .choices__button {
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
  padding: 0;
  background-size: 8px;
  position: absolute;
  top: 50%;
  right: 0;
  left: auto;
  margin-top: -10px;
  margin-right: 25px;
  margin-left: 0;
  height: 20px;
  width: 20px;
  border-radius: 10em;
  opacity: 0.5;
}
.choices[data-type*=select-one] .choices__button:hover, .choices[data-type*=select-one] .choices__button:focus {
  opacity: 1;
}
.choices[data-type*=select-one] .choices__button:focus {
  box-shadow: 0px 0px 0px 2px #00bcd4;
}
.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button {
  display: none;
}
.choices[data-type*=select-one]:after {
  content: "\f0140";
  position: absolute;
  border: 0;
  border-color: #212529 transparent transparent;
  right: 11.5px;
  left: auto;
  font-family: "Material Design Icons";
  width: auto;
  height: auto;
  margin-top: 0;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
.choices[data-type*=select-one].is-open:after {
  margin-top: 0;
  transform: translateY(-50%) rotate(-180deg);
}
.choices[data-type*=select-multiple] .choices__inner, .choices[data-type*=text] .choices__inner {
  cursor: text;
  padding-right: 0.9rem;
}
.choices[data-type*=select-multiple] .choices__button, .choices[data-type*=text] .choices__button {
  position: relative;
  display: inline-block;
  margin-top: 0;
  margin-right: -4px;
  margin-bottom: 0;
  margin-left: 8px;
  padding-left: 16px;
  border-left: 1px solid rgba(255, 255, 255, 0.5);
  border-right: 0;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);
  background-size: 8px;
  width: 8px;
  line-height: 1;
  opacity: 0.75;
  border-radius: 0;
}
.choices[data-type*=select-multiple] .choices__button:hover, .choices[data-type*=select-multiple] .choices__button:focus, .choices[data-type*=text] .choices__button:hover, .choices[data-type*=text] .choices__button:focus {
  opacity: 1;
}
.choices[data-type*=select-multiple] .choices__list--dropdown, .choices[data-type*=text] .choices__list--dropdown {
  padding-bottom: 10px;
}
.choices[data-type*=select-multiple] .choices__list--dropdown .choices__list, .choices[data-type*=text] .choices__list--dropdown .choices__list {
  margin-bottom: 0;
}
.choices[data-type*=select-multiple] .choices__input {
  padding-top: 3px;
}

.input-light .choices__inner {
  background-color: var(--vz-light);
  border: none;
}

.choices__inner {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  background-color: var(--vz-input-bg-custom);
  padding: 0.25rem 3.6rem 0.1rem 0.5rem;
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  border-radius: var(--vz-border-radius) !important;
  font-size: var(--vz-font-base);
  min-height: 37.5px;
  overflow: hidden;
}

.is-focused .choices__inner,
.is-open .choices__inner {
  border-color: var(--vz-primary-border-subtle);
}

.choices__list {
  margin: 0;
  padding-left: 0;
  list-style: none;
}

.choices__list--single {
  display: inline-block;
  padding: 4px 16px 4px 4px;
  width: 100%;
}
.choices__list--single .choices__item {
  width: 100%;
}

.choices__list--multiple {
  display: inline;
}
.choices__list--multiple .choices__item {
  display: inline-block;
  vertical-align: initial;
  border-radius: 7px;
  padding: 2px 7px;
  font-size: 11px;
  font-weight: var(--vz-font-weight-normal);
  margin-right: 3.75px;
  margin-bottom: 3.75px;
  margin-top: 2px;
  background-color: var(--vz-primary);
  border: 1px solid var(--vz-primary);
  word-break: break-all;
  box-sizing: border-box;
  color: #fff;
  box-shadow: var(--vz-element-shadow);
}
.choices__list--multiple .choices__item[data-deletable] {
  padding-right: 5px;
}
.choices__list--multiple .choices__item.is-highlighted {
  background-color: var(--vz-primary);
  border: 1px solid var(--vz-primary);
}

.is-disabled .choices__list--multiple .choices__item {
  background-color: var(--vz-secondary);
  border: 1px solid var(--vz-secondary);
}

.choices__list--dropdown {
  visibility: hidden;
  z-index: 1;
  position: absolute;
  width: 100%;
  background-color: var(--vz-secondary-bg);
  border: 1px solid var(--vz-border-color);
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
  top: 100%;
  margin-top: 0;
  padding: 10px 10px 20px 10px;
  border-bottom-left-radius: 2.5px;
  border-bottom-right-radius: 2.5px;
  overflow: hidden;
  word-break: break-all;
  will-change: visibility;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  transform: translateY(1px);
}
.choices__list--dropdown.is-active {
  visibility: visible;
  animation-name: DropDownSlide;
}
.choices__list--dropdown .choices__item--selectable.is-highlighted {
  background-color: var(--vz-tertiary-bg);
}
.choices__list--dropdown .choices__list {
  margin: 0 -16px -16px;
}
.choices__list--dropdown .has-no-results {
  font-style: italic;
  font-weight: var(--vz-font-weight-medium);
}
.choices__list--dropdown .choices__item--selectable:after {
  display: none;
}

.is-open .choices__list--dropdown {
  border-color: var(--vz-border-color);
}

.is-flipped .choices__list--dropdown {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: -1px;
  border-radius: 0.25rem 0.25rem 0 0;
}
.is-flipped .choices__list--dropdown.is-active {
  animation-name: DropDownSlideDown;
  transform: translateY(-1px);
}

.choices__list--dropdown .choices__list {
  position: relative;
  max-height: 300px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}
.choices__list--dropdown .choices__item {
  position: relative;
  font-size: var(--vz-font-base);
  padding: 0.35rem 1.2rem 0.35rem 16px;
}

@media (min-width: 640px) {
  .choices__list--dropdown .choices__item--selectable:after {
    content: attr(data-select-text);
    font-size: 12px;
    opacity: 0;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
  }
  .choices__list--dropdown .choices__item--selectable.is-highlighted:after {
    opacity: 0.5;
  }
}
.choices__item {
  cursor: default;
}

.choices__item--selectable {
  cursor: pointer;
}

.choices__item--disabled {
  cursor: not-allowed;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.5;
}

.choices__heading {
  font-weight: var(--vz-font-weight-semibold);
  font-size: 12px;
  padding: 10px 16px;
  border-bottom: 1px solid var(--vz-border-color);
  color: var(--vz-secondary-color);
}

.choices__button {
  text-indent: -9999px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: 0;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position: center;
  cursor: pointer;
}
.choices__button:focus {
  outline: none;
}

.choices__input {
  display: inline-block;
  vertical-align: baseline;
  background-color: var(--vz-input-bg-custom);
  color: var(--vz-body-color);
  font-size: var(--vz-font-base);
  margin-bottom: 0;
  border: 0;
  border-radius: 0;
  max-width: 100%;
  padding: 2px 0 2px 2px;
}
.choices__input:focus {
  outline: 0;
}
.choices__input::-moz-placeholder {
  color: var(--vz-secondary-color);
}
.choices__input::placeholder {
  color: var(--vz-secondary-color);
}

.choices__placeholder {
  color: var(--vz-secondary-color);
  opacity: 1;
}

[data-bs-theme=dark] .choices[data-type*=select-one] .choices__button {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.flatpickr-calendar {
  background: transparent;
  opacity: 0;
  display: none;
  text-align: center;
  visibility: hidden;
  padding: 0;
  animation: none;
  direction: ltr;
  border: 0;
  font-size: 14px;
  line-height: 24px;
  border-radius: 5px;
  position: absolute;
  width: 307.875px;
  box-sizing: border-box;
  touch-action: manipulation;
  background: var(--vz-secondary-bg);
  box-shadow: 1px 0 0 var(--vz-border-color), -1px 0 0 var(--vz-border-color), 0 1px 0 var(--vz-border-color), 0 -1px 0 var(--vz-border-color), 0 3px 13px rgba(0, 0, 0, 0.08);
}
.flatpickr-calendar.open, .flatpickr-calendar.inline {
  opacity: 1;
  max-height: 640px;
  visibility: visible;
}
.flatpickr-calendar.open {
  display: inline-block;
  z-index: 1056;
}
.flatpickr-calendar.animate.open {
  animation: fpFadeInDown 300ms cubic-bezier(0.23, 1, 0.32, 1);
}
.flatpickr-calendar.inline {
  display: block;
  position: relative;
  top: 2px;
  width: 100%;
  box-shadow: none;
}
.flatpickr-calendar.inline .flatpickr-rContainer {
  display: block;
  width: 100%;
}
.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days {
  width: 100%;
  border: var(--vz-border-width) solid var(--vz-input-border-custom);
  border-top: none;
  border-radius: 0 0 5px 5px;
}
.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days .dayContainer {
  width: 100%;
  min-width: 100%;
  max-width: 100%;
}
.flatpickr-calendar.inline .flatpickr-rContainer .flatpickr-days .dayContainer .flatpickr-day {
  max-width: 100%;
  border-radius: 4px;
}
.flatpickr-calendar.inline .flatpickr-time {
  border: var(--vz-border-width) solid var(--vz-input-border-custom) !important;
  border-radius: var(--vz-border-radius);
}
.flatpickr-calendar.static {
  position: absolute;
  top: calc(100% + 2px);
}
.flatpickr-calendar.static.open {
  z-index: 999;
  display: block;
}
.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
  box-shadow: none !important;
}
.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
  box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
}
.flatpickr-calendar .hasWeeks .dayContainer,
.flatpickr-calendar .hasTime .dayContainer {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.flatpickr-calendar .hasWeeks .dayContainer {
  border-left: 0;
}
.flatpickr-calendar.hasTime .flatpickr-time {
  height: 40px;
  border-top: 1px solid var(--vz-border-color);
}
.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
  height: auto;
}
.flatpickr-calendar::before, .flatpickr-calendar::after {
  position: absolute;
  display: block;
  pointer-events: none;
  border: solid transparent;
  content: "";
  height: 0;
  width: 0;
  left: 22px;
}
.flatpickr-calendar.rightMost::before, .flatpickr-calendar.arrowRight::before, .flatpickr-calendar.rightMost::after, .flatpickr-calendar.arrowRight::after {
  left: auto;
  right: 22px;
}
.flatpickr-calendar.arrowCenter::before, .flatpickr-calendar.arrowCenter::after {
  left: 50%;
  right: 50%;
}
.flatpickr-calendar::before {
  border-width: 5px;
  margin: 0 -5px;
}
.flatpickr-calendar::after {
  border-width: 4px;
  margin: 0 -4px;
}
.flatpickr-calendar.arrowTop::before, .flatpickr-calendar.arrowTop::after {
  bottom: 100%;
}
.flatpickr-calendar.arrowTop::before {
  border-bottom-color: var(--vz-primary);
}
.flatpickr-calendar.arrowTop::after {
  border-bottom-color: var(--vz-primary);
}
.flatpickr-calendar.arrowBottom::before, .flatpickr-calendar.arrowBottom::after {
  top: 100%;
}
.flatpickr-calendar.arrowBottom::before {
  border-top-color: var(--vz-primary);
}
.flatpickr-calendar.arrowBottom::after {
  border-top-color: var(--vz-primary);
}
.flatpickr-calendar:focus {
  outline: 0;
}

.flatpickr-wrapper {
  position: relative;
  display: inline-block;
}

.flatpickr-months {
  display: flex;
  background-color: var(--vz-primary);
  border-radius: 5px 5px 0px 0px;
}
.flatpickr-months .flatpickr-month {
  background: transparent;
  color: rgba(255, 255, 255, 0.9);
  fill: rgba(255, 255, 255, 0.9);
  height: 34px;
  line-height: 1;
  text-align: center;
  position: relative;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: hidden;
  flex: 1;
}
.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
  text-decoration: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 34px;
  padding: 10px;
  z-index: 3;
  color: rgba(255, 255, 255, 0.9);
  fill: rgba(255, 255, 255, 0.9);
  /*
  /*rtl:begin:ignore*/
  /*
  /*rtl:end:ignore*/
  /*
  /*rtl:begin:ignore*/
  /*
  /*rtl:end:ignore*/
}
.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,
.flatpickr-months .flatpickr-next-month.flatpickr-disabled {
  display: none;
}
.flatpickr-months .flatpickr-prev-month i,
.flatpickr-months .flatpickr-next-month i {
  position: relative;
}
.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
  /*
        /*rtl:begin:ignore*/
  /*
        */
  left: 0;
  /*
        /*rtl:end:ignore*/
  /*
        */
}
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
  /*
        /*rtl:begin:ignore*/
  /*
        */
  right: 0;
  /*
        /*rtl:end:ignore*/
  /*
        */
}
.flatpickr-months .flatpickr-prev-month:hover,
.flatpickr-months .flatpickr-next-month:hover {
  color: #959ea9;
}
.flatpickr-months .flatpickr-prev-month:hover svg,
.flatpickr-months .flatpickr-next-month:hover svg {
  fill: rgba(255, 255, 255, 0.9);
}
.flatpickr-months .flatpickr-prev-month svg,
.flatpickr-months .flatpickr-next-month svg {
  width: 14px;
  height: 14px;
}
.flatpickr-months .flatpickr-prev-month svg path,
.flatpickr-months .flatpickr-next-month svg path {
  transition: fill 0.1s;
  fill: inherit;
}

.numInputWrapper {
  position: relative;
  height: auto;
}
.numInputWrapper input,
.numInputWrapper span {
  display: inline-block;
}
.numInputWrapper input {
  width: 100%;
}
.numInputWrapper input::-ms-clear {
  display: none;
}
.numInputWrapper input::-webkit-outer-spin-button, .numInputWrapper input::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
.numInputWrapper span {
  position: absolute;
  right: 0;
  width: 14px;
  padding: 0 4px 0 2px;
  height: 50%;
  line-height: 50%;
  opacity: 0;
  cursor: pointer;
  border: 1px solid rgba(var(--vz-dark-rgb), 0.15);
  box-sizing: border-box;
}
.numInputWrapper span:hover {
  background: rgba(0, 0, 0, 0.1);
}
.numInputWrapper span:active {
  background: rgba(0, 0, 0, 0.2);
}
.numInputWrapper span:after {
  display: block;
  content: "";
  position: absolute;
}
.numInputWrapper span.arrowUp {
  top: 0;
  border-bottom: 0;
}
.numInputWrapper span.arrowUp:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgba(57, 57, 57, 0.6);
  top: 26%;
}
.numInputWrapper span.arrowDown {
  top: 50%;
}
.numInputWrapper span.arrowDown:after {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgba(57, 57, 57, 0.6);
  top: 40%;
}
.numInputWrapper span svg {
  width: inherit;
  height: auto;
}
.numInputWrapper span svg path {
  fill: rgba(0, 0, 0, 0.5);
}
.numInputWrapper:hover {
  background: rgba(0, 0, 0, 0.05);
}
.numInputWrapper:hover span {
  opacity: 1;
}

.flatpickr-current-month {
  font-size: 100%;
  line-height: inherit;
  font-weight: 300;
  color: inherit;
  position: absolute;
  width: 75%;
  left: 12.5%;
  padding: 7.48px 0 0 0;
  line-height: 1;
  height: 34px;
  display: inline-block;
  text-align: center;
  transform: translate3d(0px, 0px, 0px);
}
.flatpickr-current-month span.cur-month {
  font-family: inherit;
  font-weight: 700;
  color: inherit;
  display: inline-block;
  margin-left: 0.5ch;
  padding: 0;
}
.flatpickr-current-month span.cur-month:hover {
  background: rgba(0, 0, 0, 0.05);
}
.flatpickr-current-month .numInputWrapper {
  width: 6ch;
  width: 7ch\0 ;
  display: inline-block;
}
.flatpickr-current-month span.arrowUp::after {
  border-bottom-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month span.arrowDown:after {
  border-top-color: rgba(0, 0, 0, 0.9);
}
.flatpickr-current-month input.cur-year {
  background: transparent;
  box-sizing: border-box;
  color: inherit;
  cursor: text;
  padding: 0 0 0 0.5ch;
  margin: 0;
  display: inline-block;
  font-size: inherit;
  font-family: inherit;
  font-weight: var(--vz-font-weight-semibold);
  line-height: inherit;
  height: auto;
  border: 0;
  border-radius: 0;
  vertical-align: initial;
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}
.flatpickr-current-month input.cur-year:focus {
  outline: 0;
}
.flatpickr-current-month input.cur-year[disabled], .flatpickr-current-month input.cur-year[disabled]:hover {
  font-size: 100%;
  color: rgba(255, 255, 255, 0.9);
  background: transparent;
  pointer-events: none;
}
.flatpickr-current-month .flatpickr-monthDropdown-months {
  -webkit-appearance: menulist;
     -moz-appearance: menulist;
          appearance: menulist;
  background: transparent;
  border: none;
  border-radius: 0;
  box-sizing: border-box;
  color: inherit;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
  font-weight: var(--vz-font-weight-semibold);
  height: auto;
  line-height: inherit;
  margin: -1px 0 0 0;
  outline: none;
  padding: 0 0 0 0.5ch;
  position: relative;
  vertical-align: initial;
  width: auto;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:focus, .flatpickr-current-month .flatpickr-monthDropdown-months:active {
  outline: none;
}
.flatpickr-current-month .flatpickr-monthDropdown-months:hover {
  background-color: transparent;
}
.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  background-color: transparent;
  outline: none;
  padding: 0;
  color: rgba(0, 0, 0, 0.8);
}

.flatpickr-weekdays {
  background-color: var(--vz-primary);
  text-align: center;
  overflow: hidden;
  width: 100%;
  display: flex;
  align-items: center;
  height: 36px;
  border-bottom: 1px solid var(--vz-border-color);
}
.flatpickr-weekdays .flatpickr-weekdaycontainer {
  display: flex;
  flex: 1;
}

span.flatpickr-weekday {
  cursor: default;
  font-size: 90%;
  background: var(--vz-primary);
  color: #fff;
  line-height: 1;
  margin: 0;
  text-align: center;
  display: block;
  flex: 1;
  font-weight: var(--vz-font-weight-medium);
}

.dayContainer,
.flatpickr-weeks {
  padding: 1px 0 0 0;
}

.flatpickr-days {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: flex-start;
  width: 307.875px;
}
.flatpickr-days:focus {
  outline: 0;
}

.dayContainer {
  padding: 0;
  outline: 0;
  text-align: left;
  width: 307.875px;
  min-width: 307.875px;
  max-width: 307.875px;
  box-sizing: border-box;
  display: inline-block;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  transform: translate3d(0px, 0px, 0px);
  opacity: 1;
}
.dayContainer + .dayContainer {
  box-shadow: -1px 0 0 #e6e6e6;
}

.flatpickr-day {
  background: none;
  border: 1px solid transparent;
  border-radius: 150px;
  box-sizing: border-box;
  color: var(--vz-body-color);
  cursor: pointer;
  font-weight: 400;
  width: 14.2857143%;
  flex-basis: 14.2857143%;
  max-width: 39px;
  height: 39px;
  line-height: 39px;
  margin: 0;
  display: inline-block;
  position: relative;
  justify-content: center;
  text-align: center;
}
.flatpickr-day:hover, .flatpickr-day:focus {
  background-color: rgba(var(--vz-light-rgb), 0.7);
}
.flatpickr-day.inRange, .flatpickr-day.prevMonthDay.inRange, .flatpickr-day.nextMonthDay.inRange, .flatpickr-day.today.inRange, .flatpickr-day.prevMonthDay.today.inRange, .flatpickr-day.nextMonthDay.today.inRange, .flatpickr-day:hover, .flatpickr-day.prevMonthDay:hover, .flatpickr-day.nextMonthDay:hover, .flatpickr-day:focus, .flatpickr-day.prevMonthDay:focus, .flatpickr-day.nextMonthDay:focus {
  cursor: pointer;
  outline: 0;
  background-color: var(--vz-light);
  border-color: var(--vz-light);
}
.flatpickr-day.today {
  border-color: var(--vz-primary);
  background-color: rgba(var(--vz-primary-rgb), 0.1);
  box-shadow: var(--vz-element-shadow);
}
.flatpickr-day.today:hover, .flatpickr-day.today:focus {
  border-color: var(--vz-primary);
  background-color: rgba(var(--vz-primary-rgb), 0.15);
  color: var(--vz-body-color);
}
.flatpickr-day.selected, .flatpickr-day.startRange, .flatpickr-day.endRange, .flatpickr-day.selected.inRange, .flatpickr-day.startRange.inRange, .flatpickr-day.endRange.inRange, .flatpickr-day.selected:focus, .flatpickr-day.startRange:focus, .flatpickr-day.endRange:focus, .flatpickr-day.selected:hover, .flatpickr-day.startRange:hover, .flatpickr-day.endRange:hover, .flatpickr-day.selected.prevMonthDay, .flatpickr-day.startRange.prevMonthDay, .flatpickr-day.endRange.prevMonthDay, .flatpickr-day.selected.nextMonthDay, .flatpickr-day.startRange.nextMonthDay, .flatpickr-day.endRange.nextMonthDay {
  background: var(--vz-primary);
  box-shadow: var(--vz-element-shadow);
  color: #fff;
  border-color: var(--vz-primary);
}
.flatpickr-day.selected.startRange, .flatpickr-day.startRange.startRange, .flatpickr-day.endRange.startRange {
  border-radius: 50px 0 0 50px;
}
.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)), .flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  box-shadow: -10px 0 0 var(--vz-primary);
}
.flatpickr-day.selected.startRange .endRange, .flatpickr-day.startRange.startRange .endRange, .flatpickr-day.endRange.startRange .endRange {
  border-radius: 50px;
}
.flatpickr-day.selected.endRange, .flatpickr-day.startRange.endRange, .flatpickr-day.endRange.endRange {
  border-radius: 0 50px 50px 0;
}
.flatpickr-day.inRange {
  border-radius: 0;
  box-shadow: -5px 0 0 var(--vz-light), 5px 0 0 var(--vz-light);
}
.flatpickr-day.flatpickr-disabled, .flatpickr-day.flatpickr-disabled:hover, .flatpickr-day.prevMonthDay, .flatpickr-day.nextMonthDay, .flatpickr-day.notAllowed, .flatpickr-day.notAllowed.prevMonthDay, .flatpickr-day.notAllowed.nextMonthDay {
  color: rgba(var(--vz-body-color-rgb), 0.3);
  background: transparent;
  border-color: transparent;
  cursor: default;
}
.flatpickr-day.flatpickr-disabled, .flatpickr-day.flatpickr-disabled:hover {
  cursor: not-allowed;
  color: rgba(var(--vz-body-color-rgb), 0.3);
}
.flatpickr-day.week.selected {
  border-radius: 0;
  box-shadow: -5px 0 0 var(--vz-primary), 5px 0 0 var(--vz-primary);
}
.flatpickr-day.hidden {
  visibility: hidden;
}

.rangeMode .flatpickr-day {
  margin-top: 1px;
}

.flatpickr-weekwrapper {
  float: left;
}
.flatpickr-weekwrapper .flatpickr-weeks {
  padding: 0 12px;
  box-shadow: 1px 0 0 var(--vz-border-color);
}
.flatpickr-weekwrapper .flatpickr-weekday {
  float: none;
  width: 100%;
  line-height: 35px;
}
.flatpickr-weekwrapper span.flatpickr-day, .flatpickr-weekwrapper span.flatpickr-day:hover {
  display: block;
  width: 100%;
  max-width: none;
  color: rgba(var(--vz-body-rgb), 0.3);
  background: transparent;
  cursor: default;
  border: none;
}

.flatpickr-innerContainer {
  display: block;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
}

.flatpickr-rContainer {
  display: inline-block;
  padding: 0;
  box-sizing: border-box;
}

.flatpickr-time {
  text-align: center;
  outline: 0;
  display: block;
  height: 0;
  line-height: 40px;
  max-height: 40px;
  box-sizing: border-box;
  overflow: hidden;
  display: flex;
}
.flatpickr-time:after {
  content: "";
  display: table;
  clear: both;
}
.flatpickr-time .numInputWrapper {
  flex: 1;
  width: 40%;
  height: 40px;
  float: left;
}
.flatpickr-time .numInputWrapper span.arrowUp:after, .flatpickr-time .numInputWrapper span.arrowDown:after {
  border-bottom-color: var(--vz-border-color);
}
.flatpickr-time.hasSeconds .numInputWrapper {
  width: 26%;
}
.flatpickr-time.time24hr .numInputWrapper {
  width: 49%;
}
.flatpickr-time input {
  background: transparent;
  box-shadow: none;
  border: 0;
  border-radius: 0;
  text-align: center;
  margin: 0;
  padding: 0;
  height: inherit;
  line-height: inherit;
  color: var(--vz-body-color);
  font-size: 14px;
  position: relative;
  box-sizing: border-box;
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}
.flatpickr-time input.flatpickr-hour {
  font-weight: bold;
}
.flatpickr-time input.flatpickr-minute, .flatpickr-time input.flatpickr-second {
  font-weight: 400;
}
.flatpickr-time input:focus {
  outline: 0;
  border: 0;
}
.flatpickr-time .flatpickr-time-separator,
.flatpickr-time .flatpickr-am-pm {
  height: inherit;
  float: left;
  line-height: inherit;
  color: var(--vz-body-color);
  font-weight: bold;
  width: 2%;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  align-self: center;
}
.flatpickr-time .flatpickr-am-pm {
  outline: 0;
  width: 18%;
  cursor: pointer;
  text-align: center;
  font-weight: 400;
}
.flatpickr-time input:hover, .flatpickr-time input:focus, .flatpickr-time .flatpickr-am-pm:hover, .flatpickr-time .flatpickr-am-pm:focus {
  background: rgba(var(--vz-primary-rgb), 0.04);
}

.flatpickr-am-pm:focus, .flatpickr-am-pm:hover,
.numInput:focus,
.numInput:hover,
.numInputWrapper:focus,
.numInputWrapper:hover {
  background-color: transparent;
}

.flatpickr-input[readonly] {
  cursor: pointer;
  background-color: var(--vz-input-bg-custom);
}

[data-inline-date=true], [data-time-inline] {
  display: none;
}

@keyframes fpFadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
[datepicker-inline],
[timepicker-inline] {
  display: none;
}

[data-input-flag] {
  position: relative;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list {
  max-height: 158px;
  overflow-y: auto;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list::-webkit-scrollbar {
  -webkit-appearance: none;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list::-webkit-scrollbar:vertical {
  width: 8px;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list::-webkit-scrollbar:horizontal {
  height: 10px;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-dark-rgb), 0.075);
  border-radius: 8px;
}
[data-input-flag] .dropdown-menu .dropdown-menu-list::-webkit-scrollbar-track {
  border-radius: 8px;
}

[data-option-flag-img-name] .flag-input {
  padding-left: 45px;
  background-image: url("../images/flags/us.svg");
  background-repeat: no-repeat;
  background-position: 14px 8px;
  background-size: 18px;
}

[data-option-flag-name]::before,
[data-option-flag-img-name]::before {
  content: "\f0140";
  font-family: "Material Design Icons";
  position: absolute;
  right: 0.9rem;
  top: 0.5rem;
  font-size: 16px;
}

[data-option-countrycode=false] .country-codeno {
  display: none;
}

[data-search-input=false] .searchlist-input {
  display: none;
}

.auth-page-wrapper .auth-page-content {
  padding-bottom: 60px;
  position: relative;
  z-index: 2;
  width: 100%;
}
.auth-page-wrapper .footer {
  left: 0;
  background-color: transparent;
  color: var(--vz-body-color);
}

.auth-one-bg-position {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 380px;
}
@media (max-width: 575.98px) {
  .auth-one-bg-position {
    height: 280px;
  }
}

.auth-one-bg {
  background-image: url("../images/auth-one-bg.jpg");
  background-position: center;
  background-size: cover;
}
.auth-one-bg .bg-overlay {
  background: linear-gradient(to right, var(--vz-primary-text-emphasis), var(--vz-primary));
  opacity: 0.9;
}

.shape {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 1;
  pointer-events: none;
}
.shape > svg {
  width: 100%;
  height: auto;
  fill: var(--vz-body-bg);
}

.auth-pass-inputgroup input[type=text] + .btn .ri-eye-fill:before {
  content: "\ecb6";
}

.particles-js-canvas-el {
  position: relative;
}

.signin-other-title {
  position: relative;
}
.signin-other-title:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  right: 0;
  border-top: 1px dashed var(--vz-border-color);
  top: 10px;
}
.signin-other-title .title {
  display: inline-block;
  position: relative;
  z-index: 9;
  background-color: var(--vz-secondary-bg);
  padding: 2px 16px;
}

.auth-bg-cover {
  background: linear-gradient(-45deg, var(--vz-primary) 50%, var(--vz-success));
}
.auth-bg-cover > .bg-overlay {
  background-image: url("../images/cover-pattern.png");
  background-position: center;
  background-size: cover;
  opacity: 1;
  background-color: transparent;
}
.auth-bg-cover .footer {
  color: rgba(255, 255, 255, 0.5);
}

#password-contain {
  display: none;
}
#password-contain p {
  padding-left: 13px;
}
#password-contain p.valid {
  color: var(--vz-success);
}
#password-contain p.valid::before {
  position: relative;
  left: -8px;
  content: "✔";
}
#password-contain p.invalid {
  color: var(--vz-danger);
}
#password-contain p.invalid::before {
  position: relative;
  left: -8px;
  content: "✖";
}

.dash-filter-picker {
  min-width: 210px !important;
}

.upcoming-scheduled {
  position: relative;
}
@media (max-width: 575.98px) {
  .upcoming-scheduled {
    top: 35px !important;
  }
}
.upcoming-scheduled .flatpickr-months {
  position: absolute !important;
  top: -45px !important;
  left: auto !important;
  right: 0px !important;
  width: 200px;
  background-color: transparent;
}
.upcoming-scheduled .flatpickr-months .flatpickr-month {
  color: #878a99 !important;
  fill: #878a99 !important;
}
.upcoming-scheduled .flatpickr-months .flatpickr-prev-month, .upcoming-scheduled .flatpickr-months .flatpickr-next-month {
  display: none;
}
.upcoming-scheduled .flatpickr-calendar {
  box-shadow: none !important;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-current-month {
  font-size: 13px;
  width: 100%;
  left: 0;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-monthDropdown-months {
  border: 1px solid var(--vz-border-color);
  border-radius: 4px;
  height: 26px;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-weekdays {
  background-color: var(--vz-light);
  border: none;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-weekdays span.flatpickr-weekday {
  color: var(--vz-body-color);
  background-color: var(--vz-light);
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-day.today {
  color: #fff !important;
  background-color: var(--vz-success);
  border-color: var(--vz-success) !important;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-day.today:hover {
  color: var(--vz-success) !important;
  background-color: rgba(var(--vz-success-rgb), 0.2) !important;
}
.upcoming-scheduled .flatpickr-calendar .flatpickr-day.selected {
  background-color: var(--vz-success) !important;
  border-color: var(--vz-success) !important;
  color: #fff;
}
.upcoming-scheduled .flatpickr-calendar .numInputWrapper {
  width: 7.5ch;
  margin-left: 10px;
}
.upcoming-scheduled .flatpickr-days {
  border: none !important;
}

.crm-widget .col {
  border-right: 1px solid var(--vz-border-color);
}
.crm-widget .col:last-child {
  border: 0px;
}
@media (min-width: 768px) and (max-width: 1399.98px) {
  .crm-widget .col:nth-child(3) {
    border-right: 0px;
  }
  .crm-widget .col:last-child {
    border-right: 1px solid var(--vz-border-color);
  }
}
@media (max-width: 767.98px) {
  .crm-widget .col {
    border-right: 0px;
    border-bottom: 1px solid var(--vz-border-color);
  }
}

@media (min-width: 1400px) and (max-width: 1599.98px) {
  .project-wrapper > .col-xxl-8, .project-wrapper .col-xxl-4 {
    width: 100%;
  }
}
.crypto-widget {
  max-width: 130px !important;
}

.bg-marketplace {
  background-image: url(../images/nft/marketplace.png);
  background-size: cover;
}

.dash-countdown .countdownlist .count-num {
  background-color: var(--vz-secondary-bg);
  padding: 16px 8px;
  font-size: 22px;
}
@media (max-width: 575.98px) {
  .dash-countdown .countdownlist .count-num {
    font-size: 16px;
    padding: 8px 6px;
  }
}
@media (max-width: 575.98px) {
  .dash-countdown .countdownlist .count-title {
    font-size: 10px;
  }
}

[data-layout-width=boxed] .dash-countdown .countdownlist {
  flex-wrap: wrap;
}

.marketplace-icon {
  position: absolute;
  float: right;
  top: 30px;
  left: 30px;
}

.marketplace-swiper .swiper-button-next,
.marketplace-swiper .swiper-button-prev {
  top: 34px;
  width: 28px;
  height: 28px;
  background-color: rgba(var(--vz-primary-rgb), 0.1);
  color: var(--vz-primary);
  border-radius: 0.3rem;
  right: 16px !important;
}
.marketplace-swiper .swiper-button-prev {
  right: 58px !important;
  left: auto !important;
}

.dash-collection .content {
  background-color: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
}

@media (max-width: 1441.98px) {
  .dash-nft .col-xxl-9, .dash-nft .col-xxl-3 {
    width: 100% !important;
    max-width: 100% !important;
  }
}

.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
}

#portfolio_donut_charts .apexcharts-pie text {
  fill: var(--vz-body-color);
}

.timeline {
  position: relative;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.timeline-item {
  padding: 30px 60px;
  position: relative;
  background: inherit;
  width: 50%;
}
.timeline-item::after {
  content: "";
  position: absolute;
  width: 2px;
  background: var(--vz-timeline-color);
  top: 38px;
  bottom: -38px;
  right: 0;
  margin-left: -1.5px;
}
.timeline-item:last-child::after {
  display: none;
}
.timeline-item .icon {
  position: absolute;
  display: inline-flex;
  width: 60px;
  height: 60px;
  align-items: center;
  justify-content: center;
  font-size: 25px;
  top: 30px;
  right: -30px;
  padding: 9px 0;
  background: var(--vz-secondary-bg);
  border: 1px solid var(--vz-border-color);
  border-radius: 50px;
  color: var(--vz-success);
  z-index: 1;
}
.timeline-item .date {
  position: absolute;
  display: inline-block;
  width: calc(100% - 48px);
  top: 50px;
  font-size: 14px;
  font-weight: 500;
  font-style: italic;
}
.timeline-item .content {
  padding: 20px;
  background: var(--vz-secondary-bg);
  position: relative;
  border: 1px solid var(--vz-border-color);
  border-radius: 0.25rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
.timeline-item.left {
  left: 0;
}
.timeline-item.left .date {
  left: calc(100% + 48px);
  text-align: start;
}
.timeline-item.right {
  left: 50%;
}
.timeline-item.right::after {
  left: 0;
  margin-right: -1.5px;
}
.timeline-item.right .icon {
  left: -30px;
}
.timeline-item.right .date {
  right: calc(100% + 48px);
  text-align: end;
}
.timeline-item.right::before {
  left: 28px;
  border-color: transparent transparent transparent #fff;
}

@media (max-width: 991.98px) {
  .timeline::after {
    left: 24px;
    bottom: 180px;
  }
  .timeline-item {
    width: 100%;
    padding-left: 48px;
    padding-right: 0px;
  }
  .timeline-item.right, .timeline-item.left {
    left: 0%;
  }
  .timeline-item.right .icon, .timeline-item.left .icon {
    width: 45px;
    height: 45px;
    top: 0;
    font-size: 18px;
    left: 0;
  }
  .timeline-item.right::before, .timeline-item.left::before {
    left: 110px;
    border-color: transparent transparent transparent var(--vz-border-color);
  }
  .timeline-item.right .date, .timeline-item.left .date {
    right: auto;
    left: 48px;
    width: 79px;
    top: 8px;
    text-align: left;
  }
}
.timeline-2 {
  position: relative;
}
.timeline-2::after {
  position: absolute;
  content: "";
  width: 2px;
  height: 83%;
  top: 50px;
  left: 40px;
  margin-left: -1px;
  background: var(--vz-timeline-color);
}
.timeline-2 .timeline-year {
  position: relative;
  width: 100%;
  text-align: left;
  z-index: 1;
}
.timeline-2 .timeline-year p {
  display: inline-flex;
  width: 80px;
  height: 80px;
  margin: 0;
  padding: 23px 10px;
  background: var(--vz-timeline-color);
  border-radius: 50px;
  text-transform: uppercase;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.timeline-2 .timeline-year p span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
.timeline-2 .timeline-date {
  font-size: 14px;
  font-weight: var(--vz-font-weight-medium);
  margin: 24px 0 0 0;
  margin-left: 55px;
}
.timeline-2 .timeline-date::after {
  content: "";
  display: block;
  position: absolute;
  width: 14px;
  height: 14px;
  top: 26px;
  left: 45px;
  align-items: left;
  background: var(--vz-success);
  border: 3px solid var(--vz-timeline-color);
  border-radius: 50px;
  z-index: 1;
}
.timeline-2 .timeline-box {
  position: relative;
  display: inline-block;
  margin: 23px 62px;
  padding: 20px;
  border: 1px solid var(--vz-border-color);
  border-radius: 6px;
  background: var(--vz-secondary-bg);
  max-width: 695px;
}
@media (max-width: 991.98px) {
  .timeline-2 .timeline-box {
    margin-right: 0;
  }
}
.timeline-2 .timeline-box::after {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  top: 26px;
  right: 100%;
  border-color: transparent var(--vz-secondary-bg) transparent transparent;
  border-width: 10px;
}
.timeline-2 .timeline-box::before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  right: 100%;
  top: 24px;
  border-color: transparent var(--vz-border-color) transparent transparent;
  border-width: 12px;
}
.timeline-2 .timeline-box .timeline-text {
  position: relative;
  float: left;
}
.timeline-2 .timeline-launch {
  position: relative;
  display: inline-block;
  border: 1px solid var(--vz-border-color);
  border-radius: 6px;
  background: #fff;
  width: 100%;
  margin-top: 15px;
  padding: 0;
  border: none;
  text-align: left;
  background: transparent;
}
.timeline-2 .timeline-launch .timeline-box {
  margin-left: 0;
}
.timeline-2 .timeline-launch .timeline-box::after {
  left: 30px;
  margin-left: 0px;
  top: -20px;
  border-color: transparent transparent var(--vz-border-color) transparent;
}
.timeline-2 .timeline-launch .timeline-box::before {
  left: 30px;
  margin-left: 0px;
  top: -19px;
  border-color: transparent transparent var(--vz-secondary-bg) transparent;
  border-width: 10px;
  z-index: 1;
}

.horizontal-timeline {
  position: relative;
  width: 100%;
  margin: 0 auto;
}
.horizontal-timeline::before {
  content: "";
  position: absolute;
  width: 100%;
  top: 174px;
  left: 0;
  height: 2px;
  background-color: var(--vz-timeline-color);
}
.horizontal-timeline .swiper-slide .item-box {
  margin: 227px 0px 0px;
  background-color: transparent;
  box-shadow: none;
}
.horizontal-timeline .swiper-slide .item-box::after {
  content: "";
  position: absolute;
  left: 0px;
  right: 0px;
  margin: 0px auto;
  background: var(--vz-primary);
  width: 13px;
  height: 13px;
  top: -59px;
  border-radius: 50px;
  border: 3px solid var(--vz-timeline-color);
}
.horizontal-timeline .swiper-slide .timeline-content {
  min-height: 110px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: var(--vz-timeline-color);
}
.horizontal-timeline .swiper-slide .timeline-content::before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  left: 0px;
  top: -23px;
  border-color: transparent transparent var(--vz-timeline-color) transparent;
  border-width: 12px;
  right: 0;
  margin: 0 auto;
}
.horizontal-timeline .swiper-slide .time {
  position: absolute;
  top: -86px;
  right: 0px;
  left: 0px;
  margin: 0px auto;
}
.horizontal-timeline .swiper-slide:nth-child(even) {
  margin-top: 5px;
  transform: rotate(-180deg);
}
.horizontal-timeline .swiper-slide:nth-child(even) .timeline-content {
  transform: rotate(180deg);
}
.horizontal-timeline .swiper-slide:nth-child(even) .timeline-content::before {
  bottom: -23px;
  top: auto;
  border-color: var(--vz-timeline-color) transparent transparent transparent;
}
.horizontal-timeline .swiper-slide:nth-child(even) .time {
  transform: rotate(180deg);
}
.horizontal-timeline .swiper-button-next,
.horizontal-timeline .swiper-button-prev {
  height: 40px;
  width: 40px;
  line-height: 40px;
  border-radius: 50%;
  background-color: var(--vz-primary);
}
.horizontal-timeline .swiper-button-next::after,
.horizontal-timeline .swiper-button-prev::after {
  font-size: 24px;
  color: #fff;
}
.horizontal-timeline .swiper-button-next.swiper-button-disabled,
.horizontal-timeline .swiper-button-prev.swiper-button-disabled {
  background-color: rgba(var(--vz-primary-rgb), 0.5);
  opacity: 1;
  cursor: auto;
  -webkit-backdrop-filter: blur(25px);
          backdrop-filter: blur(25px);
  pointer-events: none;
}
.horizontal-timeline .swiper-button-next {
  right: 0;
}
.horizontal-timeline .swiper-button-next::after {
  content: "\ea6e";
  font-family: remixicon;
}
.horizontal-timeline .swiper-button-prev {
  left: 0;
}
.horizontal-timeline .swiper-button-prev::after {
  content: "\ea64";
  font-family: remixicon;
}

.acitivity-timeline {
  position: relative;
  overflow: hidden;
}
.acitivity-timeline .acitivity-item {
  position: relative;
}
.acitivity-timeline .acitivity-item .flex-shrink-0 {
  z-index: 2;
}
.acitivity-timeline .acitivity-item .acitivity-avatar {
  background-color: var(--vz-secondary-bg);
  border: 3px solid var(--vz-secondary-bg);
  height: 32px;
  width: 32px;
}
.acitivity-timeline .acitivity-item:before {
  content: "";
  position: absolute;
  border-left: 1px dashed var(--vz-border-color);
  left: 16px;
  height: 100%;
  top: 5px;
  z-index: 0;
}
.acitivity-timeline .acitivity-item:last-child::before {
  border-color: transparent;
}

.categories-filter .list-inline-item {
  position: relative;
  margin-right: 0;
}
.categories-filter .list-inline-item a {
  display: block;
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-semibold);
  padding: 8px 15px;
  margin: 5px;
  cursor: pointer;
}

.gallery-box {
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  margin-bottom: 10px;
  box-shadow: none;
  background-color: transparent;
  padding: 8px;
}
.gallery-box::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  width: 100%;
  height: 0;
  background-color: var(--vz-secondary-bg);
  transition: all 0.4s;
}
.gallery-box .gallery-container {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}
.gallery-box .gallery-container a {
  display: block;
}
.gallery-box .gallery-container .gallery-overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  bottom: 0px;
  right: 0px;
  opacity: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 10%, rgba(0, 0, 0, 0) 60%, rgba(0, 0, 0, 0.5) 100%);
  visibility: hidden;
  overflow: hidden;
  transition: all 0.4s ease-in-out 0s;
  display: flex;
  width: 100%;
  align-items: flex-end;
  padding: 16px;
}
.gallery-box .gallery-container .gallery-overlay .overlay-caption {
  color: #fff;
  margin: 0;
  font-size: 16px;
}
.gallery-box .gallery-img {
  transition: all 0.2s ease-in-out;
}
.gallery-box .box-content {
  position: relative;
}
.gallery-box .box-content .title {
  font-size: 14px;
  margin-bottom: 4px;
  display: none;
}
.gallery-box .box-content .post {
  margin: 0;
  transition: all 0.2s;
  color: var(--vz-secondary-color);
}
.gallery-box:hover::before {
  height: 100%;
}
.gallery-box:hover .box-content {
  bottom: 0;
}
.gallery-box:hover .box-content .post {
  opacity: 1;
}
.gallery-box:hover .gallery-overlay {
  opacity: 1;
  visibility: visible;
}

.gallery-light .gallery-box::before {
  background-color: var(--vz-light);
}

.error-basic-img {
  max-width: 450px;
}

.error-500 .title {
  font-size: 250px;
}
.error-500 .error-500-img {
  position: absolute;
  top: 57px;
  left: 0;
  right: 0;
  margin: 0 auto;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .error-500 .title {
    font-size: 150px;
  }
  .error-500 .error-500-img {
    width: 20% !important;
    top: 43px;
  }
}
@media (max-width: 767.98px) {
  .error-500 .title {
    font-size: 68px;
    margin-top: 35px;
  }
  .error-500 .error-500-img {
    position: relative;
    top: 0px;
  }
}

@keyframes errorAnimation {
  0% {
    transform: translateX(0%);
  }
  15% {
    transform: translateX(-25%) rotate(-5deg);
  }
  30% {
    transform: translateX(20%) rotate(3deg);
  }
  45% {
    transform: translateX(-15%) rotate(-3deg);
  }
  60% {
    transform: translateX(10%) rotate(2deg);
  }
  75% {
    transform: translateX(-5%) rotate(-1deg);
  }
  100% {
    transform: translateX(0%);
  }
}
.error-img {
  animation: errorAnimation 20s infinite;
}

.error-text {
  text-shadow: 4px 4px rgba(var(--vz-success-rgb), 0.4);
}
@media (min-width: 1200px) {
  .error-text {
    font-size: 10rem;
  }
}

.profile-wid-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  height: 320px;
}
@media (max-width: 575.98px) {
  .profile-wid-bg {
    height: 445px;
  }
}
.profile-wid-bg::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  opacity: 0.9;
  background: var(--vz-primary);
  background: linear-gradient(to top, var(--vz-primary-text-emphasis), var(--vz-primary));
}
.profile-wid-bg .profile-wid-img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.profile-nav.nav-pills .nav-link {
  color: rgba(255, 255, 255, 0.8);
}
.profile-nav.nav-pills .nav-link::before {
  background-color: rgba(255, 255, 255, 0.1);
}

.profile-project-card {
  border: 1px solid var(--vz-border-color);
  border-left: 3px solid var(--vz-border-color);
}

.profile-project-card.profile-project-primary {
  border-left-color: var(--vz-primary);
}

.profile-project-card.profile-project-secondary {
  border-left-color: var(--vz-secondary);
}

.profile-project-card.profile-project-success {
  border-left-color: var(--vz-success);
}

.profile-project-card.profile-project-info {
  border-left-color: var(--vz-info);
}

.profile-project-card.profile-project-warning {
  border-left-color: var(--vz-warning);
}

.profile-project-card.profile-project-danger {
  border-left-color: var(--vz-danger);
}

.profile-project-card.profile-project-light {
  border-left-color: var(--vz-light);
}

.profile-project-card.profile-project-dark {
  border-left-color: var(--vz-dark);
}

.profile-project-card.profile-project-pink {
  border-left-color: var(--vz-pink);
}

.profile-project-card.profile-project-purple {
  border-left-color: var(--vz-purple);
}

.profile-project-card.profile-project-orange {
  border-left-color: var(--vz-orange);
}

.user-profile-img {
  position: relative;
}
.user-profile-img .profile-img {
  width: 100%;
  height: 250px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 991px) {
  .user-profile-img .profile-img {
    height: 160px;
  }
}
.user-profile-img .profile-foreground-img-file-input {
  display: none;
}
.user-profile-img .profile-photo-edit {
  cursor: pointer;
}

.profile-user {
  position: relative;
  display: inline-block;
}
.profile-user .profile-photo-edit {
  position: absolute;
  right: 0;
  left: auto;
  bottom: 0;
  cursor: pointer;
}
.profile-user .user-profile-image {
  -o-object-fit: cover;
     object-fit: cover;
}
.profile-user .profile-img-file-input {
  display: none;
}

.profile-timeline .accordion-item {
  position: relative;
}
.profile-timeline .accordion-item .accordion-button {
  background-color: transparent;
}
.profile-timeline .accordion-item .accordion-button::after {
  background: none;
}
.profile-timeline .accordion-item::before {
  content: "";
  border-left: 2px dashed var(--vz-border-color);
  position: absolute;
  height: 100%;
  left: 23px;
}
.profile-timeline .accordion-item:first-child::before {
  top: 8px;
}
.profile-timeline .accordion-item:last-child::before {
  height: 20px;
  top: 3px;
}

.profile-setting-img {
  position: relative;
  height: 260px;
}
.profile-setting-img .overlay-content {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
}
.profile-setting-img .profile-img {
  width: 100%;
  height: 250px;
  -o-object-fit: cover;
     object-fit: cover;
}
@media (max-width: 991.98px) {
  .profile-setting-img .profile-img {
    height: 160px;
  }
}
.profile-setting-img .profile-foreground-img-file-input {
  display: none;
}
.profile-setting-img .profile-photo-edit {
  cursor: pointer;
}

@media (max-width: 575.98px) {
  [data-layout=horizontal] .profile-foreground {
    margin-top: 0 !important;
  }
}
[data-layout=semibox] .profile-wrapper {
  padding: 0 1rem;
}

.sitemap-content {
  width: 100%;
  max-width: 1142px;
  margin: 0 auto;
  padding: 0 20px;
}
.sitemap-content * {
  position: relative;
}

.sitemap-horizontal {
  position: relative;
}
.sitemap-horizontal ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.sitemap-horizontal ul a {
  display: block;
  background: var(--vz-light);
  border: 2px solid var(--vz-secondary-bg);
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  font-size: var(--vz-font-base);
  height: 60px;
  padding: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.sitemap-horizontal ul a span {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.administration .director > li > a {
  width: 50%;
  margin: 0 auto 0px auto;
  border-radius: 4px;
}
.administration .subdirector {
  position: absolute;
  width: 100%;
}
.administration .subdirector::after {
  content: "";
  display: block;
  width: 0;
  height: 130px;
  border-left: 2px dashed var(--vz-border-color);
  left: 45.45%;
  position: relative;
}
.administration .subdirector > li:first-child {
  width: 18.59%;
  height: 64px;
  margin: 0 auto 92px auto;
  padding-top: 25px;
  border-bottom: 2px dashed var(--vz-border-color);
  z-index: 1;
  float: right;
  right: 27.2%;
  border-left: 2px dashed var(--vz-border-color);
}
.administration .subdirector > li:first-child a {
  width: 100%;
  left: 25px;
}
@media screen and (max-width: 767px) {
  .administration .subdirector > li:first-child {
    width: 40%;
    right: 10%;
    margin-right: 2px;
  }
  .administration .subdirector::after {
    left: 49.8%;
  }
}

.departments {
  width: 100%;
}
.departments > li:first-child {
  width: 18.59%;
  height: 64px;
  margin: 0 auto 92px auto;
  padding-top: 25px;
  border-bottom: 2px dashed var(--vz-border-color);
  z-index: 1;
  float: left;
  left: 27%;
}
.departments > li:first-child a {
  width: 100%;
  right: 25px;
}
.departments > li:nth-child(2) {
  margin-left: 0;
  clear: left;
}
.departments > li:nth-child(2).department:before {
  border: none;
}
.departments::after {
  content: "";
  display: block;
  position: absolute;
  width: 81.1%;
  height: 22px;
  border-top: 2px dashed var(--vz-border-color);
  border-right: 2px dashed var(--vz-border-color);
  border-left: 2px dashed var(--vz-border-color);
  margin: 0 auto;
  top: 130px;
  left: 9.1%;
}
@media screen and (max-width: 767px) {
  .departments > li:first-child {
    width: 40%;
    left: 10%;
    margin-left: 2px;
  }
  .departments::after {
    border-right: none;
    left: 0;
    width: 50%;
  }
}

.department {
  border-left: 2px dashed var(--vz-border-color);
  float: left;
  margin-left: 1.75%;
  margin-bottom: 60px;
  width: 18.25%;
}
.department::before {
  content: "";
  display: block;
  position: absolute;
  width: 0;
  height: 22px;
  border-left: 2px dashed var(--vz-border-color);
  z-index: 1;
  top: -22px;
  left: 50%;
  margin-left: -4px;
}
.department > a {
  margin: 0 0 -26px -4px;
  z-index: 1;
}
.department ul {
  margin-top: 0px;
  margin-bottom: 0px;
}
.department ul li {
  padding-left: 25px;
  border-bottom: 2px dashed var(--vz-border-color);
  height: 80px;
}
.department ul li a {
  background: var(--vz-secondary-bg);
  margin-top: 48px;
  position: absolute;
  z-index: 1;
  width: 90%;
  height: 60px;
  vertical-align: middle;
  right: -1px;
  text-align: center;
}
.department:first-child {
  margin-left: 0;
  clear: left;
}
@media screen and (min-width: 768px) {
  .department:last-child:before {
    border: none;
  }
}
@media screen and (max-width: 767px) {
  .department {
    float: none;
    width: 100%;
    margin-left: 0;
  }
  .department::before {
    content: "";
    display: block;
    position: absolute;
    width: 0;
    height: 60px;
    border-left: 2px dashed #fff;
    z-index: 1;
    top: -60px;
    left: 0%;
    margin-left: -4px;
  }
  .department:nth-child(2)::before {
    display: none;
  }
}

.hori-sitemap ul {
  padding: 0;
  padding-top: 10px;
  text-align: center;
}
.hori-sitemap ul li {
  position: relative;
}
@media (max-width: 575.98px) {
  .hori-sitemap ul {
    text-align: left;
  }
  .hori-sitemap ul .parent-title a {
    padding-left: 0;
  }
  .hori-sitemap ul .parent-title a:after {
    display: none;
  }
  .hori-sitemap ul .parent-title:before {
    display: none;
  }
}
@media (max-width: 575.98px) {
  .hori-sitemap > ul {
    position: relative;
  }
  .hori-sitemap > ul li {
    padding-top: 10px;
  }
  .hori-sitemap > ul li .second-list,
  .hori-sitemap > ul li .sub-list {
    position: relative;
  }
  .hori-sitemap > ul li .second-list:before,
  .hori-sitemap > ul li .sub-list:before {
    content: "";
    height: calc(100% - 14px);
    border-right: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 0px;
    left: 0px;
  }
  .hori-sitemap > ul li .sub-list:before {
    height: 38%;
  }
  .hori-sitemap > ul li a {
    position: relative;
    padding: 4px 16px 4px 36px;
  }
  .hori-sitemap > ul li a:after {
    content: "";
    width: 24px;
    border-top: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
  }
  .hori-sitemap > ul li ul {
    margin-left: 36px;
  }
}
@media (min-width: 576px) {
  .hori-sitemap ul {
    padding-top: 20px;
  }
  .hori-sitemap ul li {
    padding-top: 30px;
  }
  .hori-sitemap ul li:before {
    content: "";
    height: 24px;
    width: 0;
    border-right: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    margin: 0px auto;
  }
  .hori-sitemap ul li:after {
    content: "";
    width: 100%;
    border-top: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 0;
    left: 50%;
  }
  .hori-sitemap ul li:last-of-type:after {
    display: none;
  }
  .hori-sitemap ul li.parent-title::before {
    content: "";
    height: 23px;
    border-right: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 28px;
    left: 0;
  }
  .hori-sitemap ul li.parent-title::after {
    border: none;
  }
  .hori-sitemap ul li .sub-title {
    position: relative;
  }
  .hori-sitemap ul li .sub-title::before {
    content: "";
    height: 21px;
    border-right: 2px dashed var(--vz-border-color);
    position: absolute;
    top: 27px;
    left: 49%;
    margin: 0 auto;
  }
}
.hori-sitemap a {
  color: var(--vz-body-color);
  padding: 4px 0px;
  display: block;
}

.verti-sitemap a {
  color: var(--vz-body-color);
  display: block;
}
.verti-sitemap .parent-title a {
  padding-left: 0;
}
.verti-sitemap .parent-title a:before {
  display: none;
}
.verti-sitemap .parent-title:before {
  display: none;
}
.verti-sitemap .first-list {
  position: relative;
  padding-top: 10px;
}
.verti-sitemap .first-list:before {
  content: "";
  border-left: 2px dashed var(--vz-border-color);
  position: absolute;
  top: 0;
  height: 100%;
  bottom: 0;
  left: 0;
}
.verti-sitemap .first-list .list-wrap a, .verti-sitemap .first-list li a {
  position: relative;
  padding: 10px 16px 4px 36px;
}
.verti-sitemap .first-list .list-wrap a::before, .verti-sitemap .first-list li a::before {
  content: "";
  width: 24px;
  border-top: 2px dashed var(--vz-border-color);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
.verti-sitemap .first-list .second-list, .verti-sitemap .first-list .third-list {
  margin-left: 42px;
}
.verti-sitemap .first-list .third-list, .verti-sitemap .first-list .second-list {
  position: relative;
}
.verti-sitemap .first-list .third-list li, .verti-sitemap .first-list .second-list li {
  position: relative;
}
.verti-sitemap .first-list .third-list li:before, .verti-sitemap .first-list .second-list li:before {
  content: "";
  height: 100%;
  border-left: 2px dashed var(--vz-border-color);
  position: absolute;
  top: 0;
  left: 0;
  margin: 0px auto;
}
.verti-sitemap .first-list .third-list li:last-child::before, .verti-sitemap .first-list .second-list li:last-child::before {
  height: 13px;
}
.verti-sitemap .first-list:last-child::before {
  height: 25px;
}

.team-box .team-cover, .profile-offcanvas .team-cover, .modal-team-cover {
  display: none;
  position: relative;
  margin-bottom: -140px;
}
.team-box .team-cover img, .profile-offcanvas .team-cover img, .modal-team-cover img {
  height: 140px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.team-box .team-cover::before, .profile-offcanvas .team-cover::before, .modal-team-cover::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, var(--vz-primary-text-emphasis), var(--vz-primary));
  opacity: 0.6;
}

.team-list.grid-view-filter {
  flex-flow: row wrap;
}
.team-list.grid-view-filter .col {
  flex: 0 0 auto;
  width: 25%;
}
.team-list.grid-view-filter .team-box {
  overflow: hidden;
}
.team-list.grid-view-filter .team-box .team-row {
  align-items: start;
}
.team-list.grid-view-filter .team-box .team-row .col {
  width: 100%;
}
.team-list.grid-view-filter .team-box .team-cover {
  display: block;
}
.team-list.grid-view-filter .team-box .team-settings .col {
  width: 50% !important;
  flex: 0 0 auto;
}
.team-list.grid-view-filter .team-box .team-settings .btn-star {
  color: #fff;
}
.team-list.grid-view-filter .team-box .team-settings .dropdown > a {
  color: #fff;
}
.team-list.grid-view-filter .team-box .team-profile-img {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25px;
  margin-top: 36px;
}
.team-list.grid-view-filter .team-box .team-profile-img .avatar-lg {
  font-size: 22px;
}
.team-list.grid-view-filter .team-box .team-profile-img .team-content {
  margin-left: 0px;
  margin-top: 25px;
  text-align: center;
}
.team-list.grid-view-filter .team-box .view-btn {
  width: 100%;
  margin-top: 25px;
}
@media (min-width: 1200px) and (max-width: 1399.98px) {
  .team-list.grid-view-filter .col {
    flex: 0 0 auto;
    width: 33.33%;
  }
}
@media (max-width: 1199.98px) {
  .team-list.grid-view-filter .col {
    flex: 0 0 auto;
    width: 50%;
  }
}
@media (max-width: 767.98px) {
  .team-list.grid-view-filter .col {
    flex: 0 0 auto;
    width: 100%;
  }
}
.team-list.list-view-filter {
  flex-direction: column;
}
.team-list.list-view-filter .team-box {
  margin-bottom: 10px;
}
.team-list.list-view-filter .team-box .team-row {
  align-items: center;
  justify-content: space-between;
}
.team-list.list-view-filter .team-box .team-profile-img {
  display: flex;
  align-items: center;
}
.team-list.list-view-filter .team-box .team-profile-img .avatar-lg {
  height: 4rem;
  width: 4rem;
  font-size: 16px;
}
.team-list.list-view-filter .team-box .team-profile-img .team-content {
  margin-left: 15px;
}
.team-list.list-view-filter .team-box .team-settings {
  width: auto;
  flex: 0 0 auto;
  order: 6;
}
.team-list.list-view-filter .team-box .btn-star {
  color: var(--vz-primary);
}
@media (max-width: 767.98px) {
  .team-list.list-view-filter {
    flex-direction: row;
  }
  .team-list.list-view-filter .col {
    flex: 0 0 auto;
    width: 100%;
  }
  .team-list.list-view-filter .team-box .team-settings {
    width: 100%;
    flex: 0 0 auto;
    order: -1;
    margin-bottom: 10px;
  }
  .team-list.list-view-filter .team-box .team-settings .col {
    width: 50%;
  }
  .team-list.list-view-filter .team-box .team-profile-img {
    margin-bottom: 25px;
  }
  .team-list.list-view-filter .team-box .view-btn {
    width: 100%;
    margin-top: 25px;
  }
}

.modal-team-cover {
  display: block;
}

.list-grid-nav .nav-link.active {
  background-color: var(--vz-info);
  color: #fff;
}

.profile-offcanvas .team-cover {
  margin-bottom: -132px;
  display: block;
  z-index: -1;
}
.profile-offcanvas .btn-star {
  color: #fff;
}
.profile-offcanvas .dropdown > a {
  color: #fff;
}

.countdownlist {
  text-align: center;
  display: flex;
  gap: 24px;
}
.countdownlist .countdownlist-item {
  width: 25%;
}
.countdownlist .countdownlist-item:last-of-type .count-num::after {
  display: none;
}
.countdownlist .count-title {
  font-size: 13px;
  font-weight: var(--vz-font-weight-medium);
  display: block;
  margin-bottom: 8px;
  color: rgba(var(--vz-body-color-rgb), 0.5);
  text-transform: uppercase;
}
.countdownlist .count-num {
  background-color: var(--vz-secondary-bg);
  padding: 16px 8px;
  position: relative;
  border-radius: var(--vz-border-radius);
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  font-weight: var(--vz-font-weight-semibold);
  font-size: 32px;
}
@media (max-width: 575.98px) {
  .countdownlist .count-num {
    font-size: 18px;
  }
}
.countdownlist .count-num::after {
  content: ":";
  font-size: 20px;
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--vz-body-color);
}

.move-animation {
  animation: mover 1s infinite alternate;
}

@keyframes mover {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-16px);
  }
}
.coming-soon-text {
  font-weight: var(--vz-font-weight-semibold);
  text-transform: uppercase;
  color: #fff;
  text-shadow: 3px 4px var(--vz-success);
}

.countdown-input-group {
  max-width: 400px;
}

.search-more-results {
  position: relative;
  overflow: hidden;
}
.search-more-results .nav-icon {
  font-size: 14px;
  color: #fff;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
  left: 0;
  right: 0;
}
.search-more-results .nav-icon i {
  font-size: 20px;
}
@media (max-width: 767.98px) {
  .search-more-results .nav-icon {
    font-size: 14px;
  }
  .search-more-results .nav-icon i {
    font-size: 18px;
  }
}

.video-list .list-element {
  display: none;
}
.video-list .list-element:nth-child(1) {
  display: block;
}
.video-list .list-element:nth-child(2) {
  display: block;
}
.video-list .list-element:nth-child(3) {
  display: block;
}

.search-voice {
  height: 120px;
  width: 120px;
  line-height: 120px;
  margin: 0px auto;
  text-align: center;
  border-radius: 50%;
  z-index: 1;
  position: relative;
}
.search-voice i {
  line-height: 56px;
  font-size: 30px;
}
.search-voice .voice-wave {
  position: absolute;
  width: 120px;
  height: 120px;
  z-index: -1;
  left: 0px;
  right: 0px;
  margin: 0px auto;
  opacity: 0;
  border-radius: 100px;
  animation: voice-wave 1.8s infinite;
  background-color: var(--vz-light);
}
.search-voice .voice-wave:nth-child(2) {
  animation-delay: 0.3s;
}
.search-voice .voice-wave:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes voice-wave {
  0% {
    opacity: 1;
    transform: scale(0);
  }
  100% {
    opacity: 0;
    transform: scale(1);
  }
}
.images-menu .swiper-slide {
  width: auto;
  display: inline-block;
}

#selection-element {
  display: none;
}

.filter-choices-input .choices__inner {
  padding: 0;
  border: none;
  background-color: var(--vz-secondary-bg);
}
.filter-choices-input .choices__input {
  background-color: var(--vz-secondary-bg);
}

@media (min-width: 992px) {
  .sticky-side-div {
    position: sticky;
    top: calc(70px + var(--vz-grid-gutter-width));
  }
}

.product-img-slider .product-nav-slider .nav-slide-item {
  border: 1px solid var(--vz-border-color);
  border-radius: 0.25rem;
  padding: 0.5rem;
  cursor: pointer;
}
.product-img-slider .product-nav-slider .swiper-slide-thumb-active .nav-slide-item {
  background-color: var(--vz-light);
}

.filter-list a.active .listname {
  color: var(--vz-success);
}

#remove-actions {
  display: none;
}

.invoice-table tbody:last-child {
  border: none;
}

.currency-select .choices__inner {
  padding: 0px;
  padding-right: 15px;
  min-height: 0px;
}
.currency-select .choices__list--single {
  padding: 0px 16px 0 4px;
}
.currency-select .choices[data-type*=select-one] {
  bottom: 0px;
}
.currency-select .choices[data-type*=select-one] :after {
  top: 4px;
}
.currency-select .choices[data-type*=select-one] .choices__inner {
  padding-bottom: 0px;
}

.chat-wrapper {
  position: relative;
  overflow-x: hidden;
}

.chat-leftsidebar {
  height: calc(100vh - 137px);
  position: relative;
  background-color: var(--vz-secondary-bg);
}
@media (min-width: 992px) {
  .chat-leftsidebar {
    min-width: 300px;
    max-width: 300px;
    height: calc(100vh - 70px - 60px - 8px);
  }
}

.chat-list {
  margin: 0;
}
.chat-list > li.active a {
  background-color: var(--vz-chat-secondary-bg);
  color: var(--vz-chat-secondary-color);
}
.chat-list > li.active a .badge {
  background-color: rgba(var(--vz-success-rgb), 0.15) !important;
  color: #0abb87 !important;
}
.chat-list > li a {
  display: block;
  padding: 7px 24px;
  color: var(--vz-body-color);
  transition: all 0.4s;
  font-family: var(--vz-font-family-secondary);
  font-weight: var(--vz-font-weight-medium);
  font-size: var(--vz-font-base);
}
.chat-list > li .chat-user-message {
  font-size: 14px;
}
.chat-list > li .unread-msg-user {
  font-weight: var(--vz-font-weight-semibold);
}
.chat-list > li .unread-message {
  position: absolute;
  display: inline-block;
  right: 24px;
  left: auto;
  top: 33px;
}
.chat-list > li .unread-message .badge {
  line-height: 16px;
  font-weight: var(--vz-font-weight-semibold);
  font-size: 10px;
}

.chat-user-img {
  position: relative;
}
.chat-user-img .user-status {
  width: 10px;
  height: 10px;
  background-color: #adb5bd;
  border-radius: 50%;
  border: 2px solid var(--vz-secondary-bg);
  position: absolute;
  right: 0;
  left: auto;
  bottom: 0;
}
.chat-user-img.online .user-status {
  background-color: var(--vz-success);
}
.chat-user-img.away .user-status {
  background-color: var(--vz-warning);
}

.chat-room-list {
  max-height: calc(100vh - 296px);
}
@media (max-width: 991.98px) {
  .chat-room-list {
    height: calc(100vh - 296px);
  }
}

.contact-list li {
  cursor: pointer;
  padding: 8px 24px;
  transition: all 0.4s;
  color: var(--vz-body-color);
  font-family: var(--vz-font-family-secondary);
  font-weight: var(--vz-font-weight-medium);
  font-size: var(--vz-font-base);
}

.contact-list-title {
  padding: 6px 24px;
  color: var(--vz-primary);
  font-weight: var(--vz-font-weight-medium);
  position: relative;
  font-size: 12px;
}
.contact-list-title:after {
  content: "";
  height: 1px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 50px;
  right: 0;
  background-color: var(--vz-light);
}

.user-chat {
  background: url("../images/chat-bg-pattern.png");
  transition: all 0.4s;
  position: relative;
  background-color: var(--vz-body-bg);
}
@media (max-width: 991.98px) {
  .user-chat {
    position: absolute;
    left: 0;
    top: 3px;
    width: 100%;
    height: calc(100% - 3px);
    visibility: hidden;
    transform: translateX(100%);
    z-index: 99;
    padding-top: 70px;
  }
  .user-chat.user-chat-show {
    visibility: visible;
    transform: translateX(0);
  }
}
.user-chat .chat-content {
  position: relative;
}
.user-chat.user-chat-show .chat-welcome-section {
  display: none;
}
@media (min-width: 992px) {
  .user-chat.user-chat-show .chat-content {
    display: flex !important;
  }
}

.user-chat-topbar {
  border-bottom: 1px solid transparent;
  background-color: var(--vz-secondary-bg);
}
@media (max-width: 991.98px) {
  .user-chat-topbar {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1;
  }
}

.user-chat-nav .nav-btn {
  height: 40px;
  width: 40px;
  line-height: 40px;
  box-shadow: none;
  padding: 0;
  font-size: 20px;
  color: #343a40;
}
@media (max-width: 575.98px) {
  .user-chat-nav {
    display: flex;
    justify-content: flex-end;
  }
}

.chat-conversation {
  height: calc(100vh - 299px);
}
@media (max-width: 991.98px) {
  .chat-conversation {
    height: calc(100vh - 275px);
  }
}
.chat-conversation .simplebar-content-wrapper {
  display: flex;
  flex-direction: column;
}
.chat-conversation .simplebar-content-wrapper .simplebar-content {
  margin-top: auto;
}
.chat-conversation .chat-conversation-list {
  padding-top: 10px;
  margin-bottom: 0;
}
.chat-conversation .chat-conversation-list > li {
  display: flex;
}
.chat-conversation li:last-of-type .conversation-list {
  margin-bottom: 0;
}
.chat-conversation .chat-list.left .check-message-icon {
  display: none;
}
.chat-conversation .chat-list .message-box-drop {
  visibility: hidden;
}
.chat-conversation .chat-list:hover .message-box-drop {
  visibility: visible;
}
.chat-conversation .chat-avatar {
  margin: 0 16px 0 0;
}
.chat-conversation .chat-avatar img {
  width: 28px;
  height: 28px;
  border-radius: 50%;
}
.chat-conversation .chat-day-title {
  position: relative;
  text-align: center;
  margin-bottom: 24px;
  margin-top: 12px;
  width: 100%;
}
.chat-conversation .chat-day-title .title {
  background-color: #fff;
  position: relative;
  font-size: 13px;
  z-index: 1;
  padding: 6px 12px;
  border-radius: 5px;
}
.chat-conversation .chat-day-title:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  right: 0;
  background-color: rgba(var(--vz-primary-rgb), 0.2);
  top: 10px;
}
.chat-conversation .chat-day-title .badge {
  font-size: 12px;
}
.chat-conversation .conversation-list {
  margin-bottom: 24px;
  display: inline-flex;
  position: relative;
  align-items: flex-end;
  max-width: 80%;
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list {
    max-width: 90%;
  }
}
.chat-conversation .conversation-list .ctext-wrap {
  display: flex;
  margin-bottom: 10px;
}
.chat-conversation .conversation-list .ctext-content {
  word-wrap: break-word;
  word-break: break-word;
}
.chat-conversation .conversation-list .ctext-wrap-content {
  padding: 12px 20px;
  background-color: var(--vz-chat-primary-bg);
  position: relative;
  border-radius: 3px;
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .attached-file-avatar {
    display: none;
  }
  .chat-conversation .conversation-list .ctext-wrap-content .attached-file .dropdown .dropdown-toggle {
    display: block;
  }
}
.chat-conversation .conversation-list .conversation-name {
  font-weight: var(--vz-font-weight-medium);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}
.chat-conversation .conversation-list .dropdown .dropdown-toggle {
  font-size: 18px;
  padding: 4px;
  color: #878a99;
}
.chat-conversation .conversation-list .dropdown .dropdown-toggle::after {
  display: none;
}
@media (max-width: 575.98px) {
  .chat-conversation .conversation-list .dropdown .dropdown-toggle {
    display: none;
  }
}
.chat-conversation .conversation-list .chat-time {
  font-size: 12px;
  margin-top: 4px;
  text-align: right;
}
.chat-conversation .conversation-list .message-img {
  border-radius: 0.2rem;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.chat-conversation .conversation-list .message-img .message-img-list {
  position: relative;
}
.chat-conversation .conversation-list .message-img img {
  max-width: 150px;
}
.chat-conversation .conversation-list .message-img .message-img-link {
  position: absolute;
  right: 10px;
  left: auto;
  bottom: 10px;
}
.chat-conversation .conversation-list .message-img .message-img-link li > a {
  font-size: 18px;
  color: #fff;
  display: inline-block;
  line-height: 20px;
  width: 26px;
  height: 24px;
  border-radius: 3px;
  background-color: rgba(33, 37, 41, 0.7);
  text-align: center;
}
.chat-conversation .right {
  justify-content: flex-end;
}
.chat-conversation .right .chat-avatar {
  order: 3;
  margin-right: 0px;
  margin-left: 16px;
}
.chat-conversation .right .chat-time {
  text-align: left;
  color: #878a99;
}
.chat-conversation .right .conversation-list {
  text-align: right;
}
.chat-conversation .right .conversation-list .ctext-wrap {
  justify-content: flex-end;
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content {
  order: 2;
  background-color: var(--vz-chat-secondary-bg);
  color: var(--vz-chat-secondary-color);
  text-align: right;
  box-shadow: var(--vz-element-shadow);
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block {
  background-color: rgba(255, 255, 255, 0.5);
  border-color: rgba(var(--vz-success-rgb), 1);
  color: #646c9a;
}
.chat-conversation .right .conversation-list .ctext-wrap .ctext-wrap-content .replymessage-block .conversation-name {
  color: rgba(var(--vz-success-rgb), 1);
}
.chat-conversation .right .conversation-list .conversation-name {
  justify-content: flex-end;
}
.chat-conversation .right .conversation-list .conversation-name .check-message-icon {
  order: 1;
}
.chat-conversation .right .conversation-list .conversation-name .time {
  order: 2;
}
.chat-conversation .right .conversation-list .conversation-name .name {
  order: 3;
}
.chat-conversation .right .conversation-list .dropdown {
  order: 1;
}
.chat-conversation .right .dot {
  background-color: #212529;
}

.chat-input-section {
  border-top: 1px solid transparent;
  background-color: var(--vz-secondary-bg);
  position: relative;
  z-index: 1;
}
.chat-input-section .chat-input-feedback {
  display: none;
  position: absolute;
  top: -20px;
  left: 4px;
  font-size: 12px;
  color: var(--vz-danger);
}
.chat-input-section .show {
  display: block;
}

.chat-input-links {
  display: flex;
}
.chat-input-links .links-list-item > .btn {
  box-shadow: none;
  padding: 0;
  font-size: 20px;
  width: 37.5px;
  height: 37.5px;
}
.chat-input-links .links-list-item > .btn.btn-link {
  color: #878a99;
}

.copyclipboard-alert {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  display: none;
}

.replyCard {
  position: absolute;
  left: 0;
  right: 0;
  border-top: 1px solid var(--vz-border-color);
  overflow: hidden;
  opacity: 0;
  bottom: 0;
  border-radius: 0;
  transition: all 0.4s;
}
@media (max-width: 991.98px) {
  .replyCard {
    bottom: -12px;
  }
}
.replyCard.show {
  transform: translateY(-88px);
  opacity: 1;
}
@media (max-width: 991.98px) {
  .replyCard.show {
    transform: translateY(-83px);
  }
}

.replymessage-block {
  padding: 12px 20px;
  margin-bottom: 8px;
  text-align: left;
  border-radius: 4px;
  background-color: rgba(var(--vz-success-rgb), 0.1);
  border-left: 2px solid rgba(var(--vz-success-rgb), 1);
}
.replymessage-block .conversation-name {
  color: rgba(var(--vz-success-rgb), 1);
  font-size: 14px;
}

.chat-sm .ctext-wrap-content {
  box-shadow: none !important;
}
.chat-sm .message-img img {
  max-width: 90px !important;
}
.chat-sm .message-img-link {
  bottom: 0 !important;
  right: 5px !important;
}

@media (min-width: 1025px) {
  [data-layout=horizontal] .chat-wrapper {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .chat-wrapper {
    margin-top: 0 !important;
  }
}
[data-layout=horizontal] .chat-leftsidebar {
  height: calc(100vh - 70px - 60px - 54px);
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .chat-leftsidebar {
    height: calc(100vh - 70px - 60px - 8px);
  }
}
[data-layout=horizontal] .chat-room-list {
  height: calc(100vh - 352px);
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .chat-room-list {
    height: calc(100vh - 265px);
  }
}
[data-layout=horizontal] .chat-conversation {
  height: calc(100vh - 343px);
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .chat-conversation {
    height: calc(100vh - 275px);
  }
}

.email-wrapper {
  position: relative;
  overflow-x: hidden;
}

.email-menu-sidebar {
  height: calc(100vh - 137px);
  position: relative;
  background-color: var(--vz-secondary-bg);
  transition: all 0.2s;
}
@media (min-width: 992px) {
  .email-menu-sidebar {
    min-width: 250px;
    max-width: 250px;
    height: calc(100vh - 70px - 60px - 8px);
  }
}
@media (max-width: 991.98px) {
  .email-menu-sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: 200px;
    max-width: 100%;
    z-index: 1003;
    box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
    transform: translateX(-100%);
    visibility: hidden;
    height: 100vh;
  }
  .email-menu-sidebar.menubar-show {
    visibility: visible;
    transform: none;
  }
}
.email-menu-sidebar .email-menu-sidebar-scroll {
  height: calc(100vh - 295px);
}
@media (max-width: 991.98px) {
  .email-menu-sidebar .email-menu-sidebar-scroll {
    height: calc(100vh - 150px);
  }
}

.mail-list a {
  display: flex;
  align-items: center;
  color: #878a99;
  padding: 5px 0;
  font-weight: var(--vz-font-weight-medium);
}
.mail-list a:hover {
  color: var(--vz-primary);
}
.mail-list a i {
  font-size: 14px;
}
.mail-list a.active {
  color: var(--vz-success);
  font-weight: var(--vz-font-weight-semibold);
}
.mail-list a.active i {
  color: var(--vz-success);
}

.email-chat-list a {
  padding: 6px 24px;
}
.email-chat-list a.active {
  background-color: rgba(var(--vz-light-rgb), 0.7);
}

.email-topbar-link .btn-ghost-secondary {
  color: var(--vz-secondary-color);
}
.email-topbar-link .btn-ghost-secondary:hover {
  color: var(--vz-secondary);
}

.email-content {
  width: 100%;
  background-color: var(--vz-secondary-bg);
  transition: all 0.2s;
}

.unreadConversations-alert {
  position: fixed;
  bottom: 60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.email-detail-content {
  position: relative;
  top: 0;
  bottom: 0;
  left: -2px;
  width: 0;
  background-color: var(--vz-secondary-bg);
  transform: translateX(200%);
  transition: all 0.2s;
  display: none;
  border-left: 2px solid var(--vz-body-bg);
}

.email-detail-show .email-detail-content {
  transform: none;
  display: block;
  width: 60%;
}
.email-detail-show .email-content {
  margin-right: 0;
}

.email-detail-content-scroll {
  height: calc(100vh - 400px);
}
@media (max-width: 1349.98px) {
  .email-detail-content-scroll {
    height: calc(100vh - 257px);
  }
}

.message-list-content {
  height: calc(100vh - 242px);
}
@media (max-width: 575.98px) {
  .message-list-content {
    height: calc(100vh - 230px);
  }
}

.message-list {
  display: block;
  padding-left: 0;
  margin: 0;
}
.message-list li {
  position: relative;
  display: block;
  height: 50px;
  line-height: 50px;
  cursor: default;
  transition-duration: 0.3s;
  clear: both;
}
.message-list li a {
  color: var(--vz-heading-color);
}
.message-list li:hover {
  background: var(--vz-light);
  transition-duration: 0.05s;
}
.message-list li .col-mail {
  float: left;
  position: relative;
}
.message-list li .col-mail-1 {
  width: 280px;
}
.message-list li .col-mail-1 .star-toggle,
.message-list li .col-mail-1 .checkbox-wrapper-mail,
.message-list li .col-mail-1 .dot {
  display: block;
  float: left;
}
.message-list li .col-mail-1 .dot {
  border: 4px solid transparent;
  border-radius: 100px;
  margin: 22px 26px 0;
  height: 0;
  width: 0;
  line-height: 0;
  font-size: 0;
}
.message-list li .col-mail-1 .checkbox-wrapper-mail {
  margin: 15px 0 0 20px;
  line-height: normal;
}
.message-list li .col-mail-1 .star-toggle {
  margin-top: 18px;
  margin-left: 5px;
}
.message-list li .col-mail-1 .title {
  position: absolute;
  top: 0;
  left: 95px;
  right: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-bottom: 0;
}
@media (max-width: 575.98px) {
  .message-list li .col-mail-1 .title {
    left: 95px;
  }
}
.message-list li .col-mail-2 {
  position: absolute;
  top: 0;
  left: 280px;
  right: 0;
  bottom: 0;
}
.message-list li .col-mail-2 .subject,
.message-list li .col-mail-2 .date {
  position: absolute;
  top: 0;
}
.message-list li .col-mail-2 .subject {
  left: 0;
  right: 110px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.message-list li .col-mail-2 .subject .teaser {
  color: var(--vz-secondary-color);
}
.message-list li .col-mail-2 .date {
  right: 0;
  width: 100px;
  padding-left: 20px;
}
.message-list li.active, .message-list li.active:hover {
  box-shadow: inset 3px 0 0 var(--vz-primary);
  background-color: var(--vz-light);
}
.message-list li.unread {
  color: var(--vz-body-color);
}
.message-list li.unread a {
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-semibold);
}

#unreadConversations, #email-topbar-actions {
  display: none;
}

#elmLoader {
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
}

.email-compose-input {
  padding-right: 80px;
}

@media (max-width: 1349.98px) {
  .email-detail-content {
    display: block;
    position: fixed;
    top: 0;
    bottom: 0;
    left: auto;
    right: 0;
    width: 400px;
    max-width: 100%;
    z-index: 1003;
    box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
    transform: translateX(100%);
    visibility: hidden;
  }
  .email-detail-show .email-detail-content {
    width: 400px;
    transform: none;
    visibility: visible;
  }
}
@media (max-width: 575.98px) {
  .message-list li .col-mail-1 {
    width: 180px;
  }
}
.email-editor .ck-editor__editable_inline {
  min-height: 200px !important;
}

.email-chat-detail {
  width: 350px;
  position: fixed;
  max-width: 100%;
  bottom: 60px;
  right: 60px;
  z-index: 9;
  display: none;
}
.email-chat-detail .card {
  box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
}
@media (max-width: 515.98px) {
  .email-chat-detail {
    left: 16px;
    right: 16px;
  }
}

.message-list-scroll {
  position: relative;
  overflow-y: auto;
}
.message-list-scroll::-webkit-scrollbar {
  -webkit-appearance: none;
}
.message-list-scroll::-webkit-scrollbar:vertical {
  width: 12px;
}
.message-list-scroll::-webkit-scrollbar:horizontal {
  height: 12px;
}
.message-list-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-body-color-rgb), 0.075);
  border-radius: 10px;
  border: 2px solid var(--vz-secondary-bg);
}
.message-list-scroll::-webkit-scrollbar-track {
  border-radius: 10px;
  background-color: var(--vz-secondary-bg);
}

@media (min-width: 1025px) {
  [data-layout=horizontal] .email-wrapper {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .email-wrapper {
    margin-top: 0 !important;
  }
}
[data-layout=horizontal] .email-menu-sidebar {
  height: calc(100vh - 70px - 60px - 54px);
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .email-menu-sidebar {
    height: 100vh;
  }
}
[data-layout=horizontal] .email-menu-sidebar .email-menu-sidebar-scroll {
  height: calc(100vh - 330px);
}
@media (max-width: 991.98px) {
  [data-layout=horizontal] .email-menu-sidebar .email-menu-sidebar-scroll {
    height: calc(100vh - 150px);
  }
}
[data-layout=horizontal] .message-list-content {
  height: calc(100vh - 289px);
}
[data-layout=horizontal] .email-detail-content-scroll {
  height: calc(100vh - 435px);
}
@media (max-width: 1349.98px) {
  [data-layout=horizontal] .email-detail-content-scroll {
    height: calc(100vh - 252px);
  }
}

.tasks-board {
  display: flex;
  overflow-x: auto;
  align-items: stretch;
}
.tasks-board .tasks-list {
  min-width: 300px;
  margin-right: 24px;
}
.tasks-board::-webkit-scrollbar {
  -webkit-appearance: none;
}
.tasks-board::-webkit-scrollbar:vertical {
  width: 10px;
}
.tasks-board::-webkit-scrollbar:horizontal {
  height: 8px;
}
.tasks-board::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-dark-rgb), 0.075);
  border-radius: 8px;
}
.tasks-board::-webkit-scrollbar-track {
  border-radius: 8px;
}

.tasks-box .progress {
  border-radius: 0px 0px var(--vz-border-radius) var(--vz-border-radius);
}
.tasks-box .tasks-img {
  height: 135px;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  margin: 12px 0px;
}
.tasks-box:last-child {
  margin-bottom: 0px;
}

.tasks-wrapper {
  max-height: calc(100vh - 418px);
}

.tasks {
  min-height: 180px;
  position: relative;
}
.tasks.noTask::before {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  height: 180px;
  width: 270px;
  margin: 0 auto;
  background-image: url("../images/file.png");
  background-size: cover;
  background-position: center;
}

#tasksList tr .tasks-list-menu {
  opacity: 0;
}
#tasksList tr:hover .tasks-list-menu {
  opacity: 1;
}

.layout-wrapper.landing {
  background-color: var(--vz-secondary-bg);
  font-size: 15px;
}

.navbar-show .vertical-overlay {
  display: block;
}

.section {
  padding: 90px 0;
  position: relative;
}
@media (max-width: 767.98px) {
  .section {
    padding: 50px 0;
  }
}

.icon-effect {
  position: relative;
}
.icon-effect::before {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: rgba(var(--vz-success-rgb), 0.2);
  border-radius: 50%;
  bottom: 0;
  left: 0;
}

.navbar-landing {
  padding: 10px 0px;
  transition: all 0.5s ease;
}
@media (max-width: 991.98px) {
  .navbar-landing {
    background-color: var(--vz-secondary-bg);
    box-shadow: 0 1px 16px -2px rgba(56, 65, 74, 0.15);
    padding: 10px 8px;
  }
}
.navbar-landing .navbar-nav .nav-item .nav-link {
  font-size: 16px;
  font-weight: var(--vz-font-weight-medium);
  transition: all 0.4s;
  font-family: var(--vz-font-family-secondary);
  color: var(--vz-body-color);
  padding: 14px;
}
@media (max-width: 991.98px) {
  .navbar-landing .navbar-nav .nav-item .nav-link {
    padding: 8px 0px;
  }
}
.navbar-landing .navbar-nav .nav-item .nav-link:hover, .navbar-landing .navbar-nav .nav-item .nav-link.active, .navbar-landing .navbar-nav .nav-item .nav-link:focus {
  color: var(--vz-success) !important;
}
.navbar-landing.is-sticky {
  background-color: var(--vz-secondary-bg);
  box-shadow: 0 1px 16px -2px rgba(56, 65, 74, 0.15);
}

.navbar-light .navbar-brand .card-logo-dark {
  display: none;
}
.navbar-light .navbar-brand .card-logo-light {
  display: block;
}
.navbar-light .navbar-nav .nav-item .nav-link {
  color: rgba(var(--vz-white-rgb), 0.75);
}
.navbar-light.is-sticky .navbar-nav .nav-item .nav-link {
  color: var(--vz-body-color);
}
.navbar-light.is-sticky .navbar-brand .card-logo-dark {
  display: block;
}
.navbar-light.is-sticky .navbar-brand .card-logo-light {
  display: none;
}

.hero-section {
  background-color: rgba(var(--vz-light-rgb), 0.5);
}
.hero-section .hero-shape-svg svg path {
  fill: var(--vz-secondary-bg);
}

.bg-overlay-pattern {
  background-image: url("../images/landing/bg-pattern.png");
  background-color: transparent;
  background-position: center;
  background-size: cover;
  opacity: 0.2;
}

.demo-carousel {
  position: relative;
  z-index: 1;
}
.demo-carousel .carousel-item .demo-item {
  background-color: var(--vz-secondary-bg);
  padding: 8px;
  border-radius: 7px;
}
.demo-carousel .demo-img-patten-top {
  position: absolute;
  right: -50px;
  top: -16px;
  max-width: 230px;
}
.demo-carousel .demo-img-patten-bottom {
  position: absolute;
  left: -70px;
  bottom: -50px;
  max-width: 230px;
}

.client-images img {
  max-height: 45px;
  width: auto;
  margin: 12px auto;
  transition: all 0.4s;
}

.plan-box {
  max-width: 356px;
  margin-left: auto;
  margin-right: auto;
}

.process-card {
  position: relative;
}
.process-card .process-arrow-img {
  position: absolute;
  left: 75%;
  top: 7%;
  width: 50%;
  opacity: 0.1;
}

.custom-footer {
  color: #9ba7b3;
}

.footer-list li a {
  color: #9ba7b3;
  padding: 7px 0;
  display: block;
  transition: all 0.3s ease-in-out;
}
.footer-list li a:hover {
  color: rgba(255, 255, 255, 0.9);
}

.footer-social-link .avatar-title {
  color: #778089;
  background-color: rgba(255, 255, 255, 0.05);
  transition: all 0.3s ease;
}
.footer-social-link .avatar-title:hover {
  color: #fff;
  background-color: var(--vz-primary);
}

#back-to-top.landing-back-top {
  bottom: 35px;
}

[data-bs-theme=dark] .layout-wrapper.landing .demo-img-patten-top,
[data-bs-theme=dark] .layout-wrapper.landing .demo-img-patten-bottom {
  opacity: 0.2;
}
[data-bs-theme=dark] .layout-wrapper.landing footer.bg-dark {
  background-color: var(--vz-secondary-bg) !important;
}
[data-bs-theme=dark] .navbar-light.is-sticky .navbar-brand .card-logo-dark {
  display: none;
}
[data-bs-theme=dark] .navbar-light.is-sticky .navbar-brand .card-logo-light {
  display: block;
}

.bookmark-icon .btn {
  color: var(--vz-secondary-color);
  background-color: var(--vz-light) !important;
  box-shadow: 0 1px 2px rgba(56, 65, 74, 0.15);
  font-size: var(--vz-font-base);
}
.bookmark-icon .btn:hover, .bookmark-icon .btn.active {
  border-color: transparent;
  color: var(--vz-danger);
}

.explore-box {
  border-radius: 9px;
  overflow: hidden;
}
.explore-box .explore-img {
  height: 280px;
  -o-object-fit: cover;
     object-fit: cover;
}
.explore-box .explore-place-bid-img {
  position: relative;
  overflow: hidden;
  z-index: 0;
}
.explore-box .explore-place-bid-img .bg-overlay {
  position: absolute;
  right: 0;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(var(--vz-dark-rgb), 0.4);
  opacity: 0;
  transition: all 0.5s ease;
}
.explore-box .explore-place-bid-img .place-bid-btn {
  top: 50%;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
}
.explore-box .explore-place-bid-img .place-bid-btn .btn {
  opacity: 0;
  bottom: -25px;
  transition: 0.5s ease;
}
.explore-box:hover .explore-place-bid-img .place-bid-btn .btn {
  opacity: 1;
  bottom: 0;
}
.explore-box:hover .explore-place-bid-img .bg-overlay {
  opacity: 1;
}
.explore-box .discount-time {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(5px);
          backdrop-filter: blur(5px);
  padding: 8px;
}

.nft-hero {
  background-image: url("../images/nft/bg-home.jpg");
  background-size: cover;
  background-position: bottom;
  padding: 222px 0 150px 0;
}
.nft-hero .bg-overlay {
  background-color: #05175f;
  opacity: 0.85;
}

#explorecard-list .list-element {
  display: none;
}
#explorecard-list .list-element:nth-child(-n+10) {
  display: block;
}

.file-manager-menu {
  max-height: calc(100vh - 296px);
}
@media (max-width: 991.98px) {
  .file-manager-menu {
    height: calc(100vh - 296px);
  }
}
.file-manager-menu li {
  padding: 5px 0px;
}
.file-manager-menu li a {
  color: var(--vz-body-color);
  font-weight: var(--vz-font-weight-medium);
  transition: all 0.5s ease;
}
.file-manager-menu li a.active, .file-manager-menu li a:hover, .file-manager-menu li a[aria-expanded=true] {
  color: var(--vz-success);
}
.file-manager-menu li .sub-menu li {
  padding-left: 25px;
  position: relative;
}
.file-manager-menu li .sub-menu li::before {
  content: "\f0374";
  position: absolute;
  left: 0px;
  font-family: Material Design Icons;
  color: rgba(var(--vz-dark-rgb), 0.4);
}

.file-manager-sidebar {
  position: relative;
  background-color: var(--vz-secondary-bg);
}
@media (min-width: 992px) {
  .file-manager-sidebar {
    min-width: 300px;
    max-width: 300px;
    height: calc(100vh - 70px - 60px - 8px);
  }
}
@media (max-width: 991.98px) {
  .file-manager-sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: 200px;
    max-width: 100%;
    z-index: 1003;
    box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
    transform: translateX(-100%);
    visibility: hidden;
    height: 100vh;
  }
  .file-manager-sidebar.menubar-show {
    visibility: visible;
    transform: none;
  }
}

.file-menu-sidebar-scroll {
  height: calc(100vh - 375px);
}
@media (max-width: 991.98px) {
  .file-menu-sidebar-scroll {
    height: calc(100vh - 190px);
  }
}

.file-manager-content {
  background-color: var(--vz-secondary-bg);
}

.file-manager-content-scroll {
  height: calc(100vh - 185px);
}
@media (max-width: 991.98px) {
  .file-manager-content-scroll {
    height: calc(100vh - 144px);
  }
}
.file-manager-content-scroll .simplebar-content {
  height: 100%;
}

.file-detail-content-scroll {
  height: calc(100vh - 180px);
}
@media (max-width: 991.98px) {
  .file-detail-content-scroll {
    height: 100vh;
  }
}
.file-detail-content-scroll .simplebar-content {
  height: 100%;
}

.file-manager-detail-content {
  width: 32%;
  max-width: 100%;
  background-color: var(--vz-secondary-bg);
  display: none;
}

.file-detail-show .file-manager-detail-content {
  display: block;
}

#file-overview {
  display: none;
}

@media (max-width: 1399.98px) {
  .file-manager-detail-content {
    position: fixed;
    top: 0;
    bottom: 0;
    left: auto;
    right: 0;
    width: 400px;
    max-width: 100%;
    z-index: 1003;
    box-shadow: 0 5px 10px rgba(30, 32, 37, 0.12);
    transform: translateX(100%);
  }
  .file-detail-show .file-manager-detail-content {
    transform: none;
  }
}
.file-details-box {
  height: 195px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.to-do-menu li .nav-link {
  color: var(--vz-body-color);
  padding-left: 0;
  font-weight: var(--vz-font-weight-medium);
  background-color: var(--vz-light);
  padding: 10px 12px;
  border-radius: 4px;
  display: block;
  margin-bottom: 8px;
}
.to-do-menu li .nav-link:hover, .to-do-menu li .nav-link.active {
  color: var(--vz-success);
}

.todo-content {
  height: calc(100vh - 362px);
  overflow-y: auto;
}
.todo-content::-webkit-scrollbar {
  -webkit-appearance: none;
}
.todo-content::-webkit-scrollbar:vertical {
  width: 8px;
}
.todo-content::-webkit-scrollbar:horizontal {
  height: 10px;
}
.todo-content::-webkit-scrollbar-thumb {
  background-color: rgba(var(--vz-body-color-rgb), 0.075);
  border-radius: 8px;
}
.todo-content::-webkit-scrollbar-track {
  border-radius: 8px;
}

.todo-task table .form-check .form-check-input:checked + .form-check-label {
  text-decoration: line-through;
}

.task-handle {
  cursor: move;
}

#task-error-msg {
  display: none;
}

.bg-job {
  background-image: url(../images/new.png);
  background-size: cover;
  background-position: center;
}

.job-list-view-card .background {
  height: 130px;
}

@media (min-width: 1400px) and (max-width: 1499.98px) {
  .job-list-row.row .col-xxl-3 {
    width: 33%;
  }
}

.custom-container {
  width: 90%;
}

.job-navbar .dropdown-menu .dropdown-item .avatar-sm {
  height: 2.5rem;
  width: 2.5rem;
}
@media (max-width: 991.98px) {
  .job-navbar {
    background-color: var(--vz-secondary-bg);
    padding: 16px 0;
  }
}

.job-hero-section {
  position: relative;
  padding: 165px 0;
  overflow-x: hidden;
}

.job-panel-filter {
  background-color: var(--vz-secondary-bg);
  border-radius: 0.25rem;
  overflow: hidden;
}
.job-panel-filter .form-control {
  height: 45px;
  border: none;
  border-radius: 0;
  background-color: transparent;
}
.job-panel-filter .btn {
  border-radius: 0;
}
.job-panel-filter input.form-control {
  border-right: 1px solid var(--vz-border-color);
}
@media (max-width: 767.98px) {
  .job-panel-filter {
    background-color: transparent;
  }
  .job-panel-filter .form-control {
    background-color: #fff;
    border-radius: var(--vz-border-radius);
  }
  .job-panel-filter input.form-control {
    border: 0;
  }
  .job-panel-filter .btn {
    border-radius: var(--vz-border-radius);
  }
}

.circle-effect {
  position: absolute;
  top: 0;
  z-index: 0;
  right: -55px;
  margin: 0 auto;
}
.circle-effect .circle {
  border: 1px solid var(--vz-border-color);
  height: 240px;
  width: 240px;
  margin: 0 auto;
  border-radius: 50%;
}
.circle-effect .circle2 {
  position: absolute;
  top: -20px;
  right: -20px;
  border: 1px solid var(--vz-border-color);
  height: 280px;
  width: 280px;
  margin: 0 auto;
  border-radius: 50%;
}
.circle-effect .circle3 {
  position: absolute;
  top: -40px;
  right: -40px;
  border: 1px solid var(--vz-border-color);
  height: 320px;
  width: 320px;
  margin: 0 auto;
  border-radius: 50%;
}
.circle-effect .circle4 {
  position: absolute;
  top: -60px;
  right: -60px;
  border: 1px solid var(--vz-border-color);
  height: 360px;
  width: 360px;
  margin: 0 auto;
  border-radius: 50%;
}
@media (max-width: 991.98px) {
  .circle-effect {
    display: none;
  }
}

.home-img .user-img {
  position: relative;
  z-index: 1;
}

.application-box,
.feedback-box,
.inquiry-box {
  max-width: 240px;
  position: absolute;
  z-index: 2;
  transition: all 0.5s ease;
}
.application-box:hover,
.feedback-box:hover,
.inquiry-box:hover {
  transform: translateY(-5px);
}

.inquiry-box {
  left: -134px;
  top: 90px;
}
@media (max-width: 991.98px) {
  .inquiry-box {
    left: 0;
  }
}

.application-box {
  bottom: 50px;
  right: -160px;
}
@media (max-width: 991.98px) {
  .application-box {
    right: 0;
  }
}

.feedback-box {
  right: -50px;
  bottom: -54px;
}
@media (max-width: 576.98px) {
  .feedback-box {
    right: 0;
    bottom: 0;
  }
}

.job-icon-effect {
  height: 30px;
  width: 30px;
  position: absolute;
  top: 0px;
  left: -2%;
  border-radius: 50%;
  background: repeating-linear-gradient(-55deg, rgba(255, 255, 255, 0) 0.8px, var(--vz-success) 1.6px, var(--vz-success) 2px, rgba(255, 255, 255, 0) 3.8px, rgba(255, 255, 255, 0) 5px);
  z-index: 0;
  opacity: 0.2;
}
.job-icon-effect span {
  position: relative;
  z-index: 1;
}

.about-img-section {
  position: relative;
}
.about-img-section::before {
  content: "";
  position: absolute;
  left: 20px;
  right: -20px;
  top: -20px;
  bottom: 20px;
  border: 1px solid var(--vz-info);
  z-index: 0;
  border-radius: 0.25rem;
}
@media (max-width: 576.98px) {
  .about-img-section::before {
    right: 20px;
    top: 20px;
  }
}

.features-company-widgets {
  position: absolute;
  bottom: -18px;
  right: -35px;
}

.blog-grid-card .blog-img {
  height: 230px;
  width: 100%;
  transition: all 0.8s ease;
}
.blog-grid-card:hover .blog-img {
  transform: scale(1.1);
}
