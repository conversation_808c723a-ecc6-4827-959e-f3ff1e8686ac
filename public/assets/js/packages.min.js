/******/ (() => { // webpackBootstrap
/*!**********************************!*\
  !*** ./resources/js/packages.js ***!
  \**********************************/
$(function () {
  $.ajaxSetup({
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    cache: false
  });
  var $apiProviderID = $('#api_provider_id');
  var $serviceID = $('#service_id');
  var $serviceName = $('.service-name');
  var $min = $('#min');
  var $max = $('#max');
  var $rate = $('#rate');
  var $type = $('#service_type');
  var $refill = $('#refill');
  var $dripFeed = $('#drip_feed');
  var $cancel = $('#cancel');
  var initialProviderId = $apiProviderID.data('selected') || $apiProviderID.val();
  var initialServiceId = $serviceID.data('selected');
  var initialServiceName = $serviceID.data('selected-name');
  var resetServiceInfo = function resetServiceInfo() {
    $serviceID.html('<option value="0">Chọn dịch vụ</option>').val('0').trigger('change');
    $serviceName.val('');
    $min.val('');
    $max.val('');
    $rate.val('');
    $type.val('');
    $refill.prop('checked', false);
    $dripFeed.prop('checked', false);
    $cancel.prop('checked', false);
  };
  var fillServiceInfo = function fillServiceInfo() {
    var service = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    $serviceName.val(service.name || '');
    $min.val(service.min || '');
    $max.val(service.max || '');
    $rate.val(service.rate || '');
    $type.val(service.type || '');
    $refill.prop('checked', !!service.refill);
    $dripFeed.prop('checked', !!service.drip_feed);
    $cancel.prop('checked', !!service.cancel);
  };
  var formatTemplate = function formatTemplate(_ref) {
    var id = _ref.id,
      text = _ref.text;
    id = id || '';
    return $("<span><span class=\"dropdown-item-indicator\">".concat(id, " &#45;</span>").concat(text, "</span>"));
  };

  // Hàm khởi tạo service select
  var initializeServiceSelect = function initializeServiceSelect(providerId) {
    var preloadServiceId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    var preloadServiceName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
    var url = $apiProviderID.data('url');
    if (preloadServiceId && preloadServiceId !== '0' && preloadServiceName) {
      var preloadOption = new Option(preloadServiceName, preloadServiceId, true, true);
      $serviceID.html('').append(preloadOption);
    } else {
      $serviceID.html('<option value="0">Chọn dịch vụ</option>');
    }
    Base.select($serviceID, {
      ajax: {
        url: url,
        delay: 250,
        cache: true,
        data: function data(params) {
          return {
            provider_id: providerId,
            q: params.term,
            page: params.page || 1
          };
        },
        processResults: function processResults(_ref2) {
          var data = _ref2.data;
          return {
            results: $.map(data.data, function (item) {
              return {
                id: item.service,
                text: item.name,
                raw: item
              };
            }),
            pagination: {
              more: !!data.next_page_url && Object.keys(data.data).length > 0
            }
          };
        }
      },
      templateResult: formatTemplate,
      templateSelection: formatTemplate
    }, true);
    if (preloadServiceId && preloadServiceId !== '0') {
      loadServiceDetails(providerId, preloadServiceId);
    }
    $serviceID.off('select2:select').on('select2:select', function (e) {
      var _e$params$data;
      var service = (_e$params$data = e.params.data) === null || _e$params$data === void 0 ? void 0 : _e$params$data.raw;
      if (service) {
        fillServiceInfo(service);
      } else {
        var serviceId = e.params.data.id;
        if (serviceId && serviceId !== '0') {
          loadServiceDetails(providerId, serviceId);
        } else {
          resetServiceInfo();
        }
      }
    });
  };
  var loadServiceDetails = function loadServiceDetails(providerId, serviceId) {
    var url = $apiProviderID.data('url');
    $.ajax({
      url: url,
      data: {
        provider_id: providerId,
        service_id: serviceId,
        q: '',
        page: 1
      },
      success: function success(response) {
        if (response.data && response.data.length > 0) {
          var service = response.data.find(function (item) {
            return item.service == serviceId;
          });
          if (service) {
            fillServiceInfo(service);
          }
        }
      },
      error: function error() {
        console.log('Không thể load thông tin service');
      }
    });
  };
  $apiProviderID.on('change', function () {
    var providerId = $(this).val();
    resetServiceInfo();
    if (!providerId || providerId === '0') return;
    initializeServiceSelect(providerId);
  });
  var initializeOnLoad = function initializeOnLoad() {
    if (initialProviderId && initialProviderId !== '0') {
      if ($apiProviderID.val() !== initialProviderId) {
        $apiProviderID.val(initialProviderId).trigger('change.select2');
      }
      initializeServiceSelect(initialProviderId, initialServiceId, initialServiceName);
    }
  };
  setTimeout(initializeOnLoad, 100);
});
/******/ })()
;