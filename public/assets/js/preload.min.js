/******/ (() => { // webpackBootstrap
/*!*********************************!*\
  !*** ./resources/js/preload.js ***!
  \*********************************/
function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function _iterableToArrayLimit(r, l) { var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t["return"] && (u = t["return"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }
function _arrayWithHoles(r) { if (Array.isArray(r)) return r; }
function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
// Constants và cấu hình
var REGEX_PATTERNS = {
  FACEBOOK_DOMAINS: /facebook\.com|fb\.watch/,
  INSTAGRAM_DOMAIN: /instagram\.com/,
  TIKTOK_VIDEO: /tiktok\.com\/([^\/]+)\/video\/([0-9]+)/,
  TIKTOK_PROFILE: /tiktok\.com\/@([\w\-._]+)/,
  NUMERIC_ID: /^\d+$/,
  UID_FORMAT: /\w+_\d+/,
  PFBID_FORMAT: /(^[\d_]+$|^pfbid\w+)/
};
var FB_URL_PATTERNS = {
  POSTS_VIDEOS: /(posts|videos)\/([\d\w]+)/,
  FBID: /fbid=([\d\w]+)/,
  PHOTOS: /\/photos\//,
  PERMALINK: /\/permalink\/([0-9]+)/,
  WATCH_LIVE_V: /watch\/live\/\?v=([0-9]+)/,
  WATCH_V: /watch\/\?v=([0-9]+)/,
  WATCH_LIVE_V2: /v=(\d+)/,
  STORIES: /\/stories\//,
  REEL: /reel\/(\d+)/,
  EVENTS: /events\/(\d+)/,
  ID_PARAM: /\?id=(\d+)/,
  MULTI_PERMALINKS: /multi_permalinks=([0-9]+)/,
  SHARE_WATCH: /(fb\.watch|facebook\.com\/share\/)/
};
var INSTAGRAM_PATTERNS = {
  POST: /com\/p\/([a-zA-Z0-9_.-]+)/,
  PROFILE: /com\/([a-zA-Z0-9_.-]+)/
};

// Utility functions
var formatLink = function formatLink(link) {
  link = link.toString().trim();
  if (link.endsWith('#')) link = link.slice(0, -1);
  var httpsIndex = link.indexOf('https:');
  if (httpsIndex > 0) link = link.substring(httpsIndex);
  if (!link.includes('?') || /\?fbid|story_fbid|watch\/|story\.php|multi_permalinks/.test(link)) {
    return link;
  }
  return link.split("?")[0];
};
var extractBetween = function extractBetween(str, start, end) {
  try {
    var parts = str.split(start);
    if (parts.length < 2) return "";
    return parts[1].split(end)[0];
  } catch (_unused) {
    return "";
  }
};
var makeApiCall = /*#__PURE__*/function () {
  var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(url, data) {
    return _regenerator().w(function (_context) {
      while (1) switch (_context.n) {
        case 0:
          return _context.a(2, new Promise(function (resolve) {
            $.ajax({
              type: "POST",
              url: url,
              data: data,
              success: resolve,
              error: function error() {
                return resolve({
                  status: false,
                  msg: 'Lỗi mạng'
                });
              }
            });
          }));
      }
    }, _callee);
  }));
  return function makeApiCall(_x, _x2) {
    return _ref.apply(this, arguments);
  };
}();
var showLoadingDialog = function showLoadingDialog() {
  var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Đang xử lý...';
  return swalTimeOut(message, 'info', 10000, 'Thử lại', 'Huỷ', false);
};

// Main functions
var extractFacebookUid = function extractFacebookUid(url) {
  var patterns = FB_URL_PATTERNS;
  if (url.includes("/posts/") || url.includes("/videos/")) {
    var match = url.match(patterns.POSTS_VIDEOS);
    return match === null || match === void 0 ? void 0 : match[2];
  }
  if (url.includes("fbid=")) {
    var _match = url.match(patterns.FBID);
    return _match === null || _match === void 0 ? void 0 : _match[1];
  }
  if (url.includes("story_fbid=")) {
    return extractBetween(url, "story_fbid=", "&");
  }
  if (url.includes("/photos/")) {
    var parts = url.split("/");
    return parts[parts.length - 2];
  }
  var simplePatterns = [patterns.PERMALINK, patterns.WATCH_LIVE_V, patterns.WATCH_V, patterns.REEL, patterns.EVENTS, patterns.ID_PARAM, patterns.MULTI_PERMALINKS];
  for (var _i = 0, _simplePatterns = simplePatterns; _i < _simplePatterns.length; _i++) {
    var pattern = _simplePatterns[_i];
    var _match2 = url.match(pattern);
    if (_match2) return _match2[1];
  }
  if (url.match(/watch\/live/) && url.match(/v=\d+/)) {
    var _match3 = url.match(patterns.WATCH_LIVE_V2);
    return _match3 === null || _match3 === void 0 ? void 0 : _match3[1];
  }
  if (url.match(patterns.STORIES)) {
    return url.split('?')[0];
  }
  return null;
};
function getPostUid(_x3) {
  return _getPostUid.apply(this, arguments);
}
function _getPostUid() {
  _getPostUid = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5(input) {
    var silent,
      data,
      uid,
      response,
      match,
      replyMatch,
      _args5 = arguments;
    return _regenerator().w(function (_context5) {
      while (1) switch (_context5.n) {
        case 0:
          silent = _args5.length > 1 && _args5[1] !== undefined ? _args5[1] : true;
          data = formatLink(input); // Check if already in correct format
          if (!REGEX_PATTERNS.PFBID_FORMAT.test(data)) {
            _context5.n = 1;
            break;
          }
          return _context5.a(2, [true, data]);
        case 1:
          if (!REGEX_PATTERNS.FACEBOOK_DOMAINS.test(data)) {
            _context5.n = 4;
            break;
          }
          uid = extractFacebookUid(data);

          // Handle special cases that need API call
          if (!(!uid && FB_URL_PATTERNS.SHARE_WATCH.test(data))) {
            _context5.n = 3;
            break;
          }
          if (!silent) {
            showLoadingDialog().then(function (confirm) {
              if (confirm) $('#uid').trigger('change');
            });
          }
          _context5.n = 2;
          return makeApiCall('/facebook/get_link_uid', {
            link: data,
            type: type
          });
        case 2:
          response = _context5.v;
          if (!silent) swalClose();
          return _context5.a(2, response.status ? [true, response.msg] : [false, response.msg]);
        case 3:
          _context5.n = 5;
          break;
        case 4:
          if (REGEX_PATTERNS.INSTAGRAM_DOMAIN.test(data)) {
            match = data.match(INSTAGRAM_PATTERNS.POST) || data.match(INSTAGRAM_PATTERNS.PROFILE);
            uid = match === null || match === void 0 ? void 0 : match[1];
          }
        case 5:
          if (uid) {
            _context5.n = 6;
            break;
          }
          return _context5.a(2, [false, 'Không nhận ra uid']);
        case 6:
          // Handle reply comment if needed
          if ($('#switch_uid_reply').is(':checked')) {
            replyMatch = input.match(/(?:reply_comment_id|comment_id)=([0-9]+)/);
            if (replyMatch) uid += '_' + replyMatch[1];
          }
          return _context5.a(2, [true, uid]);
      }
    }, _callee5);
  }));
  return _getPostUid.apply(this, arguments);
}
function getProfileInfo(_x4) {
  return _getProfileInfo.apply(this, arguments);
}
function _getProfileInfo() {
  _getProfileInfo = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6(input) {
    var silent,
      cleanInput,
      _args6 = arguments;
    return _regenerator().w(function (_context6) {
      while (1) switch (_context6.n) {
        case 0:
          silent = _args6.length > 1 && _args6[1] !== undefined ? _args6[1] : true;
          cleanInput = input.trim();
          if (!(REGEX_PATTERNS.FACEBOOK_DOMAINS.test(cleanInput) || cleanInput.includes('fb.com'))) {
            _context6.n = 2;
            break;
          }
          _context6.n = 1;
          return handleFacebookProfile(cleanInput, silent);
        case 1:
          return _context6.a(2, _context6.v);
        case 2:
          if (!REGEX_PATTERNS.INSTAGRAM_DOMAIN.test(cleanInput)) {
            _context6.n = 3;
            break;
          }
          return _context6.a(2, handleInstagramProfile(cleanInput));
        case 3:
          // Default case - treat as username
          if (!silent) {
            showLoadingDialog().then(function (confirm) {
              if (confirm) $('#link').trigger('change');
            });
          }
          _context6.n = 4;
          return getFbInfoFromUsername(cleanInput);
        case 4:
          return _context6.a(2, _context6.v);
      }
    }, _callee6);
  }));
  return _getProfileInfo.apply(this, arguments);
}
function handleFacebookProfile(_x5, _x6) {
  return _handleFacebookProfile.apply(this, arguments);
}
function _handleFacebookProfile() {
  _handleFacebookProfile = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7(input, silent) {
    var match, groupMatch, username, _match4;
    return _regenerator().w(function (_context7) {
      while (1) switch (_context7.n) {
        case 0:
          if (!(input.includes("profile.php") && !(typeof type !== "undefined" && type === 'review'))) {
            _context7.n = 1;
            break;
          }
          match = input.match(/id=(\d+)/);
          if (!match) {
            _context7.n = 1;
            break;
          }
          return _context7.a(2, [true, {
            id: match[1],
            name: 'Tên khách hàng'
          }]);
        case 1:
          // Handle groups
          groupMatch = input.match(/groups\/(\d+)/);
          if (!groupMatch) {
            _context7.n = 2;
            break;
          }
          return _context7.a(2, [true, {
            id: groupMatch[1],
            name: 'Tên Nhóm'
          }]);
        case 2:
          // Extract username from various URL formats
          username = "";
          if (input.includes("/posts/")) {
            username = extractBetween(input, "facebook.com/", "/posts/");
          } else if (input.includes("/videos/")) {
            username = extractBetween(input, "facebook.com/", "/videos/");
          } else {
            _match4 = input.match(/(facebook|fb)\.com\/(.*)/);
            username = (_match4 === null || _match4 === void 0 ? void 0 : _match4[2]) || "";
          }
          if (username) {
            _context7.n = 3;
            break;
          }
          return _context7.a(2, [false, 'Không nhận ra link']);
        case 3:
          if (!silent) {
            showLoadingDialog().then(function (confirm) {
              if (confirm) $('#link').trigger('change');
            });
          }
          _context7.n = 4;
          return getFbInfoFromUsername(username);
        case 4:
          return _context7.a(2, _context7.v);
      }
    }, _callee7);
  }));
  return _handleFacebookProfile.apply(this, arguments);
}
function handleInstagramProfile(input) {
  if (!input.includes("/p/")) {
    var username = extractBetween(input, "instagram.com/", "/");
    return [true, {
      id: username,
      name: 'Tên khách hàng'
    }];
  }
  return [false, 'Không nhận ra link'];
}
function getCommentLink(_x7) {
  return _getCommentLink.apply(this, arguments);
}
function _getCommentLink() {
  _getCommentLink = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8(input) {
    var _yield$getPostUid5, _yield$getPostUid6, success, postId, commentMatch;
    return _regenerator().w(function (_context8) {
      while (1) switch (_context8.n) {
        case 0:
          if (!REGEX_PATTERNS.UID_FORMAT.test(input)) {
            _context8.n = 1;
            break;
          }
          return _context8.a(2, [true, input]);
        case 1:
          _context8.n = 2;
          return getPostUid(input, false);
        case 2:
          _yield$getPostUid5 = _context8.v;
          _yield$getPostUid6 = _slicedToArray(_yield$getPostUid5, 2);
          success = _yield$getPostUid6[0];
          postId = _yield$getPostUid6[1];
          if (!success) {
            _context8.n = 3;
            break;
          }
          commentMatch = input.match(/(?:reply_comment_id|comment_id)=([0-9]+)/);
          if (!commentMatch) {
            _context8.n = 3;
            break;
          }
          return _context8.a(2, [true, "".concat(postId, "_").concat(commentMatch[1])]);
        case 3:
          return _context8.a(2, [false, 'Không nhận dạng được link!']);
      }
    }, _callee8);
  }));
  return _getCommentLink.apply(this, arguments);
}
function getTiktokLink(_x8) {
  return _getTiktokLink.apply(this, arguments);
}
function _getTiktokLink() {
  _getTiktokLink = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9(link) {
    var silent,
      formattedLink,
      cleanLink,
      httpsIndex,
      response,
      _args9 = arguments;
    return _regenerator().w(function (_context9) {
      while (1) switch (_context9.n) {
        case 0:
          silent = _args9.length > 1 && _args9[1] !== undefined ? _args9[1] : true;
          formattedLink = formatLink(link);
          if (!(REGEX_PATTERNS.TIKTOK_VIDEO.test(formattedLink) || REGEX_PATTERNS.TIKTOK_PROFILE.test(formattedLink))) {
            _context9.n = 1;
            break;
          }
          return _context9.a(2, [true, formattedLink]);
        case 1:
          if (!silent) {
            showLoadingDialog('Đang get link...').then(function (confirm) {
              if (confirm) $('#tiktok_post_link').trigger('change');
            });
          }

          // Clean up link
          cleanLink = link;
          httpsIndex = cleanLink.indexOf('https');
          if (httpsIndex > 0) cleanLink = cleanLink.substring(httpsIndex);
          if (cleanLink.includes(' ')) cleanLink = cleanLink.split(' ')[0];
          _context9.n = 2;
          return makeApiCall('/api/get-tiktok-info', {
            link: cleanLink
          });
        case 2:
          response = _context9.v;
          return _context9.a(2, response.status === 1 ? [true, response.msg] : [false, response.msg]);
      }
    }, _callee9);
  }));
  return _getTiktokLink.apply(this, arguments);
}
function reFormatUrl(input) {
  var cleanInput = input.endsWith('#') ? input.slice(0, -1) : input;
  var httpsIndex = cleanInput.indexOf('https:');
  if (httpsIndex > 0) {
    cleanInput = 'https:' + cleanInput.split('https:')[1];
  }
  if (typeof type !== "undefined" && !/youtube|livestream_v2|view_other|video|live_instagram/.test(type) && cleanInput.includes('?')) {
    return [true, formatLink(cleanInput)];
  }
  return [true, cleanInput];
}
function getFbInfoFromUsername(_x9) {
  return _getFbInfoFromUsername.apply(this, arguments);
} // Event handlers
function _getFbInfoFromUsername() {
  _getFbInfoFromUsername = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee0(username) {
    var response, _t;
    return _regenerator().w(function (_context0) {
      while (1) switch (_context0.n) {
        case 0:
          _context0.p = 0;
          if (!REGEX_PATTERNS.NUMERIC_ID.test(username.toString())) {
            _context0.n = 1;
            break;
          }
          return _context0.a(2, [true, {
            id: username,
            name: 'Tên khách hàng'
          }]);
        case 1:
          _context0.n = 2;
          return makeApiCall('/api/get-uid', {
            username: username,
            need_page_id: $('#need_page_id').is(':checked') ? 1 : ''
          });
        case 2:
          response = _context0.v;
          return _context0.a(2, response.status === 1 ? [true, response.data] : [false, response.msg]);
        case 3:
          _context0.p = 3;
          _t = _context0.v;
          console.warn('getFbInfoFromUsername error:', _t);
          return _context0.a(2, [false, 'Lỗi mạng, vui lòng thử lại']);
      }
    }, _callee0, null, [[0, 3]]);
  }));
  return _getFbInfoFromUsername.apply(this, arguments);
}
$(document).on("change", "#uid", /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {
  var inputText, _yield$getPostUid, _yield$getPostUid2, success, uid, cleanUid;
  return _regenerator().w(function (_context2) {
    while (1) switch (_context2.n) {
      case 0:
        inputText = $(this).val().trim();
        if ($('#url').length) $('#url').val(inputText);
        if (inputText) {
          _context2.n = 1;
          break;
        }
        return _context2.a(2);
      case 1:
        _context2.n = 2;
        return getPostUid(inputText, false);
      case 2:
        _yield$getPostUid = _context2.v;
        _yield$getPostUid2 = _slicedToArray(_yield$getPostUid, 2);
        success = _yield$getPostUid2[0];
        uid = _yield$getPostUid2[1];
        cleanUid = uid === null || uid === void 0 ? void 0 : uid.replace(/#+$/, '');
        if (success && cleanUid) {
          $("#uid").val(cleanUid);
          if (typeof toastr !== "undefined") {
            Base.showSuccess("Cập nhật objectId thành công");
          }
        } else {
          if (typeof toastr !== "undefined") {
            Base.showError(uid || "Không nhận ra uid");
          }
        }
      case 3:
        return _context2.a(2);
    }
  }, _callee2, this);
})));
$(document).on("change", ".post-id", /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {
  var _yield$getPostUid3, _yield$getPostUid4, success, uid;
  return _regenerator().w(function (_context3) {
    while (1) switch (_context3.n) {
      case 0:
        _context3.n = 1;
        return getPostUid($(this).val(), false);
      case 1:
        _yield$getPostUid3 = _context3.v;
        _yield$getPostUid4 = _slicedToArray(_yield$getPostUid3, 2);
        success = _yield$getPostUid4[0];
        uid = _yield$getPostUid4[1];
        if (success) $(this).val(uid);
      case 2:
        return _context3.a(2);
    }
  }, _callee3, this);
})));
$(document).on("change", "#comment_link", /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {
  var _yield$getCommentLink, _yield$getCommentLink2, success, data;
  return _regenerator().w(function (_context4) {
    while (1) switch (_context4.n) {
      case 0:
        _context4.n = 1;
        return getCommentLink($(this).val());
      case 1:
        _yield$getCommentLink = _context4.v;
        _yield$getCommentLink2 = _slicedToArray(_yield$getCommentLink, 2);
        success = _yield$getCommentLink2[0];
        data = _yield$getCommentLink2[1];
        if (success) {
          _context4.n = 2;
          break;
        }
        return _context4.a(2, Base.showError(data));
      case 2:
        $(this).val(data);
        Base.showSuccess('Cập nhật comment ID thành công');
      case 3:
        return _context4.a(2);
    }
  }, _callee4, this);
})));

// Datatables

var datatableLog, ajaxUrl;
function replaceAll(str, find, replace) {
  try {
    return str.replace(new RegExp(find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, "\\$1"), 'g'), replace);
  } catch (ex) {
    return "";
  }
}
function formatMoney(input) {
  var suffix = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';
  var roundNumber = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
  if (!suffix) suffix = '';
  if (suffix == 'd') suffix = '₫';
  if (currency === 'usd') {
    roundNumber = false;
    if (suffix === '₫') suffix = '$';
    if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'USD');
  } else if (currency === 'baht') {
    roundNumber = false;
    if (suffix === '₫') suffix = '฿';
    if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'Baht');
  }
  var number = parseFloat(input);
  if (roundNumber) number = Math.floor(number);
  if (isNaN(number)) return input;
  if (currency === 'usd') number = round(number, 6);
  if (currency === 'baht') number = round(number, 4);

  // If float
  if (number && number.toString().includes('.')) {
    var parts = number.toString().split('.');
    return formatMoney(parts[0]) + '.' + parts[1] + suffix;
  } else {
    return number.toLocaleString() + suffix;
  }
}
var components = {
  "default": function _default(text) {
    return "<span class=\"text-success\">".concat(text, "</span>");
  },
  // Button components
  btn_delete_vip_expired: function btn_delete_vip_expired(full) {
    return "<button data-id=\"".concat(full.id, "\" class=\"btn btn-icon btn-delete-expired\" data-toggle=\"tooltip\" title=\"X\xF3a\">\n              <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_refund_vip: function btn_refund_vip(vip) {
    return "<button class=\"btn btn-icon btn-refund-vip\" data-toggle=\"tooltip\" title=\"Ho\xE0n ti\u1EC1n\"\n               data-id=\"".concat(vip.id, "\" data-type=\"").concat(vip.type, "\" data-server=\"").concat(vip.server, "\">\n                <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_view_log: function btn_view_log(full) {
    return "<button class=\"btn btn-icon btn-view-log\" data-toggle=\"tooltip\" title=\"Xem log\"\n              data-id=\"".concat(full.id, "\">\n                <i class=\"fa-solid fa-chart-line text-primary\"></i>\n            </button>");
  },
  btn_log_priority: function btn_log_priority(row) {
    return "<button class=\"btn btn-icon btn-log-priority\" data-toggle=\"tooltip\" title=\"\u01AFu ti\xEAn\" data-id=\"".concat(row.id, "\">\n                <i class=\"fa fa-angle-up text-primary\"></i>\n            </button>");
  },
  btn_delete_db: function btn_delete_db(full) {
    return "<button data-id=\"".concat(full.id, "\" data-type=\"").concat(full.type, "\" class=\"btn btn-icon btn-delete-db\" data-toggle=\"tooltip\" title=\"X\xF3a DB\">\n                <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_re_check: function btn_re_check(buff) {
    return "<button class=\"btn btn-icon btn-re-check\" data-id=\"".concat(buff.id, "\" data-type=\"").concat(buff.type, "\" data-server=\"").concat(buff.server, "\" data-toggle=\"tooltip\" title=\"Ti\u1EBFp t\u1EE5c ch\u1EA1y\">\n              <i class=\"fas fa-clipboard-check text-primary\"></i>\n            </button>");
  },
  btn_refund_buff: function btn_refund_buff(full) {
    return "<button data-id=\"".concat(full.id, "\" data-type=\"").concat(full.type, "\" class=\"btn btn-icon btn-refund-buff\" data-toggle=\"tooltip\" title=\"Ho\xE0n ti\u1EC1n\">\n                <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_warranty_buff: function btn_warranty_buff(full) {
    return "<button data-id=\"".concat(full.id, "\" class=\"btn btn-icon btn-warranty-buff\" data-toggle=\"tooltip\" title=\"B\u1EA3o H\xE0nh\">\n                <i class=\"fas fa-sync-alt text-primary\"></i>\n            </button>");
  },
  btn_delete_buff: function btn_delete_buff(full) {
    return "<button data-id=\"".concat(full.id, "\" data-type=\"").concat(full.type, "\" class=\"btn btn-icon btn-delete-buff\" title=\"X\xF3a \u0111\u1EC3 mua l\u1EA1i\">\n                <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_edit: function btn_edit(row) {
    var className = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'btn-edit';
    return "<button data-id=\"".concat(row.id, "\" class=\"btn btn-icon ").concat(className, "\" title=\"S\u1EEDa\">\n                <i class=\"fas fa-edit text-success\"></i>\n            </button>");
  },
  btn_delete: function btn_delete(row) {
    var className = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'btn-delete';
    return "<button data-id=\"".concat(row.id, "\" class=\"btn btn-icon ").concat(className, "\" title=\"X\xF3a\">\n                <i class=\"fa fa-trash text-danger\"></i>\n            </button>");
  },
  btn_sync: function btn_sync(id) {
    var className = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'btn-sync';
    var title = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'Đồng bộ';
    return "<button type=\"button\" data-id=\"".concat(id, "\" class=\"btn btn-icon ").concat(className, "\" title=\"").concat(title, "\">\n                <i class=\"text-primary fa fa-repeat\"></i>\n            </button>");
  },
  btn_edit_log: function btn_edit_log(full) {
    return "<button data-id=\"".concat(full.id, "\" data-type=\"").concat(full.type, "\" class=\"btn btn-icon btn-edit-log\" data-toggle=\"tooltip\" title=\"S\u1EEDa\">\n                <i class=\"fas fa-edit text-success\"></i>\n            </button>");
  },
  btn_edit_vip: function btn_edit_vip(vip) {
    return "<button data-id=\"".concat(vip.id, "\" class=\"btn btn-icon btn-edit-vip\" data-toggle=\"tooltip\" title=\"S\u1EEDa\">\n              <i class=\"fas fa-edit text-success\"></i>\n            </button>");
  },
  btn_add_post: function btn_add_post(vip) {
    return "<button data-id=\"".concat(vip.id, "\" class=\"btn btn-icon btn-add-post\" data-toggle=\"tooltip\" title=\"B\xF9 b\xE0i l\u1ED7i\">\n              <i class=\"fas fa-plus-circle text-success\"></i>\n            </button>");
  },
  btn_edit_bot: function btn_edit_bot(id) {
    return "<button class=\"btn btn-icon btn-edit-bot\" data-id=\"".concat(id, "\" data-toggle=\"tooltip\" title=\"S\u1EEDa g\xF3i\">\n              <span class=\"fas fa-edit text-success\"></span>\n            </button>");
  },
  btn_view_log_bot: function btn_view_log_bot(bot) {
    return "<button class=\"btn btn-icon btn-view-bot-log\" data-order_id=\"".concat(bot.order_id, "\" data-toggle=\"tooltip\" title=\"Xem log\">\n              <span class=\"fa fa-eye text-warning\"></span>\n            </button>");
  },
  btn_extend_vip: function btn_extend_vip(vip) {
    return "<button class=\"btn btn-icon btn-extend-vip\" data-toggle=\"tooltip\" title=\"Gia h\u1EA1n\"\n              data-id=\"".concat(vip.id, "\" data-type=\"").concat(vip.type, "\" data-server=\"").concat(vip.server, "\">\n              <i class=\"fas fa-clock text-success\"></i>\n            </button>");
  },
  btn_check_proxy: function btn_check_proxy(proxy) {
    return "<button class=\"btn btn-icon btn-check-proxy\" data-toggle=\"tooltip\" title=\"Check Proxy\" data-order_id=\"".concat(proxy.order_id, "\">\n              <i class=\"fas fa-check-circle text-primary\"></i>\n            </button>");
  },
  // Badge components
  badge_success: function badge_success(text) {
    return "<span class=\"badge badge-success\">".concat(text, "</span>");
  },
  badge_primary: function badge_primary(text) {
    return "<span class=\"badge badge-primary\">".concat(text, "</span>");
  },
  badge_warning: function badge_warning(text) {
    return "<span class=\"badge badge-warning\">".concat(text, "</span>");
  },
  badge_danger: function badge_danger(text) {
    return "<span class=\"badge badge-danger\">".concat(text, "</span>");
  },
  text_copyable: function text_copyable(text) {
    return "<span class=\"copy-on-click text-success\" data-title=\"Sao ch\xE9p\"\n              data-toggle=\"tooltip\" data-content=\"".concat(text, "\">\n              <i class=\"fa-regular fa-clipboard\"></i>\n              ").concat(text, "\n            </span>");
  },
  // Table components
  table: {
    id: function id(data, typeT, full) {
      return "<span class=\"text-success text-bold id-".concat(full.id, "\">").concat(data, "</span>");
    },
    uid: function uid(data, type, full) {
      if (full && ['proxy', 'bank_voucher', 'services'].includes(full.type)) {
        return components.table.text_primary(data);
      }
      if (data === 'NULL') return ' ';
      if (full.link && full.link !== 'default') {
        if (!full.link.match(/^(http)/)) {
          if (!full.link.toString().includes('.')) {
            full.link = 'https://fb.com/' + full.link;
          } else {
            full.link = 'https://' + full.link;
          }
        }
        if (data.length > 30) data = data.substring(0, 28) + '...';
        return "<a href=\"".concat(full.link, "\" class=\"text-bold\" target=\"_blank\">").concat(data, "</a>");
      } else {
        var prefix = 'fb.com';
        if (full.type.match(/instagram/)) prefix = 'instagram.com';
        if (full.type.match(/twitter/)) prefix = 'x.com';
        if (full.type.match(/tiktok/)) {
          prefix = 'www.tiktok.com';
          data = '@' + data;
        }
        return "<a href=\"https://".concat(prefix, "/").concat(data, "\" class=\"text-bold\" target=\"_blank\">").concat(data, "</a>");
      }
    },
    user: function user(data) {
      return "<span class=\"text-warning text-bold\">".concat(data, "</span>");
    },
    server: function server(data, t, log) {
      var mapping;
      if (log.type == 'view_other') {
        mapping = {
          server_1: '600k phút - SV Thường',
          server_2: '600k phút - SV Rẻ',
          server_3: '15k tương tác'
        };
        data = mapping[data] || 'null';
      } else {
        if (data) data = data.replace('server_', 'Server ');
        if (!data) data = '';
      }
      return "<span class=\"text-danger text-bold\">".concat(data, "</span>");
    },
    content: function content(data, typeT, full) {
      return "<div class=\"table-text-plain\">".concat(data, "</div>");
    },
    note: function note(data, typeT, full) {
      return "<div class=\"table-text-plain note-".concat(full.id, "\">").concat(data, "</div>");
    },
    admin_note: function admin_note(data, typeT, full) {
      return "<div class=\"table-text-plain text-danger admin_note-".concat(full.id, "\">").concat(data, "</div>");
    },
    original: function original(data, typeT, full) {
      return "<button class=\"btn btn-brand original-".concat(full.id, "\">").concat(data, "</button>");
    },
    present: function present(data, typeT, full) {
      return "<button class=\"btn btn-success present-".concat(full.id, "\">").concat(data, "</button>");
    },
    price: function price(data, typeT, full) {
      return "<span class=\"kt-badge kt-badge--success kt-badge--inline money-value\">".concat(formatMoney(full.price_current), "</span> \n              <span>").concat(full.math || '+', "</span> \n              <span class=\"kt-badge kt-badge--danger kt-badge--inline money-value\">").concat(formatMoney(full.price || full.commission || 0), "</span> \n              <span>=</span> \n              <span class=\"kt-badge kt-badge--primary kt-badge--inline money-value\">").concat(formatMoney(full.price_left), "</span>");
    },
    count: function count(data, typeT, full) {
      return "<button class=\"btn btn-danger count-".concat(full.id, "\">").concat(data, "</button>");
    },
    "package": function _package(data, typeT, full) {
      return "<button class=\"btn btn-danger package-".concat(full.id, "\">").concat(data, "</button>");
    },
    time: function time(data) {
      if (!data) return ' ';
      return "<button class=\"btn btn-success\">".concat(moment(data).format('HH:mm:ss DD/MM/YYYY'), "</button>");
    },
    time_text: function time_text(data) {
      if (!data) return ' ';
      return "<span class=\"text-success\">".concat(moment(data).format('HH:mm:ss DD/MM/YYYY'), "</span>");
    },
    status: function status(data, typeT, full) {
      return getStatusHtml(full);
    },
    duration: function duration(data) {
      return "<button class=\"btn btn-danger btn-ssm\">".concat(data, "</button>");
    },
    vip_status: function vip_status(data, typeT, full) {
      return getStatusVipHtml(full);
    },
    type: function type(data) {
      return "<button class=\"btn btn-primary\">".concat(data, "</button>");
    },
    comment: function comment(data) {
      if (typeof data == 'string') console.log('data string', data);
      return components.click_to_view(data.content);
    },
    text: function text(data) {
      return "<span class=\"text-bold\">".concat(data, "</span>");
    },
    domain_status: function domain_status(data) {
      var status = domainStatus[data];
      return "<span class=\"badge b-status-".concat(data, "\">").concat(status, "</span>");
    },
    text_success: function text_success(data) {
      if (data == 0 && data.toString().length) data = '0';
      if (!data) data = '';
      return "<span class=\"text-success text-bold\">".concat(data, "</span>");
    },
    text_primary: function text_primary(data) {
      if (data == 0 && data.toString().length) data = '0';
      if (!data) data = '';
      return "<span class=\"text-primary text-bold\">".concat(data, "</span>");
    },
    text_info: function text_info(data) {
      if (data == 0 && data.toString().length) data = '0';
      if (!data) data = '';
      return "<span class=\"text-info text-bold\">".concat(data, "</span>");
    },
    text_warning: function text_warning(data) {
      if (data == 0 && data.toString().length) data = '0';
      if (!data) data = '';
      return "<span class=\"text-warning text-bold\">".concat(data, "</span>");
    },
    text_money: function text_money(data) {
      if (data == 0 && data.toString().length) data = 0;
      return "<span class=\"text-money text-bold\">".concat(formatMoney(data), "</span>\u20AB");
    },
    text_danger: function text_danger(data) {
      if (data == 0 && data.toString().length) data = '0';
      if (!data) data = '';
      return "<span class=\"text-danger text-bold\">".concat(data, "</span>");
    },
    like_count: function like_count(data) {
      return "<span class=\"text-danger text-bold\">".concat(data, "</span>");
    },
    payment_mode: function payment_mode(is_auto, t, full) {
      return "<span class='text-".concat(is_auto ? 'danger' : 'success', " text-bold'>").concat(parseInt(is_auto) ? 'Tự động' + (full.extra || '') : 'Thủ công', "</span>");
    },
    textarea: function textarea(data) {
      return "<textarea class=\"form-control in-table\">".concat(data, "</textarea>");
    },
    changed_money: function changed_money(data) {
      return "<span class=\"text-danger text-bold\">".concat(formatMoney(data, " VNĐ") + (data < 0 ? ' (trừ đi)' : ''), "</span>");
    },
    number: function number(data) {
      return "<span class=\"text-danger text-bold\">".concat(formatMoney(data, ""), "</span>");
    },
    money_vnd: function money_vnd(data) {
      return "<span class=\"text-danger text-bold\">".concat(formatMoney(data, " VNĐ"), "</span>");
    }
  },
  click_to_view: function click_to_view(content) {
    if (!content || content == '{}') return ' ';
    return "<div class=\"click-to-view\">\n              <button class=\"btn btn-click-view\">Xem</button>\n              <textarea class=\"form-control\" style=\"display: none\" rows=\"4\">".concat(content, "</textarea>\n            </div>");
  },
  enable: function enable(_enable) {
    return _enable ? 'Không' : 'Có';
  },
  bool: function bool(value) {
    return components.table.text_primary(value ? 'Có' : 'Không');
  },
  time_expired: function time_expired(data) {
    if (data === null || typeof data == "undefined") {
      return "<button class=\"btn btn-danger text-bold\">H\u1EBFt h\u1EA1n</button>";
    }
    if (isExpired(data)) {
      return "<button class=\"btn btn-danger text-bold\">H\u1EBFt h\u1EA1n (".concat(moment(data).format('DD/MM/YYYY'), ")</button>");
    }
    var daysLeft = Math.ceil(moment(data)['diff'](moment(), 'hours') / 24);
    return "<button class=\"btn btn-primary\">".concat(moment(data).format('DD/MM/YYYY'), "<br />(").concat(daysLeft, " ng\xE0y)</button>");
  }
};
function makeColumn(title, name) {
  var render = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;
  var disableSort = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
  var obj = {
    title: title,
    data: name,
    name: name
  };
  if (disableSort) {
    Object.assign(obj, {
      orderable: false,
      searchable: false
    });
  }
  if (!render) render = name;
  if (render) {
    if (typeof render === 'string') {
      if (typeof components[render] !== "undefined") {
        obj.render = components[render];
      } else if (typeof components.table[render] !== "undefined") {
        obj.render = components.table[render];
      } else if (!obj.render || obj.render === 'random') {
        var values = ['text_success', 'text_primary', 'text_warning', 'text_danger', 'text_info'];
        var randomIndex = Math.floor(Math.random() * values.length);
        obj.render = components.table[values[randomIndex]];
      }
    } else {
      obj.render = render;
    }
  }
  return obj;
}
function readJson(rawData) {
  try {
    if (rawData instanceof Object) return rawData;
    return JSON.parse(rawData) || {};
  } catch (e) {
    return {};
  }
}
function callAjaxPost(url, postData) {
  return new Promise(function (resolve) {
    $.ajax({
      type: "POST",
      url: url,
      data: postData,
      success: function success(data) {
        return resolve(data);
      }
    });
  });
}
function reloadTable() {
  if (datatableVip) datatableVip.ajax.reload(showToolTip, false);
  if (datatableLog) datatableLog.ajax.reload(showToolTip, false);
}
/******/ })()
;