.icon {
    --#{$prefix}icon-size: 1.25rem;
    stroke-width: 1.5;
    font-size: var(--#{$prefix}icon-size);
    height: var(--#{$prefix}icon-size);
    vertical-align: bottom;
    width: var(--#{$prefix}icon-size)
}

.web-logo-text {
    font-size: 28px;
    font-weight: 600;
    color: var(--#{$prefix}primary);

}

.icon-md {
    --#{$prefix}icon-size: 1.5rem;
}


.icon-lg {
    --#{$prefix}icon-size: 1.75rem;
}

.form-group {
    margin-bottom: 1rem;
}

small {
    font-weight: 500;
}

.bg-pink {
    --vz-bg-opacity: 1;
    background-color: rgba(var(--vz-pink-rgb), var(--vz-bg-opacity)) !important;
}

.text-plain {
    color: green;
    font-weight: 500;
}

.badge-sm {
    padding: .75rem;
    font-size: .7rem !important;
    color: #fff;
    height: 0;
    width: auto;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    margin: 0 2px;
}

.form-fieldset {
    background: var(--#{$prefix}body-bg);
    border: var(--#{$prefix}border-width) var(--#{$prefix}border-style) var(--#{$prefix}border-color);
    border-radius: var(--#{$prefix}border-radius);
    margin-bottom: 1rem;
    padding: 1rem
}

fieldset:empty {
    display: none
}

.dropdown-item {
    font-weight: 500;
}

.package-price {
    padding: 2px 4px;
    color: red;
    font-weight: 600;
}

.package-status {
    padding: 2px 4px;
    background-color: green;
    color: white;
    font-weight: 600;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
}

.package-info {
    margin-left: 10px;
    padding: 8px 14px;
    border-radius: 5px;
    margin-bottom: 15px;
    transition: all .3s;
    border: 1px solid #C2F7F4;
    background-color: #C2F7F4;
    color: #000;
    font-weight: 500;
}

.package-id {
    color: orangered;
}

.radio-package.stopped *,
.radio-package.paused * {
    cursor: not-allowed;
    pointer-events: none;
}

.package-status.stopped,
.radio-package.stopped .package-status,
.radio-package.paused .package-status {
    background-color: orangered;
}

.radio-package.slow .package-status {
    background-color: #FFCC00;
}

.fb-reaction {
    min-height: 90px;
    text-align: center;

    .checkbox {
        display: inline-block;
        margin-left: 1rem;
    }

    img {
        width: 50px;
        display: block;
        transition: all .3s;
        cursor: pointer;
        border-bottom: 6px solid transparent;
    }
}

.widget-top {
    display: flex;
    align-items: flex-start;

    &__avatar {
        img {
            width: 110px;
            border-radius: 50%;
        }
    }

    &__content {
        width: 100%;
        padding-left: 1.25rem;
    }
}

.widget-bottom {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 1.5rem;
    border-top: 1px solid var(--#{$prefix}border-color);

    &__item {
        display: flex;
        align-items: center;
        flex-grow: 1;
        padding: 1.5rem 1rem 0 0;
    }

    &__content {
        padding-left: 1rem;
    }

    &__balance {
        font-size: 1.1rem;
        font-weight: 500;
        color: var(--#{$prefix}dark);
    }
}

.tab-filter {
    li{
        .count{
            background-color: whitesmoke;
            padding: 2px 7px;
            margin-left: 3px;
            border-radius: 10px;
        }
    }
}
.card-faq-question{
    padding: .5rem;
    border: 1px solid var(--#{$prefix}border-color);
    color: var(--#{$prefix}dark);
    border-radius: 4px;
}
.list-notification{
    max-height: 180px;
    overflow: auto;
    border-bottom: 1px solid #ddd;
}
.notify-item{
    &__time{
        border-right: 1px solid #ddd;
        padding-right: 10px;
        width: 50px;
        color: brown;
        padding-bottom: 10px;
        font-size: 13px;
        font-weight: 500;
    }
    &__content{
        padding-left: 10px;
        padding-bottom: 10px;
        font-weight: 500;
        word-break: break-word;
        font-size: 13px;
    }
}
.post-icon{
    width: 50px;
    border-radius: 5px;
    margin-right: 8px;
}
.post-time{
    font-size: 13px;
    font-weight: 600;
}
.post-name{
    font-size: 15px;
    font-weight: 600;
    padding-bottom: 10px;
    color: cadetblue;
}

.post-content{
    margin-top: 10px;
    margin-bottom: 25px;
    font-weight: 500;
}
.post-image{
    text-align: center;
    cursor: pointer;
    img{
        width: 400px;
        max-width: 100%;
        height: auto;
    }
}
