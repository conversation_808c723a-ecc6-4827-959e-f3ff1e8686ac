<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-layout="vertical" data-sidebar-size="lg" data-theme="default" data-theme-colors="default" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') | {{ config('app.name', 'Laravel') }}</title>
    @stack('css')
    {!! \Assets::renderHeader() !!}
    @stack('style')
</head>
<body>
<div id="app">
    @include('clients.partials.header')
    @include('clients.partials.sidebar')
    <div class="main-content">
        <div class="page-content">
            <div class="container-fluid">
                {{ \Diglactic\Breadcrumbs\Breadcrumbs::render() }}
                <div class="main">
                    @yield('content')
                </div>
            </div>
        </div>
    </div>
</div>
@stack('footer')
{!! \Assets::renderFooter() !!}
@include('partials.toast')
@stack('js')
</body>
</html>
