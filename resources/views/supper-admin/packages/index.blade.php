@extends('admin.layouts.app')

@section('title', '<PERSON>ói dịch vụ')

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            Danh sách danh mục
        </h4>
        <div class="">
            <a href="{{ route('supper-admin.packages.create') }}" class="btn btn-success">
                <i class="ri-add-line"></i>
                Thêm mới
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Danh mục</th>
                        <th>Tên gói</th>
                        <th>Thành viên</th>
                        <th>CTV</th>
                        <th><PERSON><PERSON><PERSON> lý</th>
                        <th><PERSON><PERSON><PERSON> phân phối</th>
                        <th>Trạng thái</th>
                        <th><PERSON><PERSON><PERSON> tạ<PERSON></th>
                        <th>Thao tác</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.packages.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'category', name: 'category.name'},
                    {data: 'name', name: 'name'},
                    {data: 'member_price', name: 'member_price'},
                    {data: 'collaborator_price', name: 'collaborator_price'},
                    {data: 'agency_price', name: 'agency_price'},
                    {data: 'distributor_price', name: 'distributor_price'},
                    {data: 'status', name: 'status'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'action', name:'action',searchable: false, orderable: false},
                ],
            });
        });
    </script>
@endpush
