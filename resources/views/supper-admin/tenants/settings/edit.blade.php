@extends('admin.layouts.app')
@section('title', 'Cài đặt trang web')
@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            Cài đặt cho <span class="text-orange fw-semibold">{{ $tenant->domain }}</span>
        </h4>
    </div>

    <form action="{{ route('supper-admin.tenants.settings.update', $tenant->id) }}" method="POST" class="form-action-request">
        @csrf
        @method('PUT')
        <div class="accordion custom-accordion-border accordion-border-box accordion-secondary accordion-success" id="setting-tenant">
            <div class="accordion-item material-shadow">
                <h2 class="accordion-header" id="setting-tenant-general">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#setting-tenant-general-theme" aria-expanded="true" aria-controls="setting-tenant-general-theme">
                        Giao <PERSON> trang web
                    </button>
                </h2>
                <div id="setting-tenant-general-theme" class="accordion-collapse collapse show" aria-labelledby="setting-tenant-general" data-bs-parent="#setting-tenant">
                    <div class="accordion-body">
                        <div class="row">

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="logo-text" class="form-label">Logo văn bản</label>
                                    <input type="text" class="form-control" id="logo-text" name="settings[logo_text]" value="{{ $settings['logo_text'] ?? '' }}">
                                    <small>Chỉ sử dụng 1 trong 2 loại logo</small>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="logo-url" class="form-label">Logo URL</label>
                                    <input type="text" class="form-control" id="logo-url" name="settings[logo_image]" value="{{ $settings['logo_image'] ?? '' }}">
                                    <small>Chỉ sử dụng 1 trong 2 loại logo. Lấy đường dẫn hình ảnh <a href="">Tại đây</a> </small>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="logo-favicon" class="form-label">Favicon</label>
                                    <input type="text" class="form-control" id="logo-favicon" name="settings[logo_favicon]" value="{{ $settings['logo_favicon'] ?? '' }}">
                                    <small>Lấy đường dẫn ảnh <a href="">Tại đây</a></small>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label>Landing Page</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check card-radio">
                                            <input id="landingpage-01" name="settings[data_theme]" type="radio" value="default" class="form-check-input"
                                                {{ ($settings['data_theme'] ?? '') === 'default' ? 'checked' : '' }}>
                                            <label class="form-check-label p-0" for="landingpage-01">
                                                <img src="https://themesbrand.com/velzon/assets/images/demo/default.png" alt="default" loading="lazy" class="img-fluid">
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check card-radio">
                                            <input id="landingpage-02" name="settings[data_theme]" type="radio" value="corporate" class="form-check-input"
                                                {{ ($settings['data_theme'] ?? '') === 'corporate' ? 'checked' : '' }}>
                                            <label class="form-check-label p-0" for="landingpage-02">
                                                <img src="https://themesbrand.com/velzon/assets/images/demo/corporate.png" alt="corporate" loading="lazy" class="img-fluid">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item mt-2 material-shadow">
                <h2 class="accordion-header" id="setting-tenant-seo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#setting-tenant-seo-detail" aria-expanded="false" aria-controls="setting-tenant-seo-detail">
                        Cài đặt SEO
                    </button>
                </h2>
                <div id="setting-tenant-seo-detail" class="accordion-collapse collapse" aria-labelledby="setting-tenant-seo" data-bs-parent="#setting-tenant">
                    <div class="accordion-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="meta_title" class="form-label">Meta Title</label>
                                    <input type="text" class="form-control" id="meta_title" name="settings[meta_title]" value="{{ $settings['meta_title'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea class="form-control" id="meta_description" name="settings[meta_description]" rows="3">{{ $settings['meta_description'] ?? '' }}</textarea>
                                    <small>Lấy đường dẫn ảnh <a href="">Tại đây</a></small>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                    <input type="text" class="form-control" id="meta_keywords" name="settings[meta_keywords]" value="{{ $settings['meta_keywords'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="meta_image" class="form-label">Meta Images</label>
                                    <input type="text" class="form-control" id="meta_image" name="settings[meta_image]" value="{{ $settings['meta_image'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="og_title" class="form-label">Meta Og:title</label>
                                    <input type="text" class="form-control" id="og_title" name="settings[og_title]" value="{{ $settings['og_title'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="meta_description" class="form-label">Meta Description</label>
                                    <textarea class="form-control" id="meta_description" name="settings[meta_description]" rows="3">{{ $settings['meta_description'] ?? '' }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="og_image" class="form-label">Meta Og:image</label>
                                    <input type="text" class="form-control" id="og_image" name="settings[og_image]" value="{{ $settings['og_image'] ?? '' }}">
                                    <small>Lấy đường dẫn ảnh <a href="">Tại đây</a></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item mt-2 material-shadow">
                <h2 class="accordion-header" id="setting-tenant-contact">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#setting-tenant-contact-detail" aria-expanded="false" aria-controls="setting-tenant-contact-detail">
                        Thông tin liên hệ
                    </button>
                </h2>
                <div id="setting-tenant-contact-detail" class="accordion-collapse collapse" aria-labelledby="setting-tenant-contact" data-bs-parent="#setting-tenant">
                    <div class="accordion-body">
                        <p><span class="text-danger">Lưu ý: </span>Thông tin liên hệ sẽ được hiển thị ở thanh menu ở trang người dùng</p>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">Email liên hệ</label>
                                <input type="email" class="form-control" id="contact_email" name="settings[contact_email]" value="{{ $settings['contact_email'] ?? '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label">Số điện thoại/Hotline</label>
                                <input type="text" class="form-control" id="contact_phone" name="settings[contact_phone]" value="{{ $settings['contact_phone'] ?? '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_zalo" class="form-label">Zalo</label>
                                <input type="text" class="form-control" id="contact_zalo" name="settings[contact_zalo]" value="{{ $settings['contact_zalo'] ?? '' }}">
                                <small>VD đường dẫn là https://zalo.com/12345 thì điền <code>12345</code></small>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_telegram" class="form-label">Telegram(nếu có)</label>
                                <input type="text" class="form-control" id="contact_telegram" name="settings[contact_telegram]" value="{{ $settings['contact_telegram'] ?? '' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact_facebook" class="form-label">Fanpage Facebook</label>
                                <input type="text" class="form-control" id="contact_facebook" name="settings[contact_facebook]" value="{{ $settings['contact_facebook'] ?? '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item mt-2 material-shadow">
                <h2 class="accordion-header" id="setting-tenant-auto-bank-acb">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#setting-tenant-auto-bank-acb-detail" aria-expanded="false" aria-controls="setting-tenant-auto-bank-acb-detail">
                        Auto ngân hàng ACB
                    </button>
                </h2>
                <div id="setting-tenant-auto-bank-acb-detail" class="accordion-collapse collapse" aria-labelledby="setting-tenant-auto-bank-acb" data-bs-parent="#setting-tenant">
                    <div class="accordion-body">
                        <p><span class="text-danger">Lưu ý: </span>Dịch vụ được hỗ trợ qua Pay2s. Sau khi cài đặt đủ thông tin xong vui lòng liên hệ Admin để xử lý tiếp</p>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" role="switch" id="acb_public" name="settings[acb_public]" value="1" {{ !empty($settings['acb_public']) ? 'checked' : '' }}>
                                <label class="form-check-label" for="acb_public">Sử dụng ngân hàng ACB</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="acb_account_name" class="form-label">Họ và tên</label>
                                <input type="text" class="form-control" id="acb_account_name" name="settings[acb_account_name]" value="{{ $settings['acb_account_name'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="acb_account_number" class="form-label">Số tài khoản</label>
                                <input type="text" class="form-control" id="acb_account_number" name="settings[acb_account_number]" value="{{ $settings['acb_account_number'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="acb_account_phone" class="form-label">Số điện thoại</label>
                                <input type="text" class="form-control" id="acb_account_phone" name="settings[acb_account_phone]" value="{{ $settings['acb_account_phone'] ?? '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="accordion-item mt-2 material-shadow">
                <h2 class="accordion-header" id="setting-tenant-auto-bank-vietcombank">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#setting-tenant-auto-bank-vietcombank-detail" aria-expanded="false" aria-controls="setting-tenant-auto-bank-vietcombank-detail">
                        Auto ngân hàng Vietcombank
                    </button>
                </h2>
                <div id="setting-tenant-auto-bank-vietcombank-detail" class="accordion-collapse collapse" aria-labelledby="setting-tenant-auto-bank-vietcombank" data-bs-parent="#setting-tenant">
                    <div class="accordion-body">
                        <p><span class="text-danger">Lưu ý: </span>Dịch vụ được hỗ trợ qua Pay2s. Sau khi cài đặt đủ thông tin xong vui lòng liên hệ Admin để xử lý tiếp</p>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" role="switch" id="vietcombank_public" name="settings[vietcombank_public]" value="1" {{ !empty($settings['vietcombank_public']) ? 'checked' : '' }}>
                                <label class="form-check-label" for="vietcombank_public">Sử dụng ngân hàng ACB</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vietcombank_account_username" class="form-label">Tên đăng nhập</label>
                                <input type="text" class="form-control" id="vietcombank_account_username" name="settings[vietcombank_account_username]" value="{{ $settings['vietcombank_account_username'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vietcombank_account_password" class="form-label">Mật khẩu tài khoản</label>
                                <input type="text" class="form-control" id="vietcombank_account_password" name="settings[vietcombank_account_password]" value="{{ $settings['vietcombank_account_password'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="vietcombank_account_number" class="form-label">Số tài khoản</label>
                                <input type="text" class="form-control" id="vietcombank_account_number" name="settings[vietcombank_account_number]" value="{{ $settings['vietcombank_account_number'] ?? '' }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-3">
            <button type="submit" class="btn btn-primary">
                <i class="ri-save-line"></i> Lưu cài đặt
            </button>
        </div>
    </form>
@endsection
