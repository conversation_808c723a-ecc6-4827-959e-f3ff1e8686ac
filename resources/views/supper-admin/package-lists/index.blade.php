@extends('admin.layouts.app')

@section('title', 'Danh sách gói ')

@push('css')
    @include('admin.partials.datatables.style')
@endpush

@section('content')
    <div class="d-flex align-items-center justify-content-between mb-3">
        <h4 class="fw-medium fs-16">
            <PERSON>h sách nền tảng
        </h4>
        <div class="">
            <a href="{{ route('supper-admin.package-lists.create') }}" class="btn btn-success">
                <i class="ri-add-line"></i>
                Thêm mới
            </a>
        </div>
    </div>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered datatables">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tên</th>
                        <th>Key</th>
                        <th>Trạng thái</th>
                        <th><PERSON><PERSON><PERSON> t<PERSON></th>
                        <th><PERSON><PERSON> t<PERSON></th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
@push('js')
    @include('admin.partials.datatables.script')
    <script>
        $(function () {
            $('.datatables').DataTable({
                processing: true,
                serverSide: true,
                ajax: '{!! route('supper-admin.package-lists.index') !!}',
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'name', name: 'name'},
                    {data: 'key', name: 'key'},
                    {data: 'status', name: 'status'},
                    {data: 'created_at', name: 'created_at'},
                    {data: 'action', name:'action'}
                ],
            });
        });
    </script>
@endpush
