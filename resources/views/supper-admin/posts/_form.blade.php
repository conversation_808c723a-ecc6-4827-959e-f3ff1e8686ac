{!! csrf_field() !!}
<div class="card">
    <div class="card-header">
        <h4 class="card-title">{{ $form_title }} bài viết</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Tiêu đ<PERSON></label>
                    <input type="text" class="form-control" name="title" value="{{ old('title', $dataEdit->title ?? '') }}" required>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Website</label>
                    <select name="tenant_id" class="form-select" required>
                        <option value="">Chọn website</option>
                        @foreach($tenants as $tenant)
                            <option value="{{ $tenant->id }}" {{ old('tenant_id', $dataEdit->tenant_id ?? '') == $tenant->id ? 'selected' : '' }}>
                                {{ $tenant->domain }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Trạng thái</label>
                    <select name="status" class="form-select">
                        @foreach(App\Enums\BaseStatusEnum::cases() as $status)
                            <option value="{{ $status->value }}"
                                @selected(old('status', $dataEdit->status->value ?? null) == $status->value)>
                                {{ $status->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Loại thông báo</label>
                    <select name="type" class="form-select">
                        @foreach(App\Enums\PostTypeEnum::cases() as $type)
                            <option value="{{ $type->value }}"
                                @selected(old('type', $dataEdit->type->value ?? null) == $type->value)>
                                {{ $type->label() }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label required">Ghim</label>
                    <div class="form-check form-switch mb-2">
                        <input type="hidden" name="pin" value="0">
                        <input type="checkbox" class="form-check-input" name="pin" value="1" {{ old('pin', $dataEdit->pin ?? null) ? 'checked' : '' }}>
                        <label class="form-check-label" for="pin">Ghim bài viết</label>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label">Ảnh đại diện</label>
                    <input type="text" class="form-control" name="image">

                </div>
            </div>
            <div class="col-md-12">
                <div class="form-group">
                    <label class="form-label required">Nội dung</label>
                    <textarea class="form-control" name="content" id="content" rows="10" required>{{ old('content', $dataEdit->content ?? '') }}</textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer text-end">
        <a href="{{ route('supper-admin.posts.index') }}" class="btn btn-link link-secondary">
            Hủy
        </a>
        <button type="submit" class="btn btn-primary ms-auto">
            {{ $form_title }}
        </button>
    </div>
</div>
