import moment from "moment";
if ($.fn.dataTable)
    $.extend($.fn.dataTable.defaults, {
        fnDrawCallback: function () {
            showToolTip();
        },
        "language": {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "emptyTable": "Không tìm thấy dòng nào phù hợp",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "<PERSON>ang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sInfoPostFix": "",
            "sSearch": "Tìm:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "<PERSON><PERSON><PERSON>",
                "sPrevious": "Tr<PERSON>ớ<PERSON>",
                "sNext": "Tiếp",
                "sLast": "Cuối"
            }
        }
    });
const components = {
    default: function (text) {
        return `<span class="text-success">${text}</span>`;
    },
    btn_edit: function (row, className = 'btn-edit') {
        return `<a href="javascript:void(0)" data-id="${row.id}" class="me-2 ${className}" title="Sửa">
                <i class="ri-edit-line text-primary"></i>
            </a>`
    },
    btn_delete: function (row, className = 'btn-delete') {
        return `<a href="javascript:void(0)" data-id="${row.id}" class="me-2 ${className}" title="Xóa">
                <i class="ri-delete-bin-2-line text-danger"></i>
            </a>`
    },
    badge_success: (text) => `<span class="badge badge-success">${text}</span>`,
    badge_primary: (text) => `<span class="badge badge-primary">${text}</span>`,
    badge_warning: (text) => `<span class="badge badge-warning">${text}</span>`,
    badge_danger: (text) => `<span class="badge badge-danger">${text}</span>`,

    text_copyable: function (text) {
        return `<span class="copy-on-click text-success" data-title="Sao chép"
              data-toggle="tooltip" data-content="${text}">
              <i class="ri-file-copy-line"></i>
              ${text}
            </span>`;
    },

    table: {
        id: function (data, typeT, full) {
            return `<span class="text-success text-bold id-${full.id}">${data}</span>`;
        },
        title: function (data, typeT, full) {
            return `<span class="text-plain">${data}</span>`;
        },
        name: function (data, typeT, full) {
            return `<span class="text-plain">${data}</span>`;
        },
        content: function (data, typeT, full) {
            return `<span class="text-plain">${data}</span>`;
        },
        key: function (data, typeT, full) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        text_success: function (data) {
            return `<span class="text-success">${data}</span>`;
        },
        text_primary: function (data) {
            return `<span class="text-primary">${data}</span>`;
        },
        text_warning: function (data) {
            return `<span class="text-warning">${data}</span>`;
        },
        text_danger: function (data) {
            return `<span class="text-danger">${data}</span>`;
        },
        text_info: function (data) {
            return `<span class="text-info">${data}</span>`;
        },
        time: function (data) {
            if (!data) return ' ';
            return `<span class="badge bg-success">${moment(data).format('HH:mm:ss DD/MM/YYYY')}</span>`;
        },
         action: function (render) {
            return makeColumn('Hành Động', 'id', render, true);
        },
        status: function (data, typeT, full) {
            return getStatusHtml(full);
        },
    }
}
function getStatusHtml(row) {
    const status = row.status;
    const statusMap = {
        published: { class: 'bg-success', text: 'Đã xuất bản' },
        draft: { class: 'bg-secondary', text: 'Bản nháp' },
        pending: { class: 'bg-warning', text: 'Chờ duyệt' },
    };

    const statusConfig = statusMap[status] || { class: 'badge-info', text: status };
    return `<span class="badge ${statusConfig.class}">${statusConfig.text}</span>`;
}

export const makeColumn = (title, name, render = null, disableSort = false) => {
    var obj = {
        title: title,
        data: name,
        name: name
    };

    if (disableSort) Object.assign(obj, { orderable: false, searchable: false });
    if (!render) render = name;
    if (render) {
        if (typeof render === 'string') {
            if (typeof components[render] !== "undefined") obj.render = components[render];
            if (!obj.render && typeof components.table[render] !== "undefined") obj.render = components.table[render];
            if (!obj.render || obj.render == 'random') {
                var values = ['text_success', 'text_primary', 'text_warning', 'text_danger', 'text_info'];
                obj.render = components.table[values[Math.floor(Math.random() * values.length)]];
            }
        } else obj.render = render;
    }
    return obj;
}

/**
 * Re mapping the request url of datatable
 * @param url
 * @param callbackData
 */

var ajaxUrl,
    datatableVip,
    datatableLog;

export const xAjax = (url, callbackData = null) => {
    if (!ajaxUrl) ajaxUrl = url;

    return {
        url: url,
        data: function (data) {
            try {
                if (data.order && data.order.length > 0) {
                    let order = data.order[0];
                    order.field = data.columns[order.column].data;
                    data.order_by = order.field;
                    data.order_dir = order.dir;
                }

                data.keyword = data.search?.value || '';
                delete data.order;
                delete data.columns;

                if (typeof callbackData === "function") {
                    callbackData(data);
                }

                return data;
            } catch (e) {
                console.error('xAjax data processing error:', e);
                return data;
            }
        }
    }
}
export const showToolTip = () => {
    $('[data-toggle="tooltip"], .btn-icon').tooltip();
}

export const reloadTable = () => {
    if (datatableVip) datatableVip.ajax.reload(showToolTip, false);
    if (datatableLog) datatableLog.ajax.reload(showToolTip, false);
}
export const definedColumns = {
    stt: makeColumn('STT', 'id'),
    title: makeColumn('Tiêu đề', 'title'),
    name: makeColumn('Tên', 'name'),
    key: makeColumn('Tên', 'key'),
    content: makeColumn('Nội dung', 'content'),
    created_at: makeColumn('Thời gian', 'created_at', 'time'),
    action: makeColumn('Hành Động', 'id', 'action'),
    status: makeColumn('Trạng thái', 'status', 'status'),
}
