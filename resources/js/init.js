import Toastify from "./base/toastify";
import Swal from "sweetalert2";

class Base {
    static noticesTimeout = {}
    static noticesTimeoutCount = 500

    constructor() {
        Base.handleUIFormRequest()
        Base.handleDeleteRequest()
        Base.handleSimpleBar()
        Base.handleActiveMenuSidebar()
    }

    /**
     * @param {HTMLElement} element
     * @param {object} options
     * @param {boolean} force
     */
    static select(element, options = {}, force = false) {
        const $element = $(element)

        if (!jQuery().select2 || ($element.hasClass('select2-hidden-accessible') && !force)) {
            return
        }

        options = {
            width: '100%',
            placeholder: $element.data('placeholder') || null,
            allowClear: $element.data('allow-clear') || false,
            ...options,
        }

        let parent = $element.closest('div[data-select2-dropdown-parent]') || $element.closest('.modal-content') || $element.closest('.modal')

        if (parent.length) {
            options.dropdownParent = parent
            options.width = '100%'
        }

        $element.select2(options)
    }

    static showNotice(messageType, message, messageHeader = '') {
        let key = `notices_msg.${messageType}.${message}`
        let color = ''
        let icon = ''

        if (Base.noticesTimeout[key]) {
            clearTimeout(Base.noticesTimeout[key])
        }

        Base.noticesTimeout[key] = setTimeout(() => {
            if (!messageHeader) {
                switch (messageType) {
                    case 'error':
                        messageHeader = BaseVariables.languages.notices_msg.error
                        break
                    case 'success':
                        messageHeader = BaseVariables.languages.notices_msg.success
                        break
                }
            }

            switch (messageType) {
                case 'error':
                    color = '#f44336'
                    icon =
                        '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0" /><path d="M12 9v4" /><path d="M12 16v.01" /></svg>'
                    break
                case 'success':
                    color = '#4caf50'
                    icon =
                        '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10" /></svg>'
                    break
            }

            Toastify({
                text: message,
                duration: 5000,
                close: false,
                gravity: 'top',
                position: 'right',
                stopOnFocus: true,
                escapeMarkup: false,
                icon: icon,
                style: {
                    background: color,
                },
            }).showToast()
        }, Base.noticesTimeoutCount)
    }

    static showError(message, messageHeader = '') {
        this.showNotice('error', message, messageHeader)
    }

    static showSuccess(message, messageHeader = '') {
        this.showNotice('success', message, messageHeader)
    }

    static handleError(data) {
        if (typeof data.errors !== 'undefined' && !_.isArray(data.errors)) {
            Base.handleValidationError(data.errors)
        } else {
            if (typeof data.responseJSON !== 'undefined') {
                if (typeof data.responseJSON.errors !== 'undefined') {
                    if (data.status === 422) {
                        Base.handleValidationError(data.responseJSON.errors)
                    }
                } else if (typeof data.responseJSON.message !== 'undefined') {
                    Base.showError(data.responseJSON.message)
                } else {
                    $.each(data.responseJSON, (index, el) => {
                        $.each(el, (key, item) => {
                            Base.showError(item)
                        })
                    })
                }
            } else {
                Base.showError(data.statusText)
            }
        }
    }

    static handleValidationError(errors) {
        let message = ''
        $.each(errors, (index, item) => {
            message += item + '\n'
        })
        Base.showError(message)
    }

    static handleUIFormRequest() {
        $('.form-action-request').submit(function () {
            $(this).find("button[type='submit']").prop("disabled", true);
            $(this).find("button[type='submit']").html(
                "<span class='spinner-border spinner-border-sm me-2' role='status' aria-hidden='true'></span> Đang xử lý..."
            );
        })
    }

    static handleDeleteRequest() {
        $(document).on('click', '.btn-delete', function (e) {
            e.preventDefault();

            let button = $(this);
            let id = button.data('id');
            let url = button.data('url');
            Swal.fire({
                title: 'Bạn có chắc muốn xóa?',
                text: "Hành động này không thể hoàn tác!",
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xác nhận',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: {
                            _method: 'DELETE',
                        },
                        success: function (response) {
                            Base.showSuccess('Xóa dữ liệu thành công');

                            $('.datatables').DataTable().ajax.reload(null, false);
                        },
                        error: function (xhr) {
                            Swal.fire(
                                'Lỗi!',
                                'Không thể xóa dữ liệu.',
                                'error'
                            );
                        }
                    });
                }
            });
        })
    }

    static handleSimpleBar() {
        const navbarMenu = $(".navbar-menu")
        const navbarMenuHTML = navbarMenu.html();

        $("#two-column-menu").empty();
        navbarMenu.html(navbarMenuHTML);
        $("#scrollbar").attr("data-simplebar", '');
        $("#navbar-nav").attr("data-simplebar", '');
        $("#scrollbar").addClass("h-100");
    }

    static handleActiveMenuSidebar() {
        const mPath = window.location.pathname.replace(/\/$/, '') || '/';
        const $scrollbar = $('#scrollbar');

        // Tìm tất cả links và sắp xếp theo độ dài href giảm dần
        const $allLinks = $scrollbar.find('a.menu-link, a.nav-link');
        const links = $allLinks.toArray().sort((a, b) => {
            const hrefA = $(a).attr('href') || '';
            const hrefB = $(b).attr('href') || '';
            return hrefB.length - hrefA.length;
        });

        for (const link of links) {
            const $link = $(link);
            const href = $link.attr('href');
            if (!href) continue;

            const linkPath = href.replace(/\/$/, '') || '/';

            if (mPath === linkPath || (linkPath !== '/' && mPath.startsWith(linkPath + '/'))) {
                $link.addClass('active');

                $link.parents('.collapse').each(function () {
                    $(this).addClass('show')
                        .prev('a.nav-link')
                        .addClass('active')
                        .attr('aria-expanded', 'true');
                });
                break;
            }
        }
    }
}

$(() => {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    })

    new Base()
    window.Base = Base
})
