"use strict";
import { definedColumns, xAjax, reloadTable } from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.name,
    definedColumns.key,
    definedColumns.status,
    definedColumns.created_at,
    definedColumns.action
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/platforms/ajax/data`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
