"use strict";
import { definedColumns, xAjax, reloadTable } from "../table";
$(document).ready(function() {
  var columns = [
    definedColumns.stt,
    definedColumns.title,
  ];

  reloadTable.datatableLog = $('#datatable-ajax').DataTable({
    responsive: false,
    searchDelay: 500,
    processing: true,
    serverSide: true,
    ajax: xAjax(`/supper-admin/categories/ajax`),
    order: [[ 0, "desc" ]],
    columns: columns
  });
});
