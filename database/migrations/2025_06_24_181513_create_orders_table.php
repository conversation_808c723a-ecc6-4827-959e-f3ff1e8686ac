<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('category_id')->constrained('categories')->onDelete('cascade');
            $table->foreignId('package_id')->constrained('packages')->onDelete('cascade');
            $table->foreignId('tenant_id')->constrained('tenants')->onDelete('cascade');
            $table->integer('source_id');
            $table->string('source_name');
            $table->string('source_type');
            $table->decimal('source_cost', 12, 2);
            $table->json('source_resp')->nullable();
            $table->boolean('source_place')->default(false);
            $table->string('source_status')->default('Pending');
            $table->string('order_code');
            $table->string('url');
            $table->string('uid')->nullable();
            $table->integer('count')->unsigned();
            $table->integer('start_number')->default(0);
            $table->integer('success_count')->default(0);
            $table->integer('runs')->nullable();
            $table->integer('interval')->nullable();
            $table->decimal('price_per', 12, 2);
            $table->decimal('total_payment', 12, 2);
            $table->string('currency_code')->default('USD');
            $table->string('order_note')->nullable();
            $table->string('order_status');
            $table->json('order_actions')->nullable();
            $table->string('extra_note')->nullable();
            $table->json('extra_data')->nullable();
            $table->string('notes')->nullable();
            $table->string('admin_note')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();

            $table->index('user_id');
            $table->index('order_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
