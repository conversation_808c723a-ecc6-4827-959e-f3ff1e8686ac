<?php

namespace Database\Seeders;

use App\Enums\BaseStatusEnum;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SocialMediaMarketingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $platforms = [
            [
                'name' => 'Facebook',
                'description' => 'Mạng xã hội phổ biến để kết nối và chia sẻ nội dung.',
                'status' => 'published',
                'key' => 'facebook',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Instagram',
                'description' => 'Nền tảng chia sẻ hình ảnh và video.',
                'status' => 'published',
                'key' => 'instagram',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Threads',
                'description' => 'Ứng dụng trò chuyện liên kết với Instagram.',
                'status' => 'published',
                'key' => 'threads',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'TikTok',
                'description' => 'Mạng xã hội video ngắn rất phổ biến.',
                'status' => 'published',
                'key' => 'tiktok',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Twitter',
                'description' => 'Nền tảng chia sẻ thông tin nhanh chóng (nay là X).',
                'status' => 'published',
                'key' => 'twitter',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'YouTube',
                'description' => 'Nền tảng chia sẻ video lớn nhất thế giới.',
                'status' => 'published',
                'key' => 'youtube',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Google',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'google',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Shopee',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'shopee',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'Spotify',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'spotify',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => 'LinkedIn',
                'description' => 'Mạng xã hội chuyên nghiệp dành cho người đi làm.',
                'status' => 'published',
                'key' => 'linkedin',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ];
        DB::table('platforms')->insert($platforms);

        $packageList = [
            [
                'name' => 'Gói 1',
                'key' => 'package-1',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        DB::table('package_lists')->insert($packageList);
        $categories = [
            [
                'platform_id' => 1,
                'name' => 'Like bài viết Facebook',
                'key' => 'like-post',
                'status' => BaseStatusEnum::PUBLISHED->value,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        ];

        DB::table('categories')->insert($categories);
    }
}
