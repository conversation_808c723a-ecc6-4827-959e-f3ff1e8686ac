<?php

namespace Database\Seeders;

use App\Enums\UserStatusEnum;
use App\Models\Tenant;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        $user = User::factory()->create([
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'username' => 'testuser',
            'password' => Hash::make('password'),
            'status'=> UserStatusEnum::ACTIVE,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        $tenant = Tenant::query()->create([
            'domain' => 'panel.test',
            'owner_id' => $user->id,
            'verified' => true,
            'is_primary'=> true
        ]);
        $tenant->users()->attach($user->id, ['role' => 'supper_admin']);

        $user2 = User::factory()->create([
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'username' => 'testuser2',
            'password' => Hash::make('password'),
            'status'=> UserStatusEnum::ACTIVE,
            'created_at' => now(),
            'updated_at' => now()
        ]);
        $tenant2 = Tenant::query()->create([
            'domain' => 'ryoma.test',
            'owner_id' => $user2->id,
            'verified' => true,
        ]);
        $tenant->users()->attach($user2->id, ['role' => 'user']);
        $tenant2->users()->attach($user2->id, ['role' => 'admin']);
        $users = User::factory(100)->create();

        foreach ($users as $user) {
            $tenant->users()->attach($user->id, ['role' => 'user']);
        }

        $this->call(SocialMediaMarketingSeeder::class);

    }
}
